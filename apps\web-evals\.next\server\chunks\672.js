"use strict";exports.id=672,exports.ids=[672],exports.modules={148:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(72364).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},60770:(e,t,r)=>{r.d(t,{EH:()=>l,_y:()=>d,a3:()=>a,vv:()=>n});let s=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}),n=e=>s.format(e),a=e=>{let t=Math.floor(e/1e3),r=Math.floor(t/3600),s=Math.floor(t%3600/60),n=t%60,a=[];return r>0&&a.push(`${r}h`),s>0&&a.push(`${s}m`),(n>0||0===a.length)&&a.push(`${n}s`),a.join(" ")},d=e=>e<1e3?e.toString():e<1e6?`${(e/1e3).toFixed(1)}k`:e<1e9?`${(e/1e6).toFixed(1)}M`:`${(e/1e9).toFixed(1)}B`,l=e=>0===e.attempts?"0%":`${((e.attempts-e.failures)/e.attempts*100).toFixed(1)}%`},97978:(e,t,r)=>{r.d(t,{Lc:()=>w,Ze:()=>$});var s={};r.r(s),r.d(s,{runs:()=>f,runsRelations:()=>k,schema:()=>A,taskMetrics:()=>h,tasks:()=>p,tasksRelations:()=>_,toolErrors:()=>N,toolErrorsRelations:()=>m});var n=r(86885),a=r(79033),d=r(84696),l=r(69711),o=r(54799),i=r(44635),c=r(30371),u=r(46299),y=r(91864);let f=(0,n.cJ)("runs",{id:(0,a.nd)().primaryKey().generatedAlwaysAsIdentity(),taskMetricsId:(0,a.nd)("task_metrics_id").references(()=>h.id),model:(0,d.Qq)().notNull(),description:(0,d.Qq)(),settings:(0,l.Fx)().$type(),pid:(0,a.nd)(),socketPath:(0,d.Qq)("socket_path").notNull(),concurrency:(0,a.nd)().default(2).notNull(),passed:(0,a.nd)().default(0).notNull(),failed:(0,a.nd)().default(0).notNull(),createdAt:(0,o.vE)("created_at").notNull()}),k=(0,y.K1)(f,({one:e})=>({taskMetrics:e(h,{fields:[f.taskMetricsId],references:[h.id]})})),p=(0,n.cJ)("tasks",{id:(0,a.nd)().primaryKey().generatedAlwaysAsIdentity(),runId:(0,a.nd)("run_id").references(()=>f.id).notNull(),taskMetricsId:(0,a.nd)("task_metrics_id").references(()=>h.id),language:(0,d.Qq)().notNull().$type(),exercise:(0,d.Qq)().notNull(),passed:(0,i.zM)(),startedAt:(0,o.vE)("started_at"),finishedAt:(0,o.vE)("finished_at"),createdAt:(0,o.vE)("created_at").notNull()},e=>[(0,c.GL)("tasks_language_exercise_idx").on(e.runId,e.language,e.exercise)]),_=(0,y.K1)(p,({one:e})=>({run:e(f,{fields:[p.runId],references:[f.id]}),taskMetrics:e(h,{fields:[p.taskMetricsId],references:[h.id]})})),h=(0,n.cJ)("taskMetrics",{id:(0,a.nd)().primaryKey().generatedAlwaysAsIdentity(),tokensIn:(0,a.nd)("tokens_in").notNull(),tokensOut:(0,a.nd)("tokens_out").notNull(),tokensContext:(0,a.nd)("tokens_context").notNull(),cacheWrites:(0,a.nd)("cache_writes").notNull(),cacheReads:(0,a.nd)("cache_reads").notNull(),cost:(0,u.x)().notNull(),duration:(0,a.nd)().notNull(),toolUsage:(0,l.Fx)("tool_usage").$type(),createdAt:(0,o.vE)("created_at").notNull()}),N=(0,n.cJ)("toolErrors",{id:(0,a.nd)().primaryKey().generatedAlwaysAsIdentity(),runId:(0,a.nd)("run_id").references(()=>f.id),taskId:(0,a.nd)("task_id").references(()=>p.id),toolName:(0,d.Qq)("tool_name").notNull().$type(),error:(0,d.Qq)().notNull(),createdAt:(0,o.vE)("created_at").notNull()}),m=(0,y.K1)(N,({one:e})=>({run:e(f,{fields:[N.runId],references:[f.id]}),task:e(p,{fields:[N.taskId],references:[p.id]})})),A={runs:f,runsRelations:k,tasks:p,tasksRelations:_,taskMetrics:h,toolErrors:N,toolErrorsRelations:m};var x=r(55920),v=r(9102);class I extends Error{}var M=r(64709);let g=(0,r(40078).A)(process.env.DATABASE_URL,{prepare:!1}),E=(0,M.f)({client:g,schema:s}),w=async e=>{let t=await E.query.runs.findFirst({where:(0,x.eq)(A.runs.id,e)});if(!t)throw new I;return t},$=async()=>E.query.runs.findMany({orderBy:(0,v.i)(A.runs.id),with:{taskMetrics:!0}});var q=r(33873);r(79748);var F=r(79551);let K=q.dirname((0,F.fileURLToPath)("file:///D:/Documents/Python/Roo-Code/packages/evals/src/exercises/index.ts"));q.resolve(K,"..","..","..","..","..","evals")}};