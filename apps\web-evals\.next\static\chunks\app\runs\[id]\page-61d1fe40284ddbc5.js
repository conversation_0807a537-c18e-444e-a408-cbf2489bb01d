(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[293],{1718:(e,t,s)=>{"use strict";s.d(t,{EH:()=>i,_y:()=>c,a3:()=>a,vv:()=>r});let n=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}),r=e=>n.format(e),a=e=>{let t=Math.floor(e/1e3),s=Math.floor(t/3600),n=Math.floor(t%3600/60),r=t%60,a=[];return s>0&&a.push("".concat(s,"h")),n>0&&a.push("".concat(n,"m")),(r>0||0===a.length)&&a.push("".concat(r,"s")),a.join(" ")},c=e=>e<1e3?e.toString():e<1e6?"".concat((e/1e3).toFixed(1),"k"):e<1e9?"".concat((e/1e6).toFixed(1),"M"):"".concat((e/1e9).toFixed(1),"B"),i=e=>0===e.attempts?"0%":"".concat(((e.attempts-e.failures)/e.attempts*100).toFixed(1),"%")},3354:(e,t,s)=>{Promise.resolve().then(s.bind(s,7474))},6598:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});let n=(0,s(436).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7474:(e,t,s)=>{"use strict";s.d(t,{Run:()=>b});var n=s(7093),r=s(4545),a=s(6598),c=s(1718),i=s(8590),l=s(8691),d=s(8511),o=s(3401);let u=(0,o.createServerReference)("7f6d1b280983107141b5c621ad4d82a5d8407f40d6",o.callServer,void 0,o.findSourceMapURL,"getHeartbeat"),x=(0,o.createServerReference)("7f7476cec0425ddb52aae5841eb004fe5a76157c8f",o.callServer,void 0,o.findSourceMapURL,"getRunners"),m=(0,o.createServerReference)("40fdc5a8a07cf91c07d59f79548d9deae836365c6e",o.callServer,void 0,o.findSourceMapURL,"getTasks"),h=e=>{let[t,s]=(0,r.useState)(),[n,a]=(0,r.useState)(),c=(0,r.useRef)(new Map),o=(0,r.useRef)(new Map),{data:h}=(0,i.I)({queryKey:["getHeartbeat",e.id],queryFn:()=>u(e.id),refetchInterval:1e4}),{data:f}=(0,i.I)({queryKey:["getRunners",e.id],queryFn:()=>x(e.id),refetchInterval:1e4}),{data:v}=(0,i.I)({queryKey:["getTasks",e.id,t],queryFn:async()=>m(e.id),placeholderData:l.rX,refetchInterval:3e4});return{sseStatus:function(e){let{url:t,withCredentials:s,onMessage:n}=e,a=(0,r.useRef)(null),c=(0,r.useRef)("waiting"),[i,l]=(0,r.useState)("waiting"),d=(0,r.useRef)(null),o=(0,r.useRef)(!1),u=(0,r.useCallback)(e=>n(e),[n]),x=(0,r.useCallback)(()=>{d.current&&(clearTimeout(d.current),d.current=null),a.current&&(a.current.close(),a.current=null)},[]),m=(0,r.useCallback)(()=>{o.current||(x(),c.current="waiting",l("waiting"),a.current=new EventSource(t,{withCredentials:s}),a.current.onopen=()=>{o.current||(c.current="connected",l("connected"))},a.current.onmessage=e=>{o.current||u(e)},a.current.onerror=()=>{o.current||(c.current="error",l("error"),x(),d.current=setTimeout(()=>{o.current||m()},1e3))})},[t,s,u,x]);return(0,r.useEffect)(()=>{o.current=!1,m();let e=setTimeout(()=>{"waiting"!==c.current||o.current||m()},5e3);return()=>{o.current=!0,clearTimeout(e),x()}},[m,x]),i}({url:"/api/runs/".concat(e.id,"/stream"),onMessage:(0,r.useCallback)(e=>{let t;try{t=JSON.parse(e.data)}catch(t){console.log("invalid JSON: ".concat(e.data));return}let n=d.Hk.safeParse(t);if(!n.success){console.log("unrecognized messageEvent.data: ".concat(e.data));return}let{eventName:r,payload:i,taskId:l}=n.data;if(!l){console.log("no taskId: ".concat(e.data));return}switch(r){case d.Pj.TaskStarted:o.current.set(l,Date.now());break;case d.Pj.TaskTokenUsageUpdated:{let e=o.current.get(l),t=e?Date.now()-e:void 0;c.current.set(l,{...i[1],duration:t}),a(Date.now());break}case d.Pj.EvalPass:case d.Pj.EvalFail:s(Date.now())}},[])}),heartbeat:h,runners:f,tasks:v,tokenUsage:c.current,usageUpdatedAt:n}};var f=s(4548),v=s(436);let j=(0,v.A)("circle-slash",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"9",x2:"15",y1:"15",y2:"9",key:"1dfufj"}]]);var g=s(5745);let k=(0,v.A)("circle-dashed",[["path",{d:"M10.1 2.182a10 10 0 0 1 3.8 0",key:"5ilxe3"}],["path",{d:"M13.9 21.818a10 10 0 0 1-3.8 0",key:"11zvb9"}],["path",{d:"M17.609 3.721a10 10 0 0 1 2.69 2.7",key:"1iw5b2"}],["path",{d:"M2.182 13.9a10 10 0 0 1 0-3.8",key:"c0bmvh"}],["path",{d:"M20.279 17.609a10 10 0 0 1-2.7 2.69",key:"1ruxm7"}],["path",{d:"M21.818 10.1a10 10 0 0 1 0 3.8",key:"qkgqxc"}],["path",{d:"M3.721 6.391a10 10 0 0 1 2.7-2.69",key:"1mcia2"}],["path",{d:"M6.391 20.279a10 10 0 0 1-2.69-2.7",key:"1fvljs"}]]),p=e=>{let{task:t,running:s}=e;return!1===t.passed?(0,n.jsx)(j,{className:"size-4 text-destructive"}):!0===t.passed?(0,n.jsx)(g.A,{className:"size-4 text-green-500"}):s?(0,n.jsx)(a.A,{className:"size-4 animate-spin"}):(0,n.jsx)(k,{className:"size-4"})};var y=s(8322);let N=e=>{let{runStatus:{sseStatus:t,heartbeat:s,runners:r=[]}}=e;return(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{children:"Task Stream:"}),(0,n.jsx)("div",{className:"font-mono text-sm text-muted-foreground",children:t})]}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:(0,y.cn)("absolute size-2.5 rounded-full opacity-50 animate-ping",{"bg-green-500":"connected"===t,"bg-amber-500":"waiting"===t,"bg-rose-500":"error"===t})}),(0,n.jsx)("div",{className:(0,y.cn)("size-2.5 rounded-full",{"bg-green-500":"connected"===t,"bg-amber-500":"waiting"===t,"bg-rose-500":"error"===t})})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{children:"Task Controller:"}),(0,n.jsx)("div",{className:"font-mono text-sm text-muted-foreground",children:null!=s?s:"dead"})]}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:(0,y.cn)("absolute size-2.5 rounded-full opacity-50 animate-ping",{"bg-green-500":!!s,"bg-rose-500":!s})}),(0,n.jsx)("div",{className:(0,y.cn)("size-2.5 rounded-full",{"bg-green-500":!!s,"bg-rose-500":!s})})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{children:"Task Runners:"}),r.length>0&&(0,n.jsx)("div",{className:"font-mono text-sm text-muted-foreground",children:null==r?void 0:r.join(", ")})]})]})};function b(e){let{run:t}=e,s=h(t),{tasks:i,tokenUsage:l,usageUpdatedAt:d}=s,o=(0,r.useMemo)(()=>{let e={};return null==i||i.forEach(t=>{let s=l.get(t.id);if(t.finishedAt&&t.taskMetrics)e[t.id]=t.taskMetrics;else if(s){var n;e[t.id]={tokensIn:s.totalTokensIn,tokensOut:s.totalTokensOut,tokensContext:s.contextTokens,duration:null!==(n=s.duration)&&void 0!==n?n:0,cost:s.totalCost}}}),e},[i,l,d]);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"mb-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-mono",children:t.model}),t.description&&(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:t.description})]}),!t.taskMetricsId&&(0,n.jsx)(N,{runStatus:s})]}),i?(0,n.jsxs)(f.XI,{className:"border",children:[(0,n.jsx)(f.A0,{children:(0,n.jsxs)(f.Hj,{children:[(0,n.jsx)(f.nd,{children:"Exercise"}),(0,n.jsx)(f.nd,{className:"text-center",children:"Tokens In / Out"}),(0,n.jsx)(f.nd,{children:"Context"}),(0,n.jsx)(f.nd,{children:"Duration"}),(0,n.jsx)(f.nd,{children:"Cost"})]})}),(0,n.jsx)(f.BF,{children:i.map(e=>(0,n.jsxs)(f.Hj,{children:[(0,n.jsx)(f.nA,{children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(p,{task:e,running:!!e.startedAt||!!l.get(e.id)}),(0,n.jsxs)("div",{children:[e.language,"/",e.exercise]})]})}),o[e.id]?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(f.nA,{className:"font-mono text-xs",children:(0,n.jsxs)("div",{className:"flex items-center justify-evenly",children:[(0,n.jsx)("div",{children:(0,c._y)(o[e.id].tokensIn)}),"/",(0,n.jsx)("div",{children:(0,c._y)(o[e.id].tokensOut)})]})}),(0,n.jsx)(f.nA,{className:"font-mono text-xs",children:(0,c._y)(o[e.id].tokensContext)}),(0,n.jsx)(f.nA,{className:"font-mono text-xs",children:o[e.id].duration?(0,c.a3)(o[e.id].duration):"-"}),(0,n.jsx)(f.nA,{className:"font-mono text-xs",children:(0,c.vv)(o[e.id].cost)})]}):(0,n.jsx)(f.nA,{colSpan:4})]},e.id))})]}):(0,n.jsx)(a.A,{className:"size-4 animate-spin"})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[409,221,581,777,370,335,550,358],()=>t(3354)),_N_E=e.O()}]);