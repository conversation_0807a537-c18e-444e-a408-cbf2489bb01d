(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[287],{795:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var s in e)Object.defineProperty(t,s,{enumerable:!0,get:e[s]})}(e,{bindSnapshot:function(){return n},createAsyncLocalStorage:function(){return a},createSnapshot:function(){return o}});let s=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw s}getStore(){}run(){throw s}exit(){throw s}enterWith(){throw s}static bind(t){return t}}let r="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return r?new r:new i}function n(t){return r?r.bind(t):i.bind(t)}function o(){return r?r.snapshot():function(t,...e){return t(...e)}}},825:(t,e,s)=>{"use strict";s.d(e,{$:()=>o,s:()=>n});var i=s(7554),r=s(8939),a=s(8587),n=class extends r.k{#t;#e;#s;constructor(t){super(),this.mutationId=t.mutationId,this.#e=t.mutationCache,this.#t=[],this.state=t.state||o(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#t.includes(t)||(this.#t.push(t),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#t=this.#t.filter(e=>e!==t),this.scheduleGc(),this.#e.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#t.length||("pending"===this.state.status?this.scheduleGc():this.#e.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#i({type:"continue"})};this.#s=(0,a.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#i({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#i({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#e.canRun(this)});let s="pending"===this.state.status,i=!this.#s.canStart();try{if(s)e();else{this.#i({type:"pending",variables:t,isPaused:i}),await this.#e.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#i({type:"pending",context:e,variables:t,isPaused:i})}let r=await this.#s.start();return await this.#e.config.onSuccess?.(r,t,this.state.context,this),await this.options.onSuccess?.(r,t,this.state.context),await this.#e.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,t,this.state.context),this.#i({type:"success",data:r}),r}catch(e){try{throw await this.#e.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#e.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#i({type:"error",error:e})}}finally{this.#e.runNext(this)}}#i(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),i.jG.batch(()=>{this.#t.forEach(e=>{e.onMutationUpdate(t)}),this.#e.notify({mutation:this,type:"updated",action:t})})}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},1037:t=>{t.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},2134:(t,e,s)=>{"use strict";s.d(e,{X:()=>o,k:()=>u});var i=s(8691),r=s(7554),a=s(8587),n=s(8939),o=class extends n.k{#r;#a;#n;#o;#s;#u;#h;constructor(t){super(),this.#h=!1,this.#u=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#o=t.client,this.#n=this.#o.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#r=function(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#r,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#s?.promise}setOptions(t){this.options={...this.#u,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#n.remove(this)}setData(t,e){let s=(0,i.pl)(this.state.data,t,this.options);return this.#i({data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){this.#i({type:"setState",state:t,setStateOptions:e})}cancel(t){let e=this.#s?.promise;return this.#s?.cancel(t),e?e.then(i.lQ).catch(i.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#r)}isActive(){return this.observers.some(t=>!1!==(0,i.Eh)(t.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===i.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(t=0){return this.state.isInvalidated||void 0===this.state.data||!(0,i.j3)(this.state.dataUpdatedAt,t)}onFocus(){let t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#s?.continue()}onOnline(){let t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#s?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#s&&(this.#h?this.#s.cancel({revert:!0}):this.#s.cancelRetry()),this.scheduleGc()),this.#n.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#i({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#s)return this.#s.continueRetry(),this.#s.promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let s=new AbortController,r=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#h=!0,s.signal)})},n={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#o,state:this.state,fetchFn:()=>{let t=(0,i.ZM)(this.options,e),s={client:this.#o,queryKey:this.queryKey,meta:this.meta};return(r(s),this.#h=!1,this.options.persister)?this.options.persister(t,s,this):t(s)}};r(n),this.options.behavior?.onFetch(n,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==n.fetchOptions?.meta)&&this.#i({type:"fetch",meta:n.fetchOptions?.meta});let o=t=>{(0,a.wm)(t)&&t.silent||this.#i({type:"error",error:t}),(0,a.wm)(t)||(this.#n.config.onError?.(t,this),this.#n.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#s=(0,a.II)({initialPromise:e?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{if(void 0===t){o(Error(`${this.queryHash} data is undefined`));return}try{this.setData(t)}catch(t){o(t);return}this.#n.config.onSuccess?.(t,this),this.#n.config.onSettled?.(t,this.state.error,this),this.scheduleGc()},onError:o,onFail:(t,e)=>{this.#i({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#i({type:"pause"})},onContinue:()=>{this.#i({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#s.start()}#i(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...u(e.data,this.options),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let s=t.error;if((0,a.wm)(s)&&s.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...e,error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),r.jG.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#n.notify({query:this,type:"updated",action:t})})}};function u(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,a.v_)(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}},2318:(t,e,s)=>{"use strict";function i(t){let{reason:e,children:s}=t;return s}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"BailoutToCSR",{enumerable:!0,get:function(){return i}}),s(9546)},2471:t=>{t.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},3158:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"workAsyncStorageInstance",{enumerable:!0,get:function(){return i}});let i=(0,s(795).createAsyncLocalStorage)()},3281:(t,e,s)=>{"use strict";s.d(e,{A:()=>n});var i=s(4545),r=function(){},a=i.useState;let n=function(t){var e,s,n=a(!1),o=n[0],u=n[1];return"function"==typeof t&&(t=t(o)),[i.cloneElement(t,{onMouseEnter:(e=t.props.onMouseEnter,function(t){(e||r)(t),u(!0)}),onMouseLeave:(s=t.props.onMouseLeave,function(t){(s||r)(t),u(!1)})}),o]}},4324:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"workAsyncStorage",{enumerable:!0,get:function(){return i.workAsyncStorageInstance}});let i=s(3158)},4611:(t,e,s)=>{"use strict";function i(t){let{moduleIds:e}=t;return null}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"PreloadChunks",{enumerable:!0,get:function(){return i}}),s(7093),s(1076),s(4324),s(6833)},7560:(t,e,s)=>{"use strict";s.d(e,{default:()=>r.a});var i=s(8049),r=s.n(i)},7617:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return u}});let i=s(7093),r=s(4545),a=s(2318);function n(t){return{default:t&&"default"in t?t.default:t}}s(4611);let o={loader:()=>Promise.resolve(n(()=>null)),loading:null,ssr:!0},u=function(t){let e={...o,...t},s=(0,r.lazy)(()=>e.loader().then(n)),u=e.loading;function h(t){let n=u?(0,i.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,o=!e.ssr||!!e.loading,h=o?r.Suspense:r.Fragment,l=e.ssr?(0,i.jsxs)(i.Fragment,{children:[null,(0,i.jsx)(s,{...t})]}):(0,i.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,i.jsx)(s,{...t})});return(0,i.jsx)(h,{...o?{fallback:n}:{},children:l})}return h.displayName="LoadableComponent",h}},8049:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return r}});let i=s(5932)._(s(7617));function r(t,e){var s;let r={};"function"==typeof t&&(r.loader=t);let a={...r,...e};return(0,i.default)({...a,modules:null==(s=a.loadableGenerated)?void 0:s.modules})}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},8923:(t,e,s)=>{"use strict";var i=s(2675);s.o(i,"useRouter")&&s.d(e,{useRouter:function(){return i.useRouter}})},9972:(t,e,s)=>{"use strict";s.d(e,{E:()=>y});var i=s(8691),r=s(2134),a=s(7554),n=s(667),o=class extends n.Q{constructor(t={}){super(),this.config=t,this.#l=new Map}#l;build(t,e,s){let a=e.queryKey,n=e.queryHash??(0,i.F$)(a,e),o=this.get(n);return o||(o=new r.X({client:t,queryKey:a,queryHash:n,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(a)}),this.add(o)),o}add(t){this.#l.has(t.queryHash)||(this.#l.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){let e=this.#l.get(t.queryHash);e&&(t.destroy(),e===t&&this.#l.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){a.jG.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#l.get(t)}getAll(){return[...this.#l.values()]}find(t){let e={exact:!0,...t};return this.getAll().find(t=>(0,i.MK)(e,t))}findAll(t={}){let e=this.getAll();return Object.keys(t).length>0?e.filter(e=>(0,i.MK)(t,e)):e}notify(t){a.jG.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){a.jG.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){a.jG.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},u=s(825),h=class extends n.Q{constructor(t={}){super(),this.config=t,this.#c=new Set,this.#d=new Map,this.#f=0}#c;#d;#f;build(t,e,s){let i=new u.s({mutationCache:this,mutationId:++this.#f,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){this.#c.add(t);let e=l(t);if("string"==typeof e){let s=this.#d.get(e);s?s.push(t):this.#d.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#c.delete(t)){let e=l(t);if("string"==typeof e){let s=this.#d.get(e);if(s)if(s.length>1){let e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&this.#d.delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){let e=l(t);if("string"!=typeof e)return!0;{let s=this.#d.get(e),i=s?.find(t=>"pending"===t.state.status);return!i||i===t}}runNext(t){let e=l(t);if("string"!=typeof e)return Promise.resolve();{let s=this.#d.get(e)?.find(e=>e!==t&&e.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){a.jG.batch(()=>{this.#c.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#c.clear(),this.#d.clear()})}getAll(){return Array.from(this.#c)}find(t){let e={exact:!0,...t};return this.getAll().find(t=>(0,i.nJ)(e,t))}findAll(t={}){return this.getAll().filter(e=>(0,i.nJ)(t,e))}notify(t){a.jG.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){let t=this.getAll().filter(t=>t.state.isPaused);return a.jG.batch(()=>Promise.all(t.map(t=>t.continue().catch(i.lQ))))}};function l(t){return t.options.scope?.id}var c=s(7569),d=s(6920);function f(t){return{onFetch:(e,s)=>{let r=e.options,a=e.fetchOptions?.meta?.fetchMore?.direction,n=e.state.data?.pages||[],o=e.state.data?.pageParams||[],u={pages:[],pageParams:[]},h=0,l=async()=>{let s=!1,l=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",()=>{s=!0}),e.signal)})},c=(0,i.ZM)(e.options,e.fetchOptions),d=async(t,r,a)=>{if(s)return Promise.reject();if(null==r&&t.pages.length)return Promise.resolve(t);let n={client:e.client,queryKey:e.queryKey,pageParam:r,direction:a?"backward":"forward",meta:e.options.meta};l(n);let o=await c(n),{maxPages:u}=e.options,h=a?i.ZZ:i.y9;return{pages:h(t.pages,o,u),pageParams:h(t.pageParams,r,u)}};if(a&&n.length){let t="backward"===a,e={pages:n,pageParams:o},s=(t?function(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}:p)(r,e);u=await d(e,s,t)}else{let e=t??n.length;do{let t=0===h?o[0]??r.initialPageParam:p(r,u);if(h>0&&null==t)break;u=await d(u,t),h++}while(h<e)}return u};e.options.persister?e.fetchFn=()=>e.options.persister?.(l,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=l}}}function p(t,{pages:e,pageParams:s}){let i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}var y=class{#p;#e;#u;#y;#m;#b;#v;#g;constructor(t={}){this.#p=t.queryCache||new o,this.#e=t.mutationCache||new h,this.#u=t.defaultOptions||{},this.#y=new Map,this.#m=new Map,this.#b=0}mount(){this.#b++,1===this.#b&&(this.#v=c.m.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#p.onFocus())}),this.#g=d.t.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#p.onOnline())}))}unmount(){this.#b--,0===this.#b&&(this.#v?.(),this.#v=void 0,this.#g?.(),this.#g=void 0)}isFetching(t){return this.#p.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#e.findAll({...t,status:"pending"}).length}getQueryData(t){let e=this.defaultQueryOptions({queryKey:t});return this.#p.get(e.queryHash)?.state.data}ensureQueryData(t){let e=this.defaultQueryOptions(t),s=this.#p.build(this,e),r=s.state.data;return void 0===r?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime((0,i.d2)(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(r))}getQueriesData(t){return this.#p.findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,s){let r=this.defaultQueryOptions({queryKey:t}),a=this.#p.get(r.queryHash),n=a?.state.data,o=(0,i.Zw)(e,n);if(void 0!==o)return this.#p.build(this,r).setData(o,{...s,manual:!0})}setQueriesData(t,e,s){return a.jG.batch(()=>this.#p.findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,s)]))}getQueryState(t){let e=this.defaultQueryOptions({queryKey:t});return this.#p.get(e.queryHash)?.state}removeQueries(t){let e=this.#p;a.jG.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){let s=this.#p;return a.jG.batch(()=>(s.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){let s={revert:!0,...e};return Promise.all(a.jG.batch(()=>this.#p.findAll(t).map(t=>t.cancel(s)))).then(i.lQ).catch(i.lQ)}invalidateQueries(t,e={}){return a.jG.batch(()=>(this.#p.findAll(t).forEach(t=>{t.invalidate()}),t?.refetchType==="none")?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e))}refetchQueries(t,e={}){let s={...e,cancelRefetch:e.cancelRefetch??!0};return Promise.all(a.jG.batch(()=>this.#p.findAll(t).filter(t=>!t.isDisabled()).map(t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(i.lQ)),"paused"===t.state.fetchStatus?Promise.resolve():e}))).then(i.lQ)}fetchQuery(t){let e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);let s=this.#p.build(this,e);return s.isStaleByTime((0,i.d2)(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(i.lQ).catch(i.lQ)}fetchInfiniteQuery(t){return t.behavior=f(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(i.lQ).catch(i.lQ)}ensureInfiniteQueryData(t){return t.behavior=f(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return d.t.isOnline()?this.#e.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#p}getMutationCache(){return this.#e}getDefaultOptions(){return this.#u}setDefaultOptions(t){this.#u=t}setQueryDefaults(t,e){this.#y.set((0,i.EN)(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){let e=[...this.#y.values()],s={};return e.forEach(e=>{(0,i.Cp)(t,e.queryKey)&&Object.assign(s,e.defaultOptions)}),s}setMutationDefaults(t,e){this.#m.set((0,i.EN)(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){let e=[...this.#m.values()],s={};return e.forEach(e=>{(0,i.Cp)(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;let e={...this.#u.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=(0,i.F$)(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===i.hT&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#u.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#p.clear(),this.#e.clear()}}}}]);