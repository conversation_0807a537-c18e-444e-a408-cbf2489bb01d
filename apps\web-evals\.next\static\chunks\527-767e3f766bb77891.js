"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[527],{219:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return g},MissingStaticPage:function(){return b},NormalizeError:function(){return y},PageNotFoundError:function(){return m},SP:function(){return f},ST:function(){return d},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return l},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(t){let e,r=!1;return function(){for(var n=arguments.length,s=Array(n),i=0;i<n;i++)s[i]=arguments[i];return r||(r=!0,e=t(...s)),e}}let s=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=t=>s.test(t);function o(){let{protocol:t,hostname:e,port:r}=window.location;return t+"//"+e+(r?":"+r:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function u(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function c(t){return t.finished||t.headersSent}function l(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function h(t,e){let r=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await h(e.Component,e.ctx)}:{};let n=await t.getInitialProps(e);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+u(t)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,d=f&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class p extends Error{}class y extends Error{}class m extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class b extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class g extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(t){return JSON.stringify({message:t.message,stack:t.stack})}},703:(t,e)=>{function r(t){let e={};for(let[r,n]of t.entries()){let t=e[r];void 0===t?e[r]=n:Array.isArray(t)?t.push(n):e[r]=[t,n]}return e}function n(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function s(t){let e=new URLSearchParams;for(let[r,s]of Object.entries(t))if(Array.isArray(s))for(let t of s)e.append(r,n(t));else e.set(r,n(s));return e}function i(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];for(let e of r){for(let r of e.keys())t.delete(r);for(let[r,n]of e.entries())t.append(r,n)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return s}})},825:(t,e,r)=>{r.d(e,{$:()=>a,s:()=>o});var n=r(7554),s=r(8939),i=r(8587),o=class extends s.k{#t;#e;#r;constructor(t){super(),this.mutationId=t.mutationId,this.#e=t.mutationCache,this.#t=[],this.state=t.state||a(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#t.includes(t)||(this.#t.push(t),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#t=this.#t.filter(e=>e!==t),this.scheduleGc(),this.#e.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#t.length||("pending"===this.state.status?this.scheduleGc():this.#e.remove(this))}continue(){return this.#r?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#n({type:"continue"})};this.#r=(0,i.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#n({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#n({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#e.canRun(this)});let r="pending"===this.state.status,n=!this.#r.canStart();try{if(r)e();else{this.#n({type:"pending",variables:t,isPaused:n}),await this.#e.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#n({type:"pending",context:e,variables:t,isPaused:n})}let s=await this.#r.start();return await this.#e.config.onSuccess?.(s,t,this.state.context,this),await this.options.onSuccess?.(s,t,this.state.context),await this.#e.config.onSettled?.(s,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(s,null,t,this.state.context),this.#n({type:"success",data:s}),s}catch(e){try{throw await this.#e.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#e.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#n({type:"error",error:e})}}finally{this.#e.runNext(this)}}#n(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),n.jG.batch(()=>{this.#t.forEach(e=>{e.onMutationUpdate(t)}),this.#e.notify({mutation:this,type:"updated",action:t})})}};function a(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},925:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(436).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},2762:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return s}});let n=r(4545);function s(t,e){let r=(0,n.useRef)(null),s=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let t=r.current;t&&(r.current=null,t());let e=s.current;e&&(s.current=null,e())}else t&&(r.current=i(t,n)),e&&(s.current=i(e,n))},[t,e])}function i(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let r=t(e);return"function"==typeof r?r:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},4214:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return p}});let n=r(5932),s=r(7093),i=n._(r(4545)),o=r(9118),a=r(2679),u=r(7934),c=r(2762),l=r(219),h=r(8005);r(2810);let f=r(3142);function d(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}let p=i.default.forwardRef(function(t,e){let r,n,{href:o,as:p,children:y,prefetch:m=null,passHref:b,replace:g,shallow:v,scroll:M,onClick:C,onMouseEnter:O,onTouchStart:x,legacyBehavior:E=!1,...P}=t;r=y,E&&("string"==typeof r||"number"==typeof r)&&(r=(0,s.jsx)("a",{children:r}));let k=i.default.useContext(a.AppRouterContext),R=!1!==m,w=null===m?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:j,as:A}=i.default.useMemo(()=>{let t=d(o);return{href:t,as:p?d(p):t}},[o,p]);E&&(n=i.default.Children.only(r));let S=E?n&&"object"==typeof n&&n.ref:e,_=i.default.useCallback(t=>(R&&null!==k&&(0,f.mountLinkInstance)(t,j,k,w),()=>{(0,f.unmountLinkInstance)(t)}),[R,j,k,w]),N={ref:(0,c.useMergedRef)(_,S),onClick(t){E||"function"!=typeof C||C(t),E&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(t),k&&(t.defaultPrevented||function(t,e,r,n,s,o,a){let{nodeName:u}=t.currentTarget;!("A"===u.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t))&&(t.preventDefault(),i.default.startTransition(()=>{let t=null==a||a;"beforePopState"in e?e[s?"replace":"push"](r,n,{shallow:o,scroll:t}):e[s?"replace":"push"](n||r,{scroll:t})}))}(t,k,j,A,g,v,M))},onMouseEnter(t){E||"function"!=typeof O||O(t),E&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(t),k&&R&&(0,f.onNavigationIntent)(t.currentTarget)},onTouchStart:function(t){E||"function"!=typeof x||x(t),E&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(t),k&&R&&(0,f.onNavigationIntent)(t.currentTarget)}};return(0,l.isAbsoluteUrl)(A)?N.href=A:E&&!b&&("a"!==n.type||"href"in n.props)||(N.href=(0,h.addBasePath)(A)),E?i.default.cloneElement(n,N):(0,s.jsx)("a",{...P,...N,children:r})});("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6025:(t,e,r)=>{r.d(e,{n:()=>l});var n=r(4545),s=r(825),i=r(7554),o=r(667),a=r(8691),u=class extends o.Q{#s;#i=void 0;#o;#a;constructor(t,e){super(),this.#s=t,this.setOptions(e),this.bindMethods(),this.#u()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#s.defaultMutationOptions(t),(0,a.f8)(this.options,e)||this.#s.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#o,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,a.EN)(e.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#o?.state.status==="pending"&&this.#o.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#o?.removeObserver(this)}onMutationUpdate(t){this.#u(),this.#c(t)}getCurrentResult(){return this.#i}reset(){this.#o?.removeObserver(this),this.#o=void 0,this.#u(),this.#c()}mutate(t,e){return this.#a=e,this.#o?.removeObserver(this),this.#o=this.#s.getMutationCache().build(this.#s,this.options),this.#o.addObserver(this),this.#o.execute(t)}#u(){let t=this.#o?.state??(0,s.$)();this.#i={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#c(t){i.jG.batch(()=>{if(this.#a&&this.hasListeners()){let e=this.#i.variables,r=this.#i.context;t?.type==="success"?(this.#a.onSuccess?.(t.data,e,r),this.#a.onSettled?.(t.data,null,e,r)):t?.type==="error"&&(this.#a.onError?.(t.error,e,r),this.#a.onSettled?.(void 0,t.error,e,r))}this.listeners.forEach(t=>{t(this.#i)})})}},c=r(6890);function l(t,e){let r=(0,c.jE)(e),[s]=n.useState(()=>new u(r,t));n.useEffect(()=>{s.setOptions(t)},[s,t]);let o=n.useSyncExternalStore(n.useCallback(t=>s.subscribe(i.jG.batchCalls(t)),[s]),()=>s.getCurrentResult(),()=>s.getCurrentResult()),l=n.useCallback((t,e)=>{s.mutate(t,e).catch(a.lQ)},[s]);if(o.error&&(0,a.GU)(s.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:l,mutateAsync:o.mutate}}},6147:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(436).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},6598:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(436).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8737:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(436).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},8923:(t,e,r)=>{var n=r(2675);r.o(n,"useRouter")&&r.d(e,{useRouter:function(){return n.useRouter}})},8936:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(436).A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},9042:(t,e,r)=>{r.d(e,{A:()=>n});let n=(0,r(436).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},9118:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{formatUrl:function(){return i},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let n=r(3721)._(r(703)),s=/https?|ftp|gopher|file/;function i(t){let{auth:e,hostname:r}=t,i=t.protocol||"",o=t.pathname||"",a=t.hash||"",u=t.query||"",c=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?c=e+t.host:r&&(c=e+(~r.indexOf(":")?"["+r+"]":r),t.port&&(c+=":"+t.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let l=t.search||u&&"?"+u||"";return i&&!i.endsWith(":")&&(i+=":"),t.slashes||(!i||s.test(i))&&!1!==c?(c="//"+(c||""),o&&"/"!==o[0]&&(o="/"+o)):c||(c=""),a&&"#"!==a[0]&&(a="#"+a),l&&"?"!==l[0]&&(l="?"+l),""+i+c+(o=o.replace(/[?#]/g,encodeURIComponent))+(l=l.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return i(t)}}}]);