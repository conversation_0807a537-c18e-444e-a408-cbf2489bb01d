{"type-group": {"modes": "<PERSON><PERSON><PERSON>", "mcps": "MCP Sunucuları", "match": "<PERSON><PERSON><PERSON>ş<PERSON>"}, "item-card": {"type-mode": "Mod", "type-mcp": "MCP Sunucusu", "type-other": "<PERSON><PERSON><PERSON>", "by-author": "{{author}} ta<PERSON><PERSON><PERSON><PERSON>n", "authors-profile": "Ya<PERSON>ın <PERSON>", "remove-tag-filter": "Etiket filtresini kaldır: {{tag}}", "filter-by-tag": "Etikete göre filtrele: {{tag}}", "component-details": "Bileşen Detayları", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "<PERSON><PERSON><PERSON>"}, "filters": {"search": {"placeholder": "Marketplace'te ara..."}, "type": {"label": "<PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "mode": "Mod", "mcpServer": "MCP Sunucusu"}, "sort": {"label": "S<PERSON>rala", "name": "İsim", "lastUpdated": "<PERSON>"}, "tags": {"label": "<PERSON><PERSON><PERSON><PERSON>", "clear": "Etiketleri temizle", "placeholder": "Etiket ara...", "noResults": "Etiket bulunamadı.", "selected": "Seçilen etiketlerden herhangi birine sahip öğeleri gö<PERSON>"}, "title": "Marketplace"}, "done": "<PERSON><PERSON>", "tabs": {"installed": "Yüklü", "browse": "<PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>"}, "items": {"empty": {"noItems": "Marketplace öğesi bulunamadı.", "emptyHint": "Filtrelerinizi veya arama terimlerinizi ayarlamayı deneyin"}}, "installation": {"installing": "<PERSON><PERSON><PERSON>: \"{{itemName}}\"", "installSuccess": "\"{{itemName}}\" ba<PERSON><PERSON><PERSON><PERSON> yü<PERSON>ndi", "installError": "\"{{itemName}}\" yü<PERSON>nemedi: {{errorMessage}}", "removing": "<PERSON><PERSON><PERSON> kaldırılıyor: \"{{itemName}}\"", "removeSuccess": "\"{{itemName}}\" ba<PERSON><PERSON><PERSON><PERSON> kaldırıldı", "removeError": "\"{{itemName}}\" kaldırılamadı: {{errorMessage}}"}}