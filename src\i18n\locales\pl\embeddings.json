{"unknownError": "Nieznany błąd", "authenticationFailed": "Nie udało się utworzyć osadzeń: Uwierzytelnianie nie powiodło się. Sprawdź swój klucz API.", "failedWithStatus": "<PERSON><PERSON> udało się utworzyć osadzeń po {{attempts}} próbach: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "<PERSON><PERSON> udało się utworzyć osadzeń po {{attempts}} próbach: {{errorMessage}}", "failedMaxAttempts": "<PERSON>e udało się utworzyć osadzeń po {{attempts}} pr<PERSON><PERSON>", "textExceedsTokenLimit": "Tekst w indeksie {{index}} przekracza maksymalny limit tokenów ({{itemTokens}} > {{maxTokens}}). Pomijanie.", "rateLimitRetry": "Osiągnię<PERSON> limit s<PERSON><PERSON><PERSON><PERSON>, ponawianie za {{delayMs}}ms (próba {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Nie można odczytać treści błędu", "requestFailed": "Żądanie API Ollama nie powiodło się ze statusem {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Nieprawidłowa struktura odpowiedzi z API Ollama: tablica \"embeddings\" nie została znaleziona lub nie jest tablicą.", "embeddingFailed": "Osadzenie Ollama nie powiodło się: {{message}}"}, "scanner": {"unknownErrorProcessingFile": "Nieznany błąd podczas przetwarzania pliku {{filePath}}", "unknownErrorDeletingPoints": "Nieznany błąd podczas usuwania punktów dla {{filePath}}", "failedToProcessBatchWithError": "<PERSON>e udało się przetworzyć partii po {{maxRetries}} próbach: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Nie udało się połączyć z bazą danych wektorowych Qdrant. Upew<PERSON>j się, że Qdrant jest uruchomiony i dostępny pod adresem {{qdrantUrl}}. Błąd: {{errorMessage}}"}}