# @roo-code/langfuse

A comprehensive Langfuse integration module for Roo Code that provides LLM observability, tracing, and analytics without disrupting existing functionality.

## Overview

This package integrates [Langfuse](https://langfuse.com/) - an open-source LLM observability platform - with the Roo Code extension to provide:

- **LLM Call Tracing**: Automatic tracing of all LLM API calls with request/response data
- **Conversation Tracking**: Complete conversation flows and task management
- **Tool Usage Monitoring**: Tracing of tool executions and function calls
- **Performance Analytics**: Token usage, costs, and response times
- **Debugging Support**: Capture prompts and responses for analysis
- **Privacy Compliance**: Respects user privacy settings and telemetry preferences

## Architecture

The integration follows a **non-intrusive middleware pattern** that wraps existing API handlers without modifying core functionality:

```
┌─────────────────┐    ┌──────────────────┐     ┌─────────────────┐
│   Task/Cline    │───▶│ LangfuseService  │───▶│ Langfuse Cloud  │
│   Provider      │    │                  │     │                 │
└─────────────────┘    └──────────────────┘     └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌───────────────────┐
│   API Handler   │───▶│ LangfuseMiddleware│
│   (OpenAI, etc) │    │                   │
└─────────────────┘    └───────────────────┘
```

### Key Components

- **LangfuseService**: Main service implementing TelemetryClient interface
- **LangfuseClient**: Wrapper around Langfuse SDK with error handling
- **LangfuseTracer**: Handles different types of traces (LLM, conversation, tools)
- **LangfuseMiddleware**: Non-intrusive API handler wrapper
- **LangfuseConfigManager**: VSCode settings integration

## Installation

The package is already included in the Roo Code monorepo. To enable Langfuse integration:

1. **Install dependencies** (if not already installed):
   ```bash
   pnpm install
   ```

2. **Configure Langfuse** (see Configuration section below)

3. **Initialize in extension** (see Integration Examples section below)

## Configuration

### Environment Variables (Recommended)

```bash
# Required
LANGFUSE_PUBLIC_KEY=pk_your_public_key
LANGFUSE_SECRET_KEY=sk_your_secret_key

# Optional
LANGFUSE_BASE_URL=https://your-langfuse-instance.com  # Defaults to Langfuse cloud
LANGFUSE_ENABLED=true                                 # Enable/disable tracing
LANGFUSE_MAX_CONTENT_LENGTH=128000                    # Limit content capture
```

### VSCode Settings

Add to your VSCode settings or the extension's package.json:

```json
{
  "roo-cline.langfuse.enabled": true,
  "roo-cline.langfuse.publicKey": "pk_your_public_key",
  "roo-cline.langfuse.secretKey": "sk_your_secret_key",
  "roo-cline.langfuse.baseUrl": "https://your-langfuse-instance.com",
  "roo-cline.langfuse.traceLlmCalls": true,
  "roo-cline.langfuse.traceConversations": true,
  "roo-cline.langfuse.traceToolUsage": true,
  "roo-cline.langfuse.capturePrompts": true,
  "roo-cline.langfuse.captureResponses": true,
  "roo-cline.langfuse.maxContentLength": 0,
  "roo-cline.langfuse.respectPrivacySettings": true,
  "roo-cline.langfuse.defaultTags": ["roo-code"],
  "roo-cline.langfuse.sessionTimeoutMinutes": 60
}
```

## Integration Examples

### Basic Extension Integration

```typescript
// In src/extension.ts
import { 
  LangfuseService, 
  initializeLangfuseIntegration,
  registerLangfuseWithTelemetry,
  createLangfuseConfigFromEnvironment 
} from "@roo-code/langfuse"

export async function activate(context: vscode.ExtensionContext) {
  // ... existing activation code ...

  // Initialize Langfuse integration
  const langfuseConfig = createLangfuseConfigFromEnvironment()
  const langfuseService = await initializeLangfuseIntegration(langfuseConfig)

  // Register with telemetry service
  if (langfuseService) {
    registerLangfuseWithTelemetry(telemetryService, langfuseService)
  }

  // ... rest of activation code ...
}
```

### API Handler Integration

```typescript
// In src/api/index.ts
import { wrapApiHandlerBuilder } from "@roo-code/langfuse"

// Wrap the existing buildApiHandler function
const originalBuildApiHandler = buildApiHandler
export const buildApiHandler = wrapApiHandlerBuilder(originalBuildApiHandler)
```

### Task Integration

```typescript
// In ClineProvider or Task classes
import { setupClineProviderIntegration } from "@roo-code/langfuse"

// Set up automatic task tracing
const taskIntegration = setupClineProviderIntegration(provider)
```

### Manual Tracing

```typescript
import { 
  LangfuseService,
  traceTaskStart,
  traceTaskEnd,
  traceToolUsage 
} from "@roo-code/langfuse"

// Manual task tracing
await traceTaskStart("task-123", "Initial user message")

// Manual tool tracing
await traceToolUsage("task-123", "file_reader", 
  { path: "/src/file.ts" }, 
  { content: "file contents" }
)

// Manual task completion
await traceTaskEnd("task-123", "Task completed successfully")
```

## Configuration Management

```typescript
import { 
  LangfuseConfigManager,
  createLangfuseConfigManager 
} from "@roo-code/langfuse"

// Create config manager
const configManager = createLangfuseConfigManager(context)

// Listen for config changes
configManager.onConfigChange((newConfig) => {
  console.log("Langfuse config updated:", newConfig)
})

// Update configuration
await configManager.updateConfig("maxContentLength", 5000)

// Validate configuration
const validation = configManager.validateCurrentConfig()
if (!validation.isValid) {
  console.error("Config issues:", validation.issues)
}
```

## Features

### Automatic LLM Call Tracing

All LLM API calls are automatically traced with:
- Request parameters (model, temperature, max tokens)
- Input messages and system prompts
- Response content and finish reason
- Token usage and costs
- Error handling and status

### Conversation Flow Tracking

Complete conversation flows are captured including:
- Task creation and completion
- Message exchanges between user and assistant
- Tool usage within conversations
- Session management and timeouts

### Privacy and Security

- **Privacy Compliance**: Respects VSCode telemetry settings
- **Content Filtering**: Configurable content length limits
- **Sensitive Data**: Automatic redaction of API keys and secrets
- **Error Handling**: Graceful degradation when Langfuse is unavailable

### Performance Considerations

- **Non-blocking**: Tracing happens asynchronously
- **Error Isolation**: Langfuse errors don't affect main functionality
- **Configurable**: Can be disabled or limited as needed
- **Efficient**: Minimal overhead on LLM response times

## Development

### Running Tests

```bash
cd packages/langfuse
pnpm test
```

### Type Checking

```bash
pnpm check-types
```

### Linting

```bash
pnpm lint
```

## Troubleshooting

### Common Issues

1. **Langfuse not tracing**: Check configuration and ensure keys are set
2. **Performance impact**: Reduce `maxContentLength` or disable certain trace types
3. **Privacy concerns**: Enable `respectPrivacySettings` and review captured data

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG=langfuse:*
```

### Configuration Validation

```typescript
const status = configManager.getConfigStatus()
console.log("Langfuse status:", status)
```

## License

This package is part of the Roo Code project and follows the same license terms.

## Contributing

See the main Roo Code repository for contribution guidelines.
