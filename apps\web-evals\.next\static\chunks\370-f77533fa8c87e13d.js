"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[370],{4548:(e,o,t)=>{t.d(o,{Lt:()=>a.<PERSON><PERSON>,Rx:()=>a.<PERSON>,Zr:()=>a.<PERSON>,EO:()=>a.AlertDialogContent,$v:()=>a.AlertDialogDescription,ck:()=>a.<PERSON>ogFooter,wd:()=>a.<PERSON><PERSON>og<PERSON>eader,r7:()=>a.AlertDialogTitle,$n:()=>c.$,uB:()=>u.Command,xL:()=>u.CommandEmpty,L$:()=>u.CommandGroup,G7:()=>u.CommandInput,h_:()=>u.CommandItem,oI:()=>u.CommandList,lG:()=>z.<PERSON>,Cf:()=>z.<PERSON>,Es:()=>z.<PERSON>oot<PERSON>,L3:()=>z.<PERSON>,rI:()=>m.DropdownMenu,SQ:()=>m.DropdownMenuContent,_2:()=>m.DropdownMenuItem,ty:()=>m.DropdownMenuTrigger,MJ:()=>g.FormControl,zB:()=>g.FormField,eI:()=>g.FormItem,lR:()=>g.FormLabel,C5:()=>g.FormMessage,KF:()=>w,AM:()=>x.Popover,hl:()=>x.PopoverContent,Wv:()=>x.PopoverTrigger,FK:()=>A.ScrollArea,Ap:()=>j.Slider,XI:()=>I.Table,BF:()=>I.TableBody,nA:()=>I.TableCell,nd:()=>I.TableHead,A0:()=>I.TableHeader,Hj:()=>I.TableRow,tU:()=>C.Tabs,j7:()=>C.TabsList,Xi:()=>C.TabsTrigger,TM:()=>S});var a=t(8978),n=t(7093),i=t(4545),r=t(8020),l=t(2447),s=t(8322);let d=(0,l.F)("inline-flex items-center justify-center rounded-sm border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function p(e){let{className:o,variant:t,asChild:a=!1,...i}=e,l=a?r.Slot:"span";return(0,n.jsx)(l,{"data-slot":"badge",className:(0,s.cn)(d({variant:t}),o),...i})}var c=t(9301),u=t(6844),z=t(1053);t(5822);var m=t(8510),g=t(1991);t(3849);var b=t(5049),h=t.n(b),v=t(7676),k=t(8365),y=t(1040),x=t(7204);let f=(0,l.F)("px-2 py-1",{variants:{variant:{default:"border-foreground/10 text-foreground bg-card hover:bg-card/80",secondary:"border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",inverted:"bg-background"}},defaultVariants:{variant:"default"}}),w=i.forwardRef((e,o)=>{let{options:t,onValueChange:a,variant:r,defaultValue:l=[],placeholder:d="Select options",maxCount:c=3,modalPopover:z=!1,className:m,...g}=e,[b,w]=i.useState(l),[A,j]=i.useState(!1),I=e=>{let o=b.includes(e)?b.filter(o=>o!==e):[...b,e];w(o),a(o)},C=()=>{let e=b.slice(0,c);w(e),a(e)},S=i.useRef(new Map),T=i.useRef(""),P=i.useCallback((e,o)=>{var a;if(T.current!==o)for(let{obj:{value:e},score:a}of(T.current=o,S.current.clear(),h().go(o,t,{key:"label"})))S.current.set(e,a);return"all"===e?.01*(S.current.size>1):null!==(a=S.current.get(e))&&void 0!==a?a:0},[t]);return(0,n.jsxs)(x.Popover,{open:A,onOpenChange:j,modal:z,children:[(0,n.jsx)(x.PopoverTrigger,{asChild:!0,children:(0,n.jsx)("div",{ref:o,...g,onClick:()=>{j(e=>!e)},className:(0,s.cn)("flex w-full rounded-sm min-h-9 h-auto items-center justify-between [&_svg]:pointer-events-auto","font-medium border border-input bg-input hover:opacity-80 cursor-pointer",m),children:b.length>0?(0,n.jsx)("div",{className:"flex justify-between items-center w-full",children:(0,n.jsxs)("div",{className:"flex flex-wrap items-center gap-1 p-1",children:[b.slice(0,c).map(e=>{var o;return(0,n.jsx)(p,{className:(0,s.cn)(f({variant:r})),children:(0,n.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,n.jsx)("div",{children:null===(o=t.find(o=>o.value===e))||void 0===o?void 0:o.label}),(0,n.jsx)("div",{onClick:o=>{o.stopPropagation(),I(e)},className:"cursor-pointer",children:(0,n.jsx)(v.A,{className:"size-4 rounded-full p-0.5 bg-accent/5"})})]})},e)}),b.length>c&&(0,n.jsx)(p,{className:(0,s.cn)("text-ring",f({variant:r})),children:(0,n.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,n.jsx)("div",{children:"+ ".concat(b.length-c," more")}),(0,n.jsx)("div",{onClick:e=>{e.stopPropagation(),C()},className:"cursor-pointer",children:(0,n.jsx)(v.A,{className:"size-4 rounded-full p-0.5 bg-ring/5"})})]})})]})}):(0,n.jsxs)("div",{className:"flex items-center justify-between w-full mx-auto",children:[(0,n.jsx)("span",{className:"text-muted-foreground mx-3",children:d}),(0,n.jsx)(k.A,{className:"opacity-50 size-4 mx-2"})]})})}),(0,n.jsx)(x.PopoverContent,{className:"p-0 w-[var(--radix-popover-trigger-width)]",align:"start",onEscapeKeyDown:()=>j(!1),children:(0,n.jsxs)(u.Command,{filter:P,children:[(0,n.jsx)(u.CommandInput,{placeholder:"Search",onKeyDown:e=>{if("Enter"===e.key)j(!0);else if("Backspace"===e.key&&!e.currentTarget.value){let e=[...b];e.pop(),w(e),a(e)}}}),(0,n.jsxs)(u.CommandList,{children:[(0,n.jsx)(u.CommandEmpty,{children:"No results found."}),(0,n.jsxs)(u.CommandGroup,{children:[t.map(e=>(0,n.jsxs)(u.CommandItem,{value:e.value,onSelect:()=>I(e.value),className:"flex items-center justify-between",children:[(0,n.jsx)("span",{children:e.label}),(0,n.jsx)(y.A,{className:(0,s.cn)("text-accent group-data-[selected=true]:text-accent-foreground size-4",{"opacity-0":!b.includes(e.value)})})]},e.value)),(0,n.jsx)(u.CommandItem,{value:"all",onSelect:()=>{let e=Array.from(S.current.keys());if(b.length===e.length&&b.sort().join(",")===e.sort().join(",")){w([]),a([]);return}w(e),a(e)},className:"flex items-center justify-between",children:(0,n.jsx)("span",{children:"Select All"})},"all")]})]})]})})]})});w.displayName="MultiSelect";var A=t(2800);t(5945),t(1762);var j=t(962);t(7546);var I=t(655),C=t(1897);function S(e){let{className:o,...t}=e;return(0,n.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-sm px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50","border border-input bg-input",o),...t})}t(5134)},8511:(e,o,t)=>{t.d(o,{Ur:()=>ez,jK:()=>ep,LT:()=>D,Pj:()=>ev,Xx:()=>F,YZ:()=>ed,AQ:()=>N,us:()=>ec,Hk:()=>ef}),Object.values({"us.":{regionId:"us-east-1",description:"US East (N. Virginia)",pattern:"us-",multiRegion:!0},"use.":{regionId:"us-east-1",description:"US East (N. Virginia)"},"use1.":{regionId:"us-east-1",description:"US East (N. Virginia)"},"use2.":{regionId:"us-east-2",description:"US East (Ohio)"},"usw.":{regionId:"us-west-2",description:"US West (Oregon)"},"usw2.":{regionId:"us-west-2",description:"US West (Oregon)"},"ug.":{regionId:"us-gov-west-1",description:"AWS GovCloud (US-West)",pattern:"us-gov-",multiRegion:!0},"uge1.":{regionId:"us-gov-east-1",description:"AWS GovCloud (US-East)"},"ugw1.":{regionId:"us-gov-west-1",description:"AWS GovCloud (US-West)"},"eu.":{regionId:"eu-west-1",description:"Europe (Ireland)",pattern:"eu-",multiRegion:!0},"euw1.":{regionId:"eu-west-1",description:"Europe (Ireland)"},"euw2.":{regionId:"eu-west-2",description:"Europe (London)"},"euw3.":{regionId:"eu-west-3",description:"Europe (Paris)"},"euc1.":{regionId:"eu-central-1",description:"Europe (Frankfurt)"},"euc2.":{regionId:"eu-central-2",description:"Europe (Zurich)"},"eun1.":{regionId:"eu-north-1",description:"Europe (Stockholm)"},"eus1.":{regionId:"eu-south-1",description:"Europe (Milan)"},"eus2.":{regionId:"eu-south-2",description:"Europe (Spain)"},"ap.":{regionId:"ap-southeast-1",description:"Asia Pacific (Singapore)",pattern:"ap-",multiRegion:!0},"ape1.":{regionId:"ap-east-1",description:"Asia Pacific (Hong Kong)"},"apne1.":{regionId:"ap-northeast-1",description:"Asia Pacific (Tokyo)"},"apne2.":{regionId:"ap-northeast-2",description:"Asia Pacific (Seoul)"},"apne3.":{regionId:"ap-northeast-3",description:"Asia Pacific (Osaka)"},"aps1.":{regionId:"ap-south-1",description:"Asia Pacific (Mumbai)"},"aps2.":{regionId:"ap-south-2",description:"Asia Pacific (Hyderabad)"},"apse1.":{regionId:"ap-southeast-1",description:"Asia Pacific (Singapore)"},"apse2.":{regionId:"ap-southeast-2",description:"Asia Pacific (Sydney)"},"ca.":{regionId:"ca-central-1",description:"Canada (Central)",pattern:"ca-",multiRegion:!0},"cac1.":{regionId:"ca-central-1",description:"Canada (Central)"},"sa.":{regionId:"sa-east-1",description:"South America (S\xe3o Paulo)",pattern:"sa-",multiRegion:!0},"sae1.":{regionId:"sa-east-1",description:"South America (S\xe3o Paulo)"},"apac.":{regionId:"ap-southeast-1",description:"Default APAC region",pattern:"ap-",multiRegion:!0},"emea.":{regionId:"eu-west-1",description:"Default EMEA region",pattern:"eu-",multiRegion:!0},"amer.":{regionId:"us-east-1",description:"Default Americas region",pattern:"us-",multiRegion:!0}}).map(e=>({value:e.regionId,label:e.regionId})).filter((e,o,t)=>o===t.findIndex(o=>o.value===e.value)).sort((e,o)=>e.value.localeCompare(o.value));var a=t(9860);let n=a.z.object({codebaseIndexEnabled:a.z.boolean().optional(),codebaseIndexQdrantUrl:a.z.string().optional(),codebaseIndexEmbedderProvider:a.z.enum(["openai","ollama","openai-compatible"]).optional(),codebaseIndexEmbedderBaseUrl:a.z.string().optional(),codebaseIndexEmbedderModelId:a.z.string().optional()}),i=a.z.object({openai:a.z.record(a.z.string(),a.z.object({dimension:a.z.number()})).optional(),ollama:a.z.record(a.z.string(),a.z.object({dimension:a.z.number()})).optional(),"openai-compatible":a.z.record(a.z.string(),a.z.object({dimension:a.z.number()})).optional()}),r=a.z.object({codeIndexOpenAiKey:a.z.string().optional(),codeIndexQdrantApiKey:a.z.string().optional(),codebaseIndexOpenAiCompatibleBaseUrl:a.z.string().optional(),codebaseIndexOpenAiCompatibleApiKey:a.z.string().optional(),codebaseIndexOpenAiCompatibleModelDimension:a.z.number().optional()}),l=a.z.enum(["low","medium","high"]),s=a.z.enum(["max_tokens","temperature","reasoning","include_reasoning"]),d=a.z.object({maxTokens:a.z.number().nullish(),maxThinkingTokens:a.z.number().nullish(),contextWindow:a.z.number(),supportsImages:a.z.boolean().optional(),supportsComputerUse:a.z.boolean().optional(),supportsPromptCache:a.z.boolean(),supportsReasoningBudget:a.z.boolean().optional(),requiredReasoningBudget:a.z.boolean().optional(),supportsReasoningEffort:a.z.boolean().optional(),supportedParameters:a.z.array(s).optional(),inputPrice:a.z.number().optional(),outputPrice:a.z.number().optional(),cacheWritesPrice:a.z.number().optional(),cacheReadsPrice:a.z.number().optional(),description:a.z.string().optional(),reasoningEffort:l.optional(),minTokensPerCachePoint:a.z.number().optional(),maxCachePoints:a.z.number().optional(),cachableFields:a.z.array(a.z.string()).optional(),tiers:a.z.array(a.z.object({contextWindow:a.z.number(),inputPrice:a.z.number().optional(),outputPrice:a.z.number().optional(),cacheWritesPrice:a.z.number().optional(),cacheReadsPrice:a.z.number().optional()})).optional()}),p=["anthropic","glama","openrouter","bedrock","vertex","openai","ollama","vscode-lm","lmstudio","gemini","openai-native","mistral","deepseek","unbound","requesty","human-relay","fake-ai","xai","groq","chutes","litellm"],c=a.z.enum(p),u=a.z.object({id:a.z.string(),name:a.z.string(),apiProvider:c.optional()}),z=a.z.object({includeMaxTokens:a.z.boolean().optional(),diffEnabled:a.z.boolean().optional(),fuzzyMatchThreshold:a.z.number().optional(),modelTemperature:a.z.number().nullish(),rateLimitSeconds:a.z.number().optional(),enableReasoningEffort:a.z.boolean().optional(),reasoningEffort:l.optional(),modelMaxTokens:a.z.number().optional(),modelMaxThinkingTokens:a.z.number().optional()}),m=z.extend({apiModelId:a.z.string().optional()}),g=m.extend({apiKey:a.z.string().optional(),anthropicBaseUrl:a.z.string().optional(),anthropicUseAuthToken:a.z.boolean().optional()}),b=z.extend({glamaModelId:a.z.string().optional(),glamaApiKey:a.z.string().optional()}),h=z.extend({openRouterApiKey:a.z.string().optional(),openRouterModelId:a.z.string().optional(),openRouterBaseUrl:a.z.string().optional(),openRouterSpecificProvider:a.z.string().optional(),openRouterUseMiddleOutTransform:a.z.boolean().optional()}),v=m.extend({awsAccessKey:a.z.string().optional(),awsSecretKey:a.z.string().optional(),awsSessionToken:a.z.string().optional(),awsRegion:a.z.string().optional(),awsUseCrossRegionInference:a.z.boolean().optional(),awsUsePromptCache:a.z.boolean().optional(),awsProfile:a.z.string().optional(),awsUseProfile:a.z.boolean().optional(),awsCustomArn:a.z.string().optional(),awsModelContextWindow:a.z.number().optional(),awsBedrockEndpointEnabled:a.z.boolean().optional(),awsBedrockEndpoint:a.z.string().optional()}),k=m.extend({vertexKeyFile:a.z.string().optional(),vertexJsonCredentials:a.z.string().optional(),vertexProjectId:a.z.string().optional(),vertexRegion:a.z.string().optional()}),y=z.extend({openAiBaseUrl:a.z.string().optional(),openAiApiKey:a.z.string().optional(),openAiLegacyFormat:a.z.boolean().optional(),openAiR1FormatEnabled:a.z.boolean().optional(),openAiModelId:a.z.string().optional(),openAiCustomModelInfo:d.nullish(),openAiUseAzure:a.z.boolean().optional(),azureApiVersion:a.z.string().optional(),openAiStreamingEnabled:a.z.boolean().optional(),openAiHostHeader:a.z.string().optional(),openAiHeaders:a.z.record(a.z.string(),a.z.string()).optional()}),x=z.extend({ollamaModelId:a.z.string().optional(),ollamaBaseUrl:a.z.string().optional()}),f=z.extend({vsCodeLmModelSelector:a.z.object({vendor:a.z.string().optional(),family:a.z.string().optional(),version:a.z.string().optional(),id:a.z.string().optional()}).optional()}),w=z.extend({lmStudioModelId:a.z.string().optional(),lmStudioBaseUrl:a.z.string().optional(),lmStudioDraftModelId:a.z.string().optional(),lmStudioSpeculativeDecodingEnabled:a.z.boolean().optional()}),A=m.extend({geminiApiKey:a.z.string().optional(),googleGeminiBaseUrl:a.z.string().optional()}),j=m.extend({openAiNativeApiKey:a.z.string().optional(),openAiNativeBaseUrl:a.z.string().optional()}),I=m.extend({mistralApiKey:a.z.string().optional(),mistralCodestralUrl:a.z.string().optional()}),C=m.extend({deepSeekBaseUrl:a.z.string().optional(),deepSeekApiKey:a.z.string().optional()}),S=z.extend({unboundApiKey:a.z.string().optional(),unboundModelId:a.z.string().optional()}),T=z.extend({requestyApiKey:a.z.string().optional(),requestyModelId:a.z.string().optional()}),P=z.extend({fakeAi:a.z.unknown().optional()}),_=m.extend({xaiApiKey:a.z.string().optional()}),M=m.extend({groqApiKey:a.z.string().optional()}),E=m.extend({chutesApiKey:a.z.string().optional()}),R=z.extend({litellmBaseUrl:a.z.string().optional(),litellmApiKey:a.z.string().optional(),litellmModelId:a.z.string().optional()}),U=a.z.object({apiProvider:a.z.undefined()});a.z.discriminatedUnion("apiProvider",[g.merge(a.z.object({apiProvider:a.z.literal("anthropic")})),b.merge(a.z.object({apiProvider:a.z.literal("glama")})),h.merge(a.z.object({apiProvider:a.z.literal("openrouter")})),v.merge(a.z.object({apiProvider:a.z.literal("bedrock")})),k.merge(a.z.object({apiProvider:a.z.literal("vertex")})),y.merge(a.z.object({apiProvider:a.z.literal("openai")})),x.merge(a.z.object({apiProvider:a.z.literal("ollama")})),f.merge(a.z.object({apiProvider:a.z.literal("vscode-lm")})),w.merge(a.z.object({apiProvider:a.z.literal("lmstudio")})),A.merge(a.z.object({apiProvider:a.z.literal("gemini")})),j.merge(a.z.object({apiProvider:a.z.literal("openai-native")})),I.merge(a.z.object({apiProvider:a.z.literal("mistral")})),C.merge(a.z.object({apiProvider:a.z.literal("deepseek")})),S.merge(a.z.object({apiProvider:a.z.literal("unbound")})),T.merge(a.z.object({apiProvider:a.z.literal("requesty")})),z.merge(a.z.object({apiProvider:a.z.literal("human-relay")})),P.merge(a.z.object({apiProvider:a.z.literal("fake-ai")})),_.merge(a.z.object({apiProvider:a.z.literal("xai")})),M.merge(a.z.object({apiProvider:a.z.literal("groq")})),E.merge(a.z.object({apiProvider:a.z.literal("chutes")})),R.merge(a.z.object({apiProvider:a.z.literal("litellm")})),U]);let N=a.z.object({apiProvider:c.optional(),...g.shape,...b.shape,...h.shape,...v.shape,...k.shape,...y.shape,...x.shape,...f.shape,...w.shape,...A.shape,...j.shape,...I.shape,...C.shape,...S.shape,...T.shape,...z.shape,...P.shape,..._.shape,...M.shape,...E.shape,...R.shape,...r.shape}),D=N.keyof().options,K=["apiModelId","glamaModelId","openRouterModelId","openAiModelId","ollamaModelId","lmStudioModelId","lmStudioDraftModelId","unboundModelId","requestyModelId","litellmModelId"],F=e=>{let o=K.find(o=>e[o]);return o?e[o]:void 0},O=a.z.object({id:a.z.string(),number:a.z.number(),ts:a.z.number(),task:a.z.string(),tokensIn:a.z.number(),tokensOut:a.z.number(),cacheWrites:a.z.number().optional(),cacheReads:a.z.number().optional(),totalCost:a.z.number(),size:a.z.number().optional(),workspace:a.z.string().optional()});a.z.enum(["powerSteering","disableCompletionCommand","multiFileApplyDiff"]);let L=a.z.object({powerSteering:a.z.boolean().optional(),disableCompletionCommand:a.z.boolean().optional(),multiFileApplyDiff:a.z.boolean().optional()}),W=a.z.enum(["followup","command","command_output","completion_result","tool","api_req_failed","resume_task","resume_completed_task","mistake_limit_reached","browser_action_launch","use_mcp_server","auto_approval_max_req_reached"]),B=a.z.enum(["error","api_req_started","api_req_finished","api_req_retried","api_req_retry_delayed","api_req_deleted","text","reasoning","completion_result","user_feedback","user_feedback_diff","command_output","shell_integration_warning","browser_action","browser_action_result","mcp_server_request_started","mcp_server_response","subtask_result","checkpoint_saved","rooignore_error","diff_error","condense_context","condense_context_error","codebase_search_result"]),q=a.z.object({icon:a.z.string().optional(),text:a.z.string().optional()}),H=a.z.object({cost:a.z.number(),prevContextTokens:a.z.number(),newContextTokens:a.z.number(),summary:a.z.string()}),V=a.z.object({ts:a.z.number(),type:a.z.union([a.z.literal("ask"),a.z.literal("say")]),ask:W.optional(),say:B.optional(),text:a.z.string().optional(),images:a.z.array(a.z.string()).optional(),partial:a.z.boolean().optional(),reasoning:a.z.string().optional(),conversationHistoryIndex:a.z.number().optional(),checkpoint:a.z.record(a.z.string(),a.z.unknown()).optional(),progressStatus:q.optional(),contextCondense:H.optional(),isProtected:a.z.boolean().optional()}),Z=a.z.object({totalTokensIn:a.z.number(),totalTokensOut:a.z.number(),totalCacheWrites:a.z.number().optional(),totalCacheReads:a.z.number().optional(),totalCost:a.z.number(),contextTokens:a.z.number()}),G=a.z.enum(["unset","enabled","disabled"]),Q=a.z.object({appName:a.z.string(),appVersion:a.z.string(),vscodeVersion:a.z.string(),platform:a.z.string(),editorName:a.z.string(),language:a.z.string(),mode:a.z.string()}),$=a.z.object({taskId:a.z.string().optional(),apiProvider:a.z.enum(p).optional(),modelId:a.z.string().optional(),diffStrategy:a.z.string().optional(),isSubtask:a.z.boolean().optional()}),X=a.z.object({...Q.shape,...$.shape});a.z.discriminatedUnion("type",[a.z.object({type:a.z.enum(["Task Created","Task Reopened","Task Completed","Conversation Message","Mode Switched","Tool Used","Checkpoint Created","Checkpoint Restored","Checkpoint Diffed","Code Action Used","Prompt Enhanced","Title Button Clicked","Authentication Initiated","Marketplace Item Installed","Marketplace Item Removed","Schema Validation Error","Diff Application Error","Shell Integration Error","Consecutive Mistake Error","Context Condensed","Sliding Window Truncation"]),properties:X}),a.z.object({type:a.z.literal("Task Message"),properties:a.z.object({...X.shape,taskId:a.z.string(),message:V})}),a.z.object({type:a.z.literal("LLM Completion"),properties:a.z.object({...X.shape,inputTokens:a.z.number(),outputTokens:a.z.number(),cacheReadTokens:a.z.number().optional(),cacheWriteTokens:a.z.number().optional(),cost:a.z.number().optional()})})]);let J=a.z.enum(["read","edit","browser","command","mcp","modes"]),Y=a.z.enum(["execute_command","read_file","write_to_file","apply_diff","insert_content","search_and_replace","search_files","list_files","list_code_definition_names","browser_action","use_mcp_tool","access_mcp_resource","ask_followup_question","attempt_completion","switch_mode","new_task","fetch_instructions","codebase_search"]),ee=a.z.record(Y,a.z.object({attempts:a.z.number(),failures:a.z.number()})),eo=a.z.object({fileRegex:a.z.string().optional().refine(e=>{if(!e)return!0;try{return new RegExp(e),!0}catch(e){return!1}},{message:"Invalid regular expression pattern"}),description:a.z.string().optional()}),et=a.z.union([J,a.z.tuple([J,eo])]),ea=a.z.array(et).refine(e=>{let o=new Set;return e.every(e=>{let t=Array.isArray(e)?e[0]:e;return!o.has(t)&&(o.add(t),!0)})},{message:"Duplicate groups are not allowed"}),en=a.z.object({slug:a.z.string().regex(/^[a-zA-Z0-9-]+$/,"Slug must contain only letters numbers and dashes"),name:a.z.string().min(1,"Name is required"),roleDefinition:a.z.string().min(1,"Role definition is required"),whenToUse:a.z.string().optional(),customInstructions:a.z.string().optional(),groups:ea,source:a.z.enum(["global","project"]).optional()});a.z.object({customModes:a.z.array(en).refine(e=>{let o=new Set;return e.every(e=>!o.has(e.slug)&&(o.add(e.slug),!0))},{message:"Duplicate mode slugs are not allowed"})});let ei=a.z.object({roleDefinition:a.z.string().optional(),whenToUse:a.z.string().optional(),customInstructions:a.z.string().optional()}),er=a.z.record(a.z.string(),ei.optional()),el=a.z.record(a.z.string(),a.z.string().optional()),es=a.z.enum(["ca","de","en","es","fr","hi","id","it","ja","ko","nl","pl","pt-BR","ru","tr","vi","zh-CN","zh-TW"]),ed=a.z.object({currentApiConfigName:a.z.string().optional(),listApiConfigMeta:a.z.array(u).optional(),pinnedApiConfigs:a.z.record(a.z.string(),a.z.boolean()).optional(),lastShownAnnouncementId:a.z.string().optional(),customInstructions:a.z.string().optional(),taskHistory:a.z.array(O).optional(),condensingApiConfigId:a.z.string().optional(),customCondensingPrompt:a.z.string().optional(),autoApprovalEnabled:a.z.boolean().optional(),alwaysAllowReadOnly:a.z.boolean().optional(),alwaysAllowReadOnlyOutsideWorkspace:a.z.boolean().optional(),alwaysAllowWrite:a.z.boolean().optional(),alwaysAllowWriteOutsideWorkspace:a.z.boolean().optional(),alwaysAllowWriteProtected:a.z.boolean().optional(),writeDelayMs:a.z.number().optional(),alwaysAllowBrowser:a.z.boolean().optional(),alwaysApproveResubmit:a.z.boolean().optional(),requestDelaySeconds:a.z.number().optional(),alwaysAllowMcp:a.z.boolean().optional(),alwaysAllowModeSwitch:a.z.boolean().optional(),alwaysAllowSubtasks:a.z.boolean().optional(),alwaysAllowExecute:a.z.boolean().optional(),allowedCommands:a.z.array(a.z.string()).optional(),allowedMaxRequests:a.z.number().nullish(),autoCondenseContext:a.z.boolean().optional(),autoCondenseContextPercent:a.z.number().optional(),maxConcurrentFileReads:a.z.number().optional(),browserToolEnabled:a.z.boolean().optional(),browserViewportSize:a.z.string().optional(),screenshotQuality:a.z.number().optional(),remoteBrowserEnabled:a.z.boolean().optional(),remoteBrowserHost:a.z.string().optional(),cachedChromeHostUrl:a.z.string().optional(),enableCheckpoints:a.z.boolean().optional(),ttsEnabled:a.z.boolean().optional(),ttsSpeed:a.z.number().optional(),soundEnabled:a.z.boolean().optional(),soundVolume:a.z.number().optional(),maxOpenTabsContext:a.z.number().optional(),maxWorkspaceFiles:a.z.number().optional(),showRooIgnoredFiles:a.z.boolean().optional(),maxReadFileLine:a.z.number().optional(),terminalOutputLineLimit:a.z.number().optional(),terminalShellIntegrationTimeout:a.z.number().optional(),terminalShellIntegrationDisabled:a.z.boolean().optional(),terminalCommandDelay:a.z.number().optional(),terminalPowershellCounter:a.z.boolean().optional(),terminalZshClearEolMark:a.z.boolean().optional(),terminalZshOhMy:a.z.boolean().optional(),terminalZshP10k:a.z.boolean().optional(),terminalZdotdir:a.z.boolean().optional(),terminalCompressProgressBar:a.z.boolean().optional(),rateLimitSeconds:a.z.number().optional(),diffEnabled:a.z.boolean().optional(),fuzzyMatchThreshold:a.z.number().optional(),experiments:L.optional(),codebaseIndexModels:i.optional(),codebaseIndexConfig:n.optional(),language:es.optional(),telemetrySetting:G.optional(),mcpEnabled:a.z.boolean().optional(),enableMcpServerCreation:a.z.boolean().optional(),mode:a.z.string().optional(),modeApiConfigs:a.z.record(a.z.string(),a.z.string()).optional(),customModes:a.z.array(en).optional(),customModePrompts:er.optional(),customSupportPrompts:el.optional(),enhancementApiConfigId:a.z.string().optional(),historyPreviewCollapsed:a.z.boolean().optional()}),ep=ed.keyof().options,ec=N.merge(ed),eu=["apiKey","glamaApiKey","openRouterApiKey","awsAccessKey","awsSecretKey","awsSessionToken","openAiApiKey","geminiApiKey","openAiNativeApiKey","deepSeekApiKey","mistralApiKey","unboundApiKey","requestyApiKey","xaiApiKey","groqApiKey","chutesApiKey","litellmApiKey","codeIndexOpenAiKey","codeIndexQdrantApiKey","codebaseIndexOpenAiCompatibleApiKey"];[...ep,...D].filter(e=>!eu.includes(e));let ez={apiProvider:"openrouter",openRouterUseMiddleOutTransform:!1,lastShownAnnouncementId:"may-29-2025-3-19",pinnedApiConfigs:{},autoApprovalEnabled:!0,alwaysAllowReadOnly:!0,alwaysAllowReadOnlyOutsideWorkspace:!1,alwaysAllowWrite:!0,alwaysAllowWriteOutsideWorkspace:!1,alwaysAllowWriteProtected:!1,writeDelayMs:1e3,alwaysAllowBrowser:!0,alwaysApproveResubmit:!0,requestDelaySeconds:10,alwaysAllowMcp:!0,alwaysAllowModeSwitch:!0,alwaysAllowSubtasks:!0,alwaysAllowExecute:!0,allowedCommands:["*"],browserToolEnabled:!1,browserViewportSize:"900x600",screenshotQuality:75,remoteBrowserEnabled:!1,ttsEnabled:!1,ttsSpeed:1,soundEnabled:!1,soundVolume:.5,terminalOutputLineLimit:500,terminalShellIntegrationTimeout:3e4,terminalCommandDelay:0,terminalPowershellCounter:!1,terminalZshOhMy:!0,terminalZshClearEolMark:!0,terminalZshP10k:!1,terminalZdotdir:!0,terminalCompressProgressBar:!0,terminalShellIntegrationDisabled:!0,diffEnabled:!0,fuzzyMatchThreshold:1,enableCheckpoints:!1,rateLimitSeconds:0,maxOpenTabsContext:20,maxWorkspaceFiles:200,showRooIgnoredFiles:!0,maxReadFileLine:-1,language:"en",telemetrySetting:"enabled",mcpEnabled:!1,mode:"code",customModes:[]},em=a.z.object({allowAll:a.z.boolean(),providers:a.z.record(a.z.object({allowAll:a.z.boolean(),models:a.z.array(a.z.string()).optional()}))}),eg=ed.pick({enableCheckpoints:!0,fuzzyMatchThreshold:!0,maxOpenTabsContext:!0,maxReadFileLine:!0,maxWorkspaceFiles:!0,showRooIgnoredFiles:!0,terminalCommandDelay:!0,terminalCompressProgressBar:!0,terminalOutputLineLimit:!0,terminalShellIntegrationDisabled:!0,terminalShellIntegrationTimeout:!0,terminalZshClearEolMark:!0}).merge(a.z.object({maxOpenTabsContext:a.z.number().int().nonnegative().optional(),maxReadFileLine:a.z.number().int().gte(-1).optional(),maxWorkspaceFiles:a.z.number().int().nonnegative().optional(),terminalCommandDelay:a.z.number().int().nonnegative().optional(),terminalOutputLineLimit:a.z.number().int().nonnegative().optional(),terminalShellIntegrationTimeout:a.z.number().int().nonnegative().optional()})),eb=a.z.object({recordTaskMessages:a.z.boolean().optional(),enableTaskSharing:a.z.boolean().optional(),taskShareExpirationDays:a.z.number().int().positive().optional()});a.z.object({version:a.z.number(),cloudSettings:eb.optional(),defaultSettings:eg,allowList:em}),a.z.object({success:a.z.boolean(),shareUrl:a.z.string().optional(),error:a.z.string().optional(),isNewShare:a.z.boolean().optional(),manageUrl:a.z.string().optional()});let eh=a.z.object({isSubtask:a.z.boolean()});var ev=function(e){return e.Message="message",e.TaskCreated="taskCreated",e.TaskStarted="taskStarted",e.TaskModeSwitched="taskModeSwitched",e.TaskPaused="taskPaused",e.TaskUnpaused="taskUnpaused",e.TaskAskResponded="taskAskResponded",e.TaskAborted="taskAborted",e.TaskSpawned="taskSpawned",e.TaskCompleted="taskCompleted",e.TaskTokenUsageUpdated="taskTokenUsageUpdated",e.TaskToolFailed="taskToolFailed",e.EvalPass="evalPass",e.EvalFail="evalFail",e}({});let ek=a.z.object({message:a.z.tuple([a.z.object({taskId:a.z.string(),action:a.z.union([a.z.literal("created"),a.z.literal("updated")]),message:V})]),taskCreated:a.z.tuple([a.z.string()]),taskStarted:a.z.tuple([a.z.string()]),taskModeSwitched:a.z.tuple([a.z.string(),a.z.string()]),taskPaused:a.z.tuple([a.z.string()]),taskUnpaused:a.z.tuple([a.z.string()]),taskAskResponded:a.z.tuple([a.z.string()]),taskAborted:a.z.tuple([a.z.string()]),taskSpawned:a.z.tuple([a.z.string(),a.z.string()]),taskCompleted:a.z.tuple([a.z.string(),Z,ee,eh]),taskTokenUsageUpdated:a.z.tuple([a.z.string(),Z]),taskToolFailed:a.z.tuple([a.z.string(),Y,a.z.string()])}),ey=a.z.object({clientId:a.z.string(),pid:a.z.number(),ppid:a.z.number()}),ex=a.z.discriminatedUnion("commandName",[a.z.object({commandName:a.z.literal("StartNewTask"),data:a.z.object({configuration:ec,text:a.z.string(),images:a.z.array(a.z.string()).optional(),newTab:a.z.boolean().optional()})}),a.z.object({commandName:a.z.literal("CancelTask"),data:a.z.string()}),a.z.object({commandName:a.z.literal("CloseTask"),data:a.z.string()})]),ef=a.z.discriminatedUnion("eventName",[a.z.object({eventName:a.z.literal("message"),payload:ek.shape.message,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskCreated"),payload:ek.shape.taskCreated,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskStarted"),payload:ek.shape.taskStarted,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskModeSwitched"),payload:ek.shape.taskModeSwitched,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskPaused"),payload:ek.shape.taskPaused,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskUnpaused"),payload:ek.shape.taskUnpaused,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskAskResponded"),payload:ek.shape.taskAskResponded,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskAborted"),payload:ek.shape.taskAborted,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskSpawned"),payload:ek.shape.taskSpawned,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskCompleted"),payload:ek.shape.taskCompleted,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskTokenUsageUpdated"),payload:ek.shape.taskTokenUsageUpdated,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("taskToolFailed"),payload:ek.shape.taskToolFailed,taskId:a.z.number().optional()}),a.z.object({eventName:a.z.literal("evalPass"),payload:a.z.undefined(),taskId:a.z.number()}),a.z.object({eventName:a.z.literal("evalFail"),payload:a.z.undefined(),taskId:a.z.number()})]);a.z.discriminatedUnion("type",[a.z.object({type:a.z.literal("Ack"),origin:a.z.literal("server"),data:ey}),a.z.object({type:a.z.literal("TaskCommand"),origin:a.z.literal("client"),clientId:a.z.string(),data:ex}),a.z.object({type:a.z.literal("TaskEvent"),origin:a.z.literal("server"),relayClientId:a.z.string().optional(),data:ef})]);let ew=a.z.object({name:a.z.string().min(1),key:a.z.string().min(1),placeholder:a.z.string().optional(),optional:a.z.boolean().optional().default(!1)}),eA=a.z.object({name:a.z.string().min(1),content:a.z.string().min(1),parameters:a.z.array(ew).optional(),prerequisites:a.z.array(a.z.string()).optional()});a.z.enum(["mode","mcp"]);let ej=a.z.object({id:a.z.string().min(1),name:a.z.string().min(1,"Name is required"),description:a.z.string(),author:a.z.string().optional(),authorUrl:a.z.string().url("Author URL must be a valid URL").optional(),tags:a.z.array(a.z.string()).optional(),prerequisites:a.z.array(a.z.string()).optional()}),eI=ej.extend({content:a.z.string().min(1)}),eC=ej.extend({url:a.z.string().url(),content:a.z.union([a.z.string().min(1),a.z.array(eA)]),parameters:a.z.array(ew).optional()});a.z.discriminatedUnion("type",[eI.extend({type:a.z.literal("mode")}),eC.extend({type:a.z.literal("mcp")})]),a.z.object({target:a.z.enum(["global","project"]).optional().default("project"),parameters:a.z.record(a.z.string(),a.z.any()).optional()}),a.z.discriminatedUnion("status",[a.z.object({executionId:a.z.string(),status:a.z.literal("started"),serverName:a.z.string(),toolName:a.z.string()}),a.z.object({executionId:a.z.string(),status:a.z.literal("output"),response:a.z.string()}),a.z.object({executionId:a.z.string(),status:a.z.literal("completed"),response:a.z.string().optional()}),a.z.object({executionId:a.z.string(),status:a.z.literal("error"),error:a.z.string().optional()})]),a.z.discriminatedUnion("status",[a.z.object({executionId:a.z.string(),status:a.z.literal("started"),pid:a.z.number().optional(),command:a.z.string()}),a.z.object({executionId:a.z.string(),status:a.z.literal("output"),output:a.z.string()}),a.z.object({executionId:a.z.string(),status:a.z.literal("exited"),exitCode:a.z.number().optional()}),a.z.object({executionId:a.z.string(),status:a.z.literal("fallback")})])}}]);