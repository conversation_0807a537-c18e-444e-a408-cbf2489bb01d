import { Langfuse } from "langfuse"
import type { LangfuseConfig } from "./types"
import { validateConfig, isConfigurationComplete } from "./utils/validators"

/**
 * Wrapper around the Langfuse SDK client with error handling and configuration management
 */
export class LangfuseClient {
	private client: Langfuse | null = null
	private config: LangfuseConfig
	private isInitialized = false
	private wasEverInitialized = false

	constructor(config: LangfuseConfig) {
		this.config = validateConfig(config)
	}

	/**
	 * Initializes the Langfuse client
	 */
	async initialize(): Promise<void> {
		if (!this.config.enabled) {
			throw new Error("Langfuse is disabled in configuration")
		}

		if (!isConfigurationComplete(this.config)) {
			throw new Error("Langfuse configuration is incomplete. Missing publicKey or secretKey.")
		}

		try {
			this.client = new Langfuse({
				publicKey: this.config.publicKey!,
				secretKey: this.config.secretKey!,
				baseUrl: this.config.baseUrl,
			})

			this.isInitialized = true
			this.wasEverInitialized = true
		} catch (error) {
			this.isInitialized = false
			throw new Error(`Failed to initialize Langfuse client: ${error instanceof Error ? error.message : String(error)}`)
		}
	}

	/**
	 * Updates the configuration and reinitializes if necessary
	 */
	async updateConfig(newConfig: Partial<LangfuseConfig>): Promise<void> {
		const shouldReinitialize = this.isInitialized || this.wasEverInitialized
		const updatedConfig = { ...this.config, ...newConfig }
		this.config = validateConfig(updatedConfig)

		if (shouldReinitialize) {
			// Shutdown if currently initialized
			if (this.isInitialized) {
				await this.shutdown()
			}

			// Only reinitialize if the new config is enabled and complete
			if (this.config.enabled && isConfigurationComplete(this.config)) {
				await this.initialize()
			}
		}
	}

	/**
	 * Gets the current configuration
	 */
	getConfig(): LangfuseConfig {
		return { ...this.config }
	}

	/**
	 * Checks if the client is ready for use
	 */
	isReady(): boolean {
		return this.isInitialized && this.client !== null && this.config.enabled
	}

	/**
	 * Gets the underlying Langfuse client instance
	 * @throws Error if client is not initialized
	 */
	getClient(): Langfuse {
		if (!this.isReady() || !this.client) {
			throw new Error("Langfuse client is not initialized or disabled")
		}
		return this.client
	}

	/**
	 * Creates a new trace
	 */
	async createTrace(params: {
		id?: string
		name: string
		input?: unknown
		output?: unknown
		metadata?: Record<string, unknown>
		tags?: string[]
		userId?: string
		sessionId?: string
	}) {
		if (!this.isReady()) {
			return null
		}

		try {
			const client = this.getClient()
			const trace = client.trace({
				id: params.id,
				name: params.name,
				input: params.input,
				output: params.output,
				metadata: params.metadata,
				tags: [...(this.config.defaultTags || []), ...(params.tags || [])],
				userId: params.userId,
				sessionId: params.sessionId,
			})

			return trace
		} catch (error) {
			this.handleError(error, "createTrace")
			return null
		}
	}

	/**
	 * Creates a new generation (LLM call)
	 */
	async createGeneration(params: {
		id?: string
		name: string
		model: string
		input?: unknown
		output?: unknown
		usage?: {
			promptTokens?: number
			completionTokens?: number
			totalTokens?: number
		}
		metadata?: Record<string, unknown>
		traceId?: string
		parentObservationId?: string
		startTime?: Date
		endTime?: Date
		level?: "DEBUG" | "DEFAULT" | "WARNING" | "ERROR"
		statusMessage?: string
	}) {
		if (!this.isReady()) {
			return null
		}

		try {
			const client = this.getClient()
			const generation = client.generation({
				id: params.id,
				name: params.name,
				model: params.model,
				input: params.input,
				output: params.output,
				usage: params.usage,
				metadata: params.metadata,
				traceId: params.traceId,
				parentObservationId: params.parentObservationId,
				startTime: params.startTime,
				endTime: params.endTime,
				level: params.level,
				statusMessage: params.statusMessage,
			})

			return generation
		} catch (error) {
			this.handleError(error, "createGeneration")
			return null
		}
	}

	/**
	 * Creates a new span (for tool usage, etc.)
	 */
	async createSpan(params: {
		id?: string
		name: string
		input?: unknown
		output?: unknown
		metadata?: Record<string, unknown>
		traceId?: string
		parentObservationId?: string
		startTime?: Date
		endTime?: Date
		level?: "DEBUG" | "DEFAULT" | "WARNING" | "ERROR"
		statusMessage?: string
	}) {
		if (!this.isReady()) {
			return null
		}

		try {
			const client = this.getClient()
			const span = client.span({
				id: params.id,
				name: params.name,
				input: params.input,
				output: params.output,
				metadata: params.metadata,
				traceId: params.traceId,
				parentObservationId: params.parentObservationId,
				startTime: params.startTime,
				endTime: params.endTime,
				level: params.level,
				statusMessage: params.statusMessage,
			})

			return span
		} catch (error) {
			this.handleError(error, "createSpan")
			return null
		}
	}

	/**
	 * Flushes pending data to Langfuse
	 */
	async flush(): Promise<void> {
		if (!this.isReady()) {
			return
		}

		try {
			const client = this.getClient()
			await client.flushAsync()
		} catch (error) {
			this.handleError(error, "flush")
		}
	}

	/**
	 * Shuts down the client and flushes pending data
	 */
	async shutdown(): Promise<void> {
		if (this.client) {
			try {
				await this.client.shutdownAsync()
			} catch (error) {
				this.handleError(error, "shutdown")
			} finally {
				this.client = null
				this.isInitialized = false
			}
		}
	}

	/**
	 * Handles errors in a consistent way
	 */
	private handleError(error: unknown, context: string): void {
		const errorMessage = error instanceof Error ? error.message : String(error)
		console.error(`[LangfuseClient:${context}] ${errorMessage}`)
		
		// Don't throw errors to avoid breaking the main application
		// Instead, log them and continue gracefully
	}
}
