{"extension": {"name": "Roo Code", "description": "<PERSON><PERSON><PERSON> bộ đội ngũ phát triển AI trong trình soạn thảo của bạn."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "<PERSON><PERSON>o mừng, {{name}}! Bạn có {{count}} thông báo.", "items": {"zero": "<PERSON><PERSON><PERSON><PERSON> có mục nào", "one": "<PERSON><PERSON><PERSON>", "other": "{{count}} mục"}, "confirmation": {"reset_state": "Bạn có chắc chắn muốn đặt lại tất cả trạng thái và lưu trữ bí mật trong tiện ích mở rộng không? Hành động này không thể hoàn tác.", "delete_config_profile": "Bạn có chắc chắn muốn xóa hồ sơ cấu hình này không?", "delete_custom_mode": "Bạn có chắc chắn muốn xóa chế độ tùy chỉnh này không?", "delete_message": "Bạn muốn xóa gì?", "just_this_message": "Chỉ tin nhắn này", "this_and_subsequent": "Tin nhắn này và tất cả tin nhắn tiếp theo"}, "errors": {"invalid_data_uri": "<PERSON><PERSON><PERSON> dạng URI dữ liệu không hợp lệ", "error_copying_image": "Lỗi khi sao chép hình ảnh: {{errorMessage}}", "error_saving_image": "Lỗi khi lưu hình <PERSON>nh: {{errorMessage}}", "error_opening_image": "Lỗi khi mở hình ảnh: {{error}}", "could_not_open_file": "<PERSON><PERSON><PERSON><PERSON> thể mở tệp: {{errorMessage}}", "could_not_open_file_generic": "<PERSON>h<PERSON>ng thể mở tệp!", "checkpoint_timeout": "<PERSON><PERSON> hết thời gian khi cố gắng khôi phục điểm kiểm tra.", "checkpoint_failed": "<PERSON><PERSON><PERSON><PERSON> thể khôi phục điểm kiểm tra.", "no_workspace": "<PERSON><PERSON> lòng mở thư mục dự án trước", "update_support_prompt": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật lời nhắc hỗ trợ", "reset_support_prompt": "<PERSON><PERSON><PERSON><PERSON> thể đặt lại lời nhắc hỗ trợ", "enhance_prompt": "<PERSON><PERSON><PERSON><PERSON> thể nâng cao lời nhắc", "get_system_prompt": "<PERSON><PERSON><PERSON><PERSON> thể lấy lời nh<PERSON>c hệ thống", "search_commits": "<PERSON><PERSON><PERSON><PERSON> thể tìm kiếm c<PERSON>c <PERSON>", "save_api_config": "<PERSON><PERSON><PERSON><PERSON> thể lưu cấu hình <PERSON>", "create_api_config": "<PERSON><PERSON><PERSON><PERSON> thể tạo cấu hình <PERSON>", "rename_api_config": "<PERSON><PERSON><PERSON><PERSON> thể đổi tên cấu hình <PERSON>", "load_api_config": "<PERSON><PERSON><PERSON><PERSON> thể tải cấu hình <PERSON>", "delete_api_config": "<PERSON><PERSON><PERSON><PERSON> thể xóa cấu hình <PERSON>", "list_api_config": "<PERSON><PERSON><PERSON><PERSON> thể lấy danh sách cấu hình <PERSON>", "update_server_timeout": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật thời gian chờ máy chủ", "hmr_not_running": "<PERSON>áy chủ phát triển cục bộ không chạy, HMR sẽ không hoạt động. <PERSON><PERSON> lòng chạy 'npm run dev' trước khi khởi chạy tiện ích mở rộng để bật HMR.", "retrieve_current_mode": "Lỗi không thể truy xuất chế độ hiện tại từ trạng thái.", "failed_delete_repo": "<PERSON><PERSON><PERSON><PERSON> thể xóa kho lưu trữ hoặc nhánh liên quan: {{error}}", "failed_remove_directory": "<PERSON><PERSON><PERSON><PERSON> thể xóa thư mục nhi<PERSON>m vụ: {{error}}", "custom_storage_path_unusable": "Đường dẫn lưu trữ tùy chỉnh \"{{path}}\" không thể sử dụng được, sẽ sử dụng đường dẫn mặc định", "cannot_access_path": "<PERSON><PERSON><PERSON><PERSON> thể truy cập đường dẫn {{path}}: {{error}}", "settings_import_failed": "<PERSON><PERSON><PERSON><PERSON> cài đặt thất bại: {{error}}.", "mistake_limit_guidance": "Điều này có thể cho thấy sự thất bại trong quá trình suy nghĩ của mô hình hoặc không thể sử dụng công cụ đúng cách, có thể được giảm thiểu bằng hướng dẫn của người dùng (ví dụ: \"H<PERSON>y thử chia nhỏ nhiệm vụ thành các bước nhỏ hơn\").", "violated_organization_allowlist": "<PERSON><PERSON><PERSON><PERSON> thể chạy tác vụ: hồ sơ hiện tại vi phạm cài đặt của tổ chức của bạn", "condense_failed": "<PERSON><PERSON><PERSON><PERSON> thể nén ngữ cảnh", "condense_not_enough_messages": "<PERSON>hông đủ tin nhắn để nén ngữ cảnh", "condensed_recently": "Ngữ cảnh đã được nén gần đây; bỏ qua lần thử này", "condense_handler_invalid": "<PERSON><PERSON><PERSON><PERSON> xử lý API để nén ngữ cảnh không hợp lệ", "condense_context_grew": "<PERSON><PERSON><PERSON> thước ngữ cảnh tăng lên trong quá trình nén; bỏ qua lần thử này", "share_task_failed": "<PERSON><PERSON><PERSON><PERSON> thể chia sẻ nhiệm vụ", "share_no_active_task": "<PERSON><PERSON><PERSON><PERSON> có nhiệm vụ hoạt động để chia sẻ", "share_auth_required": "<PERSON><PERSON><PERSON> x<PERSON>c thực. <PERSON><PERSON> lòng đăng nhập để chia sẻ nhiệm vụ.", "share_not_enabled": "<PERSON><PERSON> sẻ nhiệm vụ không đượ<PERSON> bật cho tổ chức này.", "share_task_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhiệm vụ hoặc truy cập bị từ chối."}, "warnings": {"no_terminal_content": "<PERSON><PERSON><PERSON><PERSON> có nội dung terminal đ<PERSON><PERSON><PERSON> chọn", "missing_task_files": "<PERSON><PERSON><PERSON> tệp của nhiệm vụ này bị thiếu. Bạn có muốn xóa nó khỏi danh sách nhiệm vụ không?"}, "info": {"no_changes": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thay đổi nào.", "clipboard_copy": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> hệ thống đã đư<PERSON>c sao chép thành công vào clipboard", "history_cleanup": "<PERSON><PERSON> dọn dẹp {{count}} nhiệm vụ có tệp bị thiếu khỏi lịch sử.", "custom_storage_path_set": "<PERSON><PERSON> thiết lập đường dẫn lưu trữ tùy chỉnh: {{path}}", "default_storage_path": "Đã quay lại sử dụng đường dẫn lưu trữ mặc định", "settings_imported": "<PERSON>ài đặt đã được nhập thành công.", "share_link_copied": "<PERSON><PERSON><PERSON> kết chia sẻ đã được sao chép vào clipboard", "image_copied_to_clipboard": "URI dữ liệu hình ảnh đã đư<PERSON>c sao chép vào clipboard", "image_saved": "<PERSON><PERSON><PERSON>nh đã đư<PERSON><PERSON> lưu vào {{path}}", "organization_share_link_copied": "<PERSON><PERSON><PERSON> kết chia sẻ tổ chức đã được sao chép vào clipboard!", "public_share_link_copied": "<PERSON><PERSON><PERSON> kết chia sẻ công khai đã được sao chép vào clipboard!"}, "answers": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Xóa", "keep": "Giữ"}, "tasks": {"canceled": "Lỗi nhiệm vụ: <PERSON><PERSON> đã bị dừng và hủy bởi người dùng.", "deleted": "Nhiệm vụ thất bại: <PERSON><PERSON> đã bị dừng và xóa bởi người dùng."}, "storage": {"prompt_custom_path": "<PERSON><PERSON><PERSON><PERSON> đường dẫn lưu trữ tùy chỉnh cho lịch sử hội thoại, để trống để sử dụng vị trí mặc định", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "<PERSON><PERSON> lòng nhập đường dẫn tuyệt đối (ví dụ: D:\\RooCodeStorage hoặc /home/<USER>/storage)", "enter_valid_path": "<PERSON><PERSON> lòng nhập đường dẫn hợp lệ"}, "input": {"task_prompt": "Bạn muốn <PERSON>oo làm gì?", "task_placeholder": "<PERSON><PERSON><PERSON><PERSON> nhi<PERSON> vụ của bạn ở đây"}, "settings": {"providers": {"groqApiKey": "Khóa API Groq", "getGroqApiKey": "Lấy khóa API Groq"}}, "mdm": {"errors": {"cloud_auth_required": "Tổ chức của bạn yêu cầu xác thực <PERSON>oo <PERSON> Cloud. <PERSON><PERSON> lòng đăng nhập để tiếp tục.", "organization_mismatch": "Bạn phải được xác thực bằng tài khoản Roo Code Cloud của tổ chức.", "verification_failed": "<PERSON><PERSON><PERSON><PERSON> thể xác minh xác thực tổ chức."}}}