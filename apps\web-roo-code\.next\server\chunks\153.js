exports.id=153,exports.ids=[153],exports.modules={8166:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Documents\\\\Python\\\\Roo-Code\\\\apps\\\\web-roo-code\\\\src\\\\components\\\\chromes\\\\theme-toggle.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\chromes\\theme-toggle.tsx","default")},8798:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>x});var o=t(3641),s=t(31464),n=t(23822),a=t(64925),l=t(30427),i=t(72404),d=t(56102),c=t(44508);function m(){let e=(0,l.usePathname)(),r=(0,l.useSearchParams)();return(0,c.useEffect)(()=>{if(e&&process.env.NEXT_PUBLIC_POSTHOG_KEY){let t=window.location.origin+e;r&&r.toString()&&(t+=`?${r.toString()}`),i.Ay.capture("$pageview",{$current_url:t})}},[e,r.toString()]),null}function h({children:e}){return(0,o.jsxs)(d.so,{client:i.Ay,children:[(0,o.jsx)(c.Suspense,{fallback:null,children:(0,o.jsx)(m,{})}),e]})}let u=new s.E,x=({children:e})=>(0,o.jsx)(n.Ht,{client:u,children:(0,o.jsx)(h,{children:(0,o.jsx)(a.N,{attribute:"class",defaultTheme:"dark",enableSystem:!1,children:e})})})},10244:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var o=t(64955),s=t(23434);function n(...e){return(0,s.QP)((0,o.$)(e))}},14223:(e,r,t)=>{"use strict";t.r(r),t.d(r,{ChartContainer:()=>h,ChartLegend:()=>g,ChartLegendContent:()=>p,ChartStyle:()=>u,ChartTooltip:()=>x,ChartTooltipContent:()=>f});var o=t(3641),s=t(44508),n=t(65561),a=t(6101),l=t(31095),i=t(10244);let d={light:"",dark:".dark"},c=s.createContext(null);function m(){let e=s.useContext(c);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let h=s.forwardRef(({id:e,className:r,children:t,config:a,...l},d)=>{let m=s.useId(),h=`chart-${e||m.replace(/:/g,"")}`;return(0,o.jsx)(c.Provider,{value:{config:a},children:(0,o.jsxs)("div",{"data-chart":h,ref:d,className:(0,i.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",r),...l,children:[(0,o.jsx)(u,{id:h,config:a}),(0,o.jsx)(n.u,{children:t})]})})});h.displayName="Chart";let u=({id:e,config:r})=>{let t=Object.entries(r).filter(([,e])=>e.theme||e.color);return t.length?(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(d).map(([r,o])=>`
${o} [data-chart=${e}] {
${t.map(([e,t])=>{let o=t.theme?.[r]||t.color;return o?`  --color-${e}: ${o};`:null}).join("\n")}
}
`).join("\n")}}):null},x=a.m,f=s.forwardRef(({active:e,payload:r,className:t,indicator:n="dot",hideLabel:a=!1,hideIndicator:l=!1,label:d,labelFormatter:c,labelClassName:h,formatter:u,color:x,nameKey:f,labelKey:g},p)=>{let{config:v}=m(),j=s.useMemo(()=>{if(a||!r?.length)return null;let[e]=r,t=`${g||e?.dataKey||e?.name||"value"}`,s=b(v,e,t),n=g||"string"!=typeof d?s?.label:v[d]?.label||d;return c?(0,o.jsx)("div",{className:(0,i.cn)("font-medium",h),children:c(n,r)}):n?(0,o.jsx)("div",{className:(0,i.cn)("font-medium",h),children:n}):null},[d,c,r,a,h,v,g]);if(!e||!r?.length)return null;let N=1===r.length&&"dot"!==n;return(0,o.jsxs)("div",{ref:p,className:(0,i.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",t),children:[N?null:j,(0,o.jsx)("div",{className:"grid gap-1.5",children:r.map((e,r)=>{let t=`${f||e.name||e.dataKey||"value"}`,s=b(v,e,t),a=x||e.payload.fill||e.color;return(0,o.jsx)("div",{className:(0,i.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===n&&"items-center"),children:u&&e?.value!==void 0&&e.name?u(e.value,e.name,e,r,e.payload):(0,o.jsxs)(o.Fragment,{children:[s?.icon?(0,o.jsx)(s.icon,{}):!l&&(0,o.jsx)("div",{className:(0,i.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===n,"w-1":"line"===n,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===n,"my-0.5":N&&"dashed"===n}),style:{"--color-bg":a,"--color-border":a}}),(0,o.jsxs)("div",{className:(0,i.cn)("flex flex-1 justify-between leading-none",N?"items-end":"items-center"),children:[(0,o.jsxs)("div",{className:"grid gap-1.5",children:[N?j:null,(0,o.jsx)("span",{className:"text-muted-foreground",children:s?.label||e.name})]}),e.value&&(0,o.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});f.displayName="ChartTooltip";let g=l.s,p=s.forwardRef(({className:e,hideIcon:r=!1,payload:t,verticalAlign:s="bottom",nameKey:n},a)=>{let{config:l}=m();return t?.length?(0,o.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center justify-center gap-4","top"===s?"pb-3":"pt-3",e),children:t.map(e=>{let t=`${n||e.dataKey||"value"}`,s=b(l,e,t);return(0,o.jsxs)("div",{className:(0,i.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[s?.icon&&!r?(0,o.jsx)(s.icon,{}):(0,o.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),s?.label]},e.value)})}):null});function b(e,r,t){if("object"!=typeof r||null===r)return;let o="payload"in r&&"object"==typeof r.payload&&null!==r.payload?r.payload:void 0,s=t;return t in r&&"string"==typeof r[t]?s=r[t]:o&&t in o&&"string"==typeof o[t]&&(s=o[t]),s in e?e[s]:e[t]}p.displayName="ChartLegend"},20445:(e,r,t)=>{"use strict";t.r(r),t.d(r,{NavBar:()=>g});var o=t(3641),s=t(40644),n=t.n(s),a=t(19876),l=t(44508),i=t(23334),d=t(4326),c=t(92457),m=t(35695),h=t(35260),u=t(52214),x=t(60391),f=t(95456);function g({stars:e,downloads:r}){let[t,s]=(0,l.useState)(!1),g=(0,u.p)();return(0,o.jsxs)("header",{className:"sticky top-0 z-50 border-b border-border bg-background/80 backdrop-blur-md",children:[(0,o.jsxs)("div",{className:"container mx-auto flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,o.jsx)("div",{className:"flex items-center",children:(0,o.jsx)(n(),{href:"/",className:"flex items-center",children:(0,o.jsx)(a.default,{src:g,alt:"Roo Code Logo",width:120,height:40,className:"h-8 w-auto"})})}),(0,o.jsxs)("nav",{className:"hidden text-sm font-medium md:flex md:items-center md:space-x-3 xl:space-x-8",children:[(0,o.jsx)(x.sb,{targetId:"features",className:"text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground max-lg:hidden",children:"Features"}),(0,o.jsx)(x.sb,{targetId:"testimonials",className:"text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground max-lg:hidden",children:"Testimonials"}),(0,o.jsx)(x.sb,{targetId:"faq",className:"text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground",children:"FAQ"}),(0,o.jsx)(n(),{href:"/evals",className:"text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground",children:"Evals"}),(0,o.jsx)(n(),{href:"/enterprise",className:"text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground",children:"Enterprise"}),(0,o.jsx)("a",{href:h.m.DOCUMENTATION,target:"_blank",className:"text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground",children:"Documentation"}),(0,o.jsx)("a",{href:h.m.CAREERS,target:"_blank",className:"text-muted-foreground transition-transform duration-200 hover:scale-105 hover:text-foreground",children:"Careers"})]}),(0,o.jsxs)("div",{className:"hidden md:flex md:items-center md:space-x-4",children:[(0,o.jsxs)("div",{className:"flex flex-row space-x-2",children:[(0,o.jsx)(f.default,{}),(0,o.jsxs)(n(),{href:h.m.GITHUB,target:"_blank",className:"hidden items-center gap-1.5 text-sm font-medium text-muted-foreground hover:text-foreground md:flex",children:[(0,o.jsx)(i.NVh,{className:"h-4 w-4"}),null!==e&&(0,o.jsx)("span",{children:e})]})]}),(0,o.jsxs)(n(),{href:h.m.MARKETPLACE,target:"_blank",className:"hidden items-center gap-1.5 rounded-full bg-primary px-3 py-1.5 text-sm font-medium text-primary-foreground transition-colors hover:bg-primary/90 md:flex",children:[(0,o.jsx)(d.$$9,{className:"-mr-[2px] mt-[1px] h-4 w-4"}),(0,o.jsxs)("span",{children:["Install ",(0,o.jsx)("span",{className:"font-black max-lg:text-xs",children:"\xb7"})]}),null!==r&&(0,o.jsx)("span",{children:r})]})]}),(0,o.jsx)("button",{"aria-expanded":t,onClick:()=>s(!t),className:"flex items-center justify-center rounded-full p-2 transition-colors hover:bg-accent md:hidden","aria-label":"Toggle mobile menu",children:t?(0,o.jsx)(m.$8F,{className:"h-6 w-6"}):(0,o.jsx)(c.TF4,{className:"h-6 w-6"})})]}),(0,o.jsx)("div",{className:`absolute left-0 right-0 top-16 z-50 transform border-b border-border bg-background shadow-lg backdrop-blur-none transition-all duration-200 md:hidden ${t?"translate-y-0 opacity-100":"pointer-events-none -translate-y-2 opacity-0"}`,children:(0,o.jsxs)("nav",{className:"flex flex-col py-2",children:[(0,o.jsx)(x.sb,{targetId:"features",className:"w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground",onClick:()=>s(!1),children:"Features"}),(0,o.jsx)(x.sb,{targetId:"testimonials",className:"w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground",onClick:()=>s(!1),children:"Testimonials"}),(0,o.jsx)(x.sb,{targetId:"faq",className:"w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground",onClick:()=>s(!1),children:"FAQ"}),(0,o.jsx)(n(),{href:"/evals",className:"w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground",onClick:()=>s(!1),children:"Evals"}),(0,o.jsx)(n(),{href:"/enterprise",className:"w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground",onClick:()=>s(!1),children:"Enterprise"}),(0,o.jsx)("a",{href:h.m.DOCUMENTATION,target:"_blank",className:"w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground",onClick:()=>s(!1),children:"Documentation"}),(0,o.jsx)("a",{href:h.m.CAREERS,target:"_blank",className:"w-full px-8 py-3 text-left text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground",onClick:()=>s(!1),children:"Careers"}),(0,o.jsx)("hr",{className:"mx-8 my-2 border-t border-border/50"}),(0,o.jsxs)("div",{className:"flex items-center justify-center gap-8 px-8 py-3",children:[(0,o.jsxs)(n(),{href:h.m.GITHUB,target:"_blank",className:"inline-flex items-center gap-2 rounded-md p-2 text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground",onClick:()=>s(!1),children:[(0,o.jsx)(i.NVh,{className:"h-5 w-5"}),null!==e&&(0,o.jsx)("span",{children:e})]}),(0,o.jsx)("div",{className:"flex items-center rounded-md p-2 transition-colors hover:bg-accent",children:(0,o.jsx)(f.default,{})}),(0,o.jsxs)(n(),{href:h.m.MARKETPLACE,target:"_blank",className:"inline-flex items-center gap-2 rounded-md p-2 text-sm font-medium text-foreground/80 transition-colors hover:bg-accent hover:text-foreground",onClick:()=>s(!1),children:[(0,o.jsx)(d.$$9,{className:"h-5 w-5"}),null!==r&&(0,o.jsx)("span",{children:r})]})]})]})})]})}},21631:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Footer:()=>o});let o=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\chromes\\footer.tsx","Footer")},29512:(e,r,t)=>{Promise.resolve().then(t.bind(t,84645)),Promise.resolve().then(t.bind(t,20445)),Promise.resolve().then(t.bind(t,95456)),Promise.resolve().then(t.bind(t,8798)),Promise.resolve().then(t.t.bind(t,40644,23))},35260:(e,r,t)=>{"use strict";t.d(r,{m:()=>o,q:()=>s});let o={GITHUB:"https://github.com/RooCodeInc/Roo-Code",DISCORD:"https://discord.gg/roocode",REDDIT:"https://reddit.com/r/RooCode",X:"https://x.com/roo_code",LINKEDIN:"https://www.linkedin.com/company/roo-code",TIKTOK:"https://www.tiktok.com/@roo.code",BLUESKY:"https://bsky.app/profile/roocode.bsky.social",DOCUMENTATION:"https://docs.roocode.com",CAREERS:"https://careers.roocode.com",ISSUES:"https://github.com/RooCodeInc/Roo-Code/issues",FEATURE_REQUESTS:"https://github.com/RooCodeInc/Roo-Code/discussions/categories/feature-requests",COMMUNITY:"https://github.com/RooCodeInc/Roo-Code/discussions",CHANGELOG:"https://github.com/RooCodeInc/Roo-Code/blob/main/CHANGELOG.md",PRIVACY_POLICY_EXTENSION:"https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md",INTEGRATIONS:"https://docs.roocode.com/community",TUTORIALS:"https://docs.roocode.com/tutorial-videos",MARKETPLACE:"https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline"},s={PRIVACY_POLICY_WEBSITE:"/privacy"}},35748:(e,r,t)=>{"use strict";async function o(){try{let e=await fetch("https://api.github.com/repos/RooCodeInc/Roo-Code"),r=await e.json();if("number"!=typeof r.stargazers_count)return console.error("GitHub API: Invalid stargazers count. Possible that you got rate-limited?"),null;return n(r.stargazers_count)}catch(e){return console.error("Error fetching GitHub stars:",e),null}}async function s(){let e=await fetch("https://marketplace.visualstudio.com/_apis/public/gallery/extensionquery",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json;api-version=7.1-preview.1"},body:JSON.stringify({filters:[{criteria:[{filterType:7,value:"RooVeterinaryInc.roo-cline"}]}],flags:914})});try{let r=await e.json(),t=r?.results?.[0]?.extensions?.[0]?.statistics;if(!t)return console.error("VSCode API: Missing statistics in response"),null;let o=t.find(e=>"install"===e.statisticName);if(!o)return console.error("VSCode API: Install count not found"),null;return n(o.value)}catch(e){return console.error("Error fetching VSCode downloads:",e),null}}function n(e){return(Math.floor(e/1e3*10)/10).toFixed(1)+"k"}t.d(r,{XX:()=>o,s8:()=>s})},36295:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h,metadata:()=>m});var o=t(90811);t(4446);var s=t(21767),n=t.n(s),a=t(91993),l=t(35748),i=t(21631),d=t(70227);async function c({children:e}){let[r,t]=await Promise.all([(0,l.XX)(),(0,l.s8)()]);return(0,o.jsxs)("div",{className:"flex min-h-screen flex-col bg-background text-foreground",children:[(0,o.jsx)(d.NavBar,{stars:r,downloads:t}),(0,o.jsx)("main",{className:"flex-1",children:e}),(0,o.jsx)(i.Footer,{})]})}t(19570),t(8166),t(88773);let m={title:"Roo Code – Your AI-Powered Dev Team in VS Code",description:"Roo Code puts an entire AI dev team right in your editor, outpacing closed tools with deep project-wide context, multi-step agentic coding, and unmatched developer-centric flexibility.",alternates:{canonical:"https://roocode.com"},icons:{icon:[{url:"/favicon.ico"},{url:"/favicon-16x16.png",sizes:"16x16",type:"image/png"},{url:"/favicon-32x32.png",sizes:"32x32",type:"image/png"}],apple:[{url:"/apple-touch-icon.png"}],other:[{rel:"android-chrome-192x192",url:"/android-chrome-192x192.png",sizes:"192x192",type:"image/png"},{rel:"android-chrome-512x512",url:"/android-chrome-512x512.png",sizes:"512x512",type:"image/png"}]}};function h({children:e}){return(0,o.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,o.jsx)("head",{children:(0,o.jsx)("link",{rel:"stylesheet",type:"text/css",href:"https://cdn.jsdelivr.net/gh/devicons/devicon@latest/devicon.min.css"})}),(0,o.jsxs)("body",{className:n().className,children:[(0,o.jsxs)("div",{itemScope:!0,itemType:"https://schema.org/WebSite",children:[(0,o.jsx)("link",{itemProp:"url",href:"https://roocode.com"}),(0,o.jsx)("meta",{itemProp:"name",content:"Roo Code"})]}),(0,o.jsx)(a.Providers,{children:(0,o.jsx)(c,{children:e})})]})]})}},37237:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,20502,23)),Promise.resolve().then(t.t.bind(t,15552,23)),Promise.resolve().then(t.t.bind(t,52052,23)),Promise.resolve().then(t.t.bind(t,807,23)),Promise.resolve().then(t.t.bind(t,42159,23)),Promise.resolve().then(t.t.bind(t,99335,23)),Promise.resolve().then(t.t.bind(t,52951,23)),Promise.resolve().then(t.t.bind(t,84417,23))},42664:(e,r,t)=>{Promise.resolve().then(t.bind(t,21631)),Promise.resolve().then(t.bind(t,70227)),Promise.resolve().then(t.bind(t,8166)),Promise.resolve().then(t.bind(t,91993)),Promise.resolve().then(t.t.bind(t,19570,23))},48727:(e,r,t)=>{"use strict";t.r(r),t.d(r,{ScrollButton:()=>n});var o=t(3641),s=t(30427);function n({targetId:e,children:r,className:t="",onClick:n}){let a=(0,s.useRouter)(),l=(0,s.usePathname)();return(0,o.jsx)("button",{onClick:()=>{if("/"===l){let r=document.getElementById(e);r?.scrollIntoView({behavior:"smooth"})}else a.push(`/#${e}`);n?.()},className:t,children:r})}},50805:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,38360,23)),Promise.resolve().then(t.t.bind(t,83510,23)),Promise.resolve().then(t.t.bind(t,61430,23)),Promise.resolve().then(t.t.bind(t,87225,23)),Promise.resolve().then(t.t.bind(t,80989,23)),Promise.resolve().then(t.t.bind(t,17445,23)),Promise.resolve().then(t.t.bind(t,36557,23)),Promise.resolve().then(t.t.bind(t,55895,23))},52214:(e,r,t)=>{"use strict";t.d(r,{p:()=>s});var o=t(64925);function s(){let{resolvedTheme:e}=(0,o.D)();return"light"===e?"/Roo-Code-Logo-Horiz-blk.svg":"/Roo-Code-Logo-Horiz-white.svg"}},60391:(e,r,t)=>{"use strict";t.d(r,{$n:()=>o.$,at:()=>s.ChartContainer,_3:()=>s.ChartLegend,Hm:()=>s.ChartLegendContent,II:()=>s.ChartTooltip,Nt:()=>s.ChartTooltipContent,lG:()=>n.Dialog,Cf:()=>n.DialogContent,rr:()=>n.DialogDescription,Es:()=>n.DialogFooter,c7:()=>n.DialogHeader,L3:()=>n.DialogTitle,zM:()=>n.DialogTrigger,sb:()=>a.ScrollButton,XI:()=>c,BF:()=>h,r6:()=>g,nA:()=>f,nd:()=>x,A0:()=>m,Hj:()=>u});var o=t(99115),s=t(14223),n=t(64624),a=t(48727),l=t(3641),i=t(44508),d=t(10244);let c=i.forwardRef(({className:e,...r},t)=>(0,l.jsx)("div",{className:"relative w-full overflow-auto",children:(0,l.jsx)("table",{ref:t,className:(0,d.cn)("w-full caption-bottom text-sm",e),...r})}));c.displayName="Table";let m=i.forwardRef(({className:e,...r},t)=>(0,l.jsx)("thead",{ref:t,className:(0,d.cn)("[&_tr]:border-b",e),...r}));m.displayName="TableHeader";let h=i.forwardRef(({className:e,...r},t)=>(0,l.jsx)("tbody",{ref:t,className:(0,d.cn)("[&_tr:last-child]:border-0",e),...r}));h.displayName="TableBody",i.forwardRef(({className:e,...r},t)=>(0,l.jsx)("tfoot",{ref:t,className:(0,d.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...r})).displayName="TableFooter";let u=i.forwardRef(({className:e,...r},t)=>(0,l.jsx)("tr",{ref:t,className:(0,d.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...r}));u.displayName="TableRow";let x=i.forwardRef(({className:e,...r},t)=>(0,l.jsx)("th",{ref:t,className:(0,d.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r}));x.displayName="TableHead";let f=i.forwardRef(({className:e,...r},t)=>(0,l.jsx)("td",{ref:t,className:(0,d.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r}));f.displayName="TableCell";let g=i.forwardRef(({className:e,...r},t)=>(0,l.jsx)("caption",{ref:t,className:(0,d.cn)("mt-4 text-sm text-muted-foreground",e),...r}));g.displayName="TableCaption"},64624:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Dialog:()=>i,DialogClose:()=>m,DialogContent:()=>u,DialogDescription:()=>p,DialogFooter:()=>f,DialogHeader:()=>x,DialogOverlay:()=>h,DialogPortal:()=>c,DialogTitle:()=>g,DialogTrigger:()=>d});var o=t(3641),s=t(44508),n=t(83991),a=t(20574),l=t(10244);let i=n.bL,d=n.l9,c=n.ZL,m=n.bm,h=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)(n.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...r}));h.displayName=n.hJ.displayName;let u=s.forwardRef(({className:e,children:r,...t},s)=>(0,o.jsxs)(c,{children:[(0,o.jsx)(h,{}),(0,o.jsxs)(n.UC,{ref:s,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[r,(0,o.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,o.jsx)(a.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=n.UC.displayName;let x=({className:e,...r})=>(0,o.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...r});x.displayName="DialogHeader";let f=({className:e,...r})=>(0,o.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...r});f.displayName="DialogFooter";let g=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)(n.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...r}));g.displayName=n.hE.displayName;let p=s.forwardRef(({className:e,...r},t)=>(0,o.jsx)(n.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...r}));p.displayName=n.VY.displayName},70227:(e,r,t)=>{"use strict";t.r(r),t.d(r,{NavBar:()=>o});let o=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call NavBar() from the server but NavBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\chromes\\nav-bar.tsx","NavBar")},84645:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Footer:()=>u});var o=t(3641),s=t(44508),n=t(40644),a=t.n(n),l=t(19876),i=t(84054),d=t(96841),c=t(35260),m=t(52214),h=t(60391);function u(){let[e,r]=(0,s.useState)(!1),t=(0,s.useRef)(null),n=(0,m.p)();return(0,o.jsx)("footer",{className:"border-t border-border bg-background",children:(0,o.jsxs)("div",{className:"mx-auto max-w-7xl px-6 pb-6 pt-12 md:pb-8 md:pt-16 lg:px-8",children:[(0,o.jsxs)("div",{className:"xl:grid xl:grid-cols-3 xl:gap-8",children:[(0,o.jsxs)("div",{className:"space-y-8",children:[(0,o.jsx)("div",{className:"flex items-center",children:(0,o.jsx)(l.default,{src:n,alt:"Roo Code Logo",width:120,height:40,className:"h-6 w-auto"})}),(0,o.jsx)("p",{className:"max-w-md text-sm leading-6 text-muted-foreground md:pr-16 lg:pr-32",children:"Empowering developers to build better software faster with AI-powered tools and insights."}),(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsxs)("a",{href:c.m.GITHUB,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground transition-colors hover:text-foreground",children:[(0,o.jsx)(d.hL4,{className:"h-6 w-6"}),(0,o.jsx)("span",{className:"sr-only",children:"GitHub"})]}),(0,o.jsxs)("a",{href:c.m.DISCORD,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground transition-colors hover:text-foreground",children:[(0,o.jsx)(d.O4U,{className:"h-6 w-6"}),(0,o.jsx)("span",{className:"sr-only",children:"Discord"})]}),(0,o.jsxs)("a",{href:c.m.REDDIT,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground transition-colors hover:text-foreground",children:[(0,o.jsx)(d.oDE,{className:"h-6 w-6"}),(0,o.jsx)("span",{className:"sr-only",children:"Reddit"})]}),(0,o.jsxs)("a",{href:c.m.X,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground transition-colors hover:text-foreground",children:[(0,o.jsx)(d.TCj,{className:"h-6 w-6"}),(0,o.jsx)("span",{className:"sr-only",children:"X"})]}),(0,o.jsxs)("a",{href:c.m.LINKEDIN,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground transition-colors hover:text-foreground",children:[(0,o.jsx)(d.QEs,{className:"h-6 w-6"}),(0,o.jsx)("span",{className:"sr-only",children:"LinkedIn"})]}),(0,o.jsxs)("a",{href:c.m.TIKTOK,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground transition-colors hover:text-foreground",children:[(0,o.jsx)(d.kkU,{className:"h-6 w-6"}),(0,o.jsx)("span",{className:"sr-only",children:"TikTok"})]}),(0,o.jsxs)("a",{href:c.m.BLUESKY,target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground transition-colors hover:text-foreground",children:[(0,o.jsx)(d.fr_,{className:"h-6 w-6"}),(0,o.jsx)("span",{className:"sr-only",children:"Bluesky"})]})]})]}),(0,o.jsxs)("div",{className:"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0",children:[(0,o.jsxs)("div",{className:"md:grid md:grid-cols-2 md:gap-8",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-sm font-semibold uppercase leading-6 text-foreground",children:"Product"}),(0,o.jsxs)("ul",{className:"mt-6 space-y-4",children:[(0,o.jsx)("li",{children:(0,o.jsx)(h.sb,{targetId:"features",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Features"})}),(0,o.jsx)("li",{children:(0,o.jsx)(a(),{href:"/enterprise",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Enterprise"})}),(0,o.jsx)("li",{children:(0,o.jsx)(h.sb,{targetId:"testimonials",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Testimonials"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:c.m.INTEGRATIONS,target:"_blank",rel:"noopener noreferrer",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Integrations"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:c.m.CHANGELOG,target:"_blank",rel:"noopener noreferrer",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Changelog"})})]})]}),(0,o.jsxs)("div",{className:"mt-10 md:mt-0",children:[(0,o.jsx)("h3",{className:"text-sm font-semibold uppercase leading-6 text-foreground",children:"Resources"}),(0,o.jsxs)("ul",{className:"mt-6 space-y-4",children:[(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:c.m.DOCUMENTATION,target:"_blank",rel:"noopener noreferrer",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Documentation"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:c.m.TUTORIALS,target:"_blank",rel:"noopener noreferrer",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Tutorials"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:c.m.COMMUNITY,target:"_blank",rel:"noopener noreferrer",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Community"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:c.m.DISCORD,target:"_blank",rel:"noopener noreferrer",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Discord"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:c.m.REDDIT,target:"_blank",rel:"noopener noreferrer",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Reddit"})})]})]})]}),(0,o.jsxs)("div",{className:"md:grid md:grid-cols-2 md:gap-8",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-sm font-semibold uppercase leading-6 text-foreground",children:"Support"}),(0,o.jsxs)("ul",{className:"mt-6 space-y-4",children:[(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:c.m.ISSUES,target:"_blank",rel:"noopener noreferrer",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Issues"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:c.m.FEATURE_REQUESTS,target:"_blank",rel:"noopener noreferrer",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Feature Requests"})}),(0,o.jsx)("li",{children:(0,o.jsx)(h.sb,{targetId:"faq",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"FAQ"})})]})]}),(0,o.jsxs)("div",{className:"mt-10 md:mt-0",children:[(0,o.jsx)("h3",{className:"text-sm font-semibold uppercase leading-6 text-foreground",children:"Company"}),(0,o.jsxs)("ul",{className:"mt-6 space-y-4",children:[(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:"mailto:<EMAIL>",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Contact"})}),(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:c.m.CAREERS,target:"_blank",rel:"noopener noreferrer",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Careers"})}),(0,o.jsx)("li",{children:(0,o.jsx)(a(),{href:"/terms",className:"text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground",children:"Terms of Service"})}),(0,o.jsx)("li",{children:(0,o.jsxs)("div",{className:"relative z-10",ref:t,children:[(0,o.jsxs)("button",{onClick:()=>r(!e),className:"flex items-center text-sm leading-6 text-muted-foreground transition-colors hover:text-foreground","aria-expanded":e,"aria-haspopup":"true",children:[(0,o.jsxs)("span",{children:["Privacy ",(0,o.jsx)("span",{className:"max-[320px]:hidden",children:"Policy"})]}),(0,o.jsx)(i.A,{className:`ml-1 h-4 w-4 transition-transform ${e?"rotate-180":""}`})]}),e&&(0,o.jsx)("div",{className:"absolute z-50 mt-2 w-44 origin-top-left scale-95 rounded-md border border-border bg-background shadow-lg ring-1 ring-black ring-opacity-5 transition-all duration-100 ease-out data-[state=open]:scale-100 max-xs:right-0 max-xs:origin-top-right xs:left-0",children:(0,o.jsxs)("div",{className:"flex flex-col gap-1 p-2 text-sm text-muted-foreground",children:[(0,o.jsx)("a",{href:c.m.PRIVACY_POLICY_EXTENSION,target:"_blank",rel:"noopener noreferrer",onClick:()=>r(!1),className:"rounded-md px-3 py-2 transition-colors hover:bg-accent/50 hover:text-foreground",children:"Extension"}),(0,o.jsx)(a(),{href:c.q.PRIVACY_POLICY_WEBSITE,onClick:()=>r(!1),className:"rounded-md px-3 py-2 transition-colors hover:bg-accent/50 hover:text-foreground",children:"Roo Code Cloud"})]})})]})})]})]})]})]})]}),(0,o.jsx)("div",{className:"mt-16 flex border-t border-border pt-8 sm:mt-20 lg:mt-24",children:(0,o.jsxs)("p",{className:"mx-auto text-sm leading-5 text-muted-foreground",children:["\xa9 ",new Date().getFullYear()," Roo Code. All rights reserved."]})})]})})}},88773:()=>{},91993:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>o});let o=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\providers\\providers.tsx","Providers")},95456:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var o=t(3641),s=t(44508),n=t(64925),a=t(23334),l=t(60391);function i(){let{theme:e,setTheme:r}=(0,n.D)(),[t,i]=(0,s.useState)(!1);return t?(0,o.jsx)(l.$n,{variant:"ghost",size:"icon",onClick:()=>r("dark"===e?"light":"dark"),className:"h-9 w-9","aria-label":"Toggle theme",children:"dark"===e?(0,o.jsx)(a.XQC,{className:"h-4 w-4 transition-all"}):(0,o.jsx)(a.AGu,{className:"h-4 w-4 transition-all"})}):(0,o.jsx)(l.$n,{variant:"ghost",size:"icon",disabled:!0,className:"h-9 w-9",children:(0,o.jsx)(a.XQC,{className:"h-4 w-4"})})}},99115:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var o=t(3641),s=t(44508),n=t(82697),a=t(28377),l=t(10244);let i=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...a},d)=>{let c=s?n.DX:"button";return(0,o.jsx)(c,{className:(0,l.cn)(i({variant:r,size:t,className:e})),ref:d,...a})});d.displayName="Button"}};