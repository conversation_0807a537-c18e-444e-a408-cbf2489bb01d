{"extension": {"name": "Roo Code", "description": "<PERSON><PERSON> compleet ontwi<PERSON><PERSON><PERSON><PERSON> van AI-agenten in je editor."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "mrd"}, "welcome": "<PERSON><PERSON><PERSON>, {{name}}! Je hebt {{count}} meldingen.", "items": {"zero": "Geen items", "one": "Eén item", "other": "{{count}} items"}, "confirmation": {"reset_state": "Weet je zeker dat je alle status en geheime opslag in de extensie wilt resetten? Dit kan niet ongedaan worden gemaakt.", "delete_config_profile": "Weet je zeker dat je dit configuratieprofiel wilt verwijderen?", "delete_custom_mode": "Weet je zeker dat je deze aangepaste modus wilt verwijderen?", "delete_message": "Wat wil je verwijderen?", "just_this_message": "Alleen dit bericht", "this_and_subsequent": "Dit en alle volgende berichten"}, "errors": {"invalid_data_uri": "Ongeldig data-URI-formaat", "error_copying_image": "Fout bij kop<PERSON><PERSON><PERSON>: {{errorMessage}}", "error_saving_image": "Fout bij <PERSON><PERSON><PERSON> van <PERSON>: {{errorMessage}}", "error_opening_image": "Fout bij openen van afbeelding: {{error}}", "could_not_open_file": "Kon bestand niet openen: {{errorMessage}}", "could_not_open_file_generic": "Kon bestand niet openen!", "checkpoint_timeout": "Time-out bij het hers<PERSON><PERSON> van checkpoint.", "checkpoint_failed": "Herstellen van checkpoint mislukt.", "no_workspace": "Open eerst een projectmap", "update_support_prompt": "Bijwerken van ondersteuningsprompt mislukt", "reset_support_prompt": "<PERSON><PERSON><PERSON> van ondersteuningsprompt mislukt", "enhance_prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> van prompt mislukt", "get_system_prompt": "<PERSON><PERSON><PERSON> van <PERSON> mislukt", "search_commits": "<PERSON><PERSON> naar commits mislukt", "save_api_config": "<PERSON><PERSON><PERSON> van API-configuratie mis<PERSON>t", "create_api_config": "Aanmaken van API-configuratie mis<PERSON>t", "rename_api_config": "<PERSON><PERSON><PERSON><PERSON> van <PERSON>-configuratie mis<PERSON>t", "load_api_config": "<PERSON><PERSON> van <PERSON>-configuratie mis<PERSON>t", "delete_api_config": "Verwijderen van API-configuratie mislukt", "list_api_config": "<PERSON><PERSON><PERSON> met API-configuraties mislukt", "update_server_timeout": "Bijwerken van server-timeout mislukt", "hmr_not_running": "Lokale ontwikkelserver d<PERSON>ait niet, HMR werkt niet. Voer 'npm run dev' uit voordat je de extensie start om HMR in te schakelen.", "retrieve_current_mode": "Fout: <PERSON><PERSON><PERSON> van h<PERSON> modus uit status mislukt.", "failed_delete_repo": "Verwijderen van gekoppelde schaduwrepository of branch mislukt: {{error}}", "failed_remove_directory": "Verwij<PERSON><PERSON> van taakmap mislukt: {{error}}", "custom_storage_path_unusable": "Aangepast opslagpad \"{{path}}\" is onb<PERSON><PERSON><PERSON>ar, standaardpad wordt gebruikt", "cannot_access_path": "Kan pad {{path}} niet openen: {{error}}", "settings_import_failed": "Importeren van instellingen mislukt: {{error}}.", "mistake_limit_guidance": "Dit kan duiden op een fout in het denkproces van het model of het onvermogen om een tool correct te gebruiken, wat kan worden verminderd met gebruikersbegeleiding (bijv. \"<PERSON><PERSON>r de taak op te delen in kleinere stappen\").", "violated_organization_allowlist": "Taak uitvoeren mislukt: het huidige profiel schendt de instellingen van uw organisatie", "condense_failed": "Comprim<PERSON><PERSON> van <PERSON> mislukt", "condense_not_enough_messages": "<PERSON>et genoeg berichten om context te comprimeren", "condensed_recently": "Context is recent gecomprimeerd; deze poging wordt overgeslagen", "condense_handler_invalid": "API-handler voor het comprimeren van context is ongeldig", "condense_context_grew": "Contextgrootte nam toe tijdens comprimeren; deze poging wordt overgeslagen", "share_task_failed": "<PERSON><PERSON> van ta<PERSON> mislukt", "share_no_active_task": "<PERSON><PERSON> actieve taak om te delen", "share_auth_required": "Authenticatie vereist. Log in om taken te delen.", "share_not_enabled": "Taken delen is niet ingeschakeld voor deze organisatie.", "share_task_not_found": "<PERSON><PERSON> niet gevonden of toegang geweigerd."}, "warnings": {"no_terminal_content": "<PERSON><PERSON> <PERSON><PERSON> gese<PERSON>d", "missing_task_files": "De bestanden van deze taak ontbreken. Wil je deze uit de takenlijst verwijderen?"}, "info": {"no_changes": "Geen wijzigingen gevonden.", "clipboard_copy": "Systeemprompt succesvol gekopieerd naar klembord", "history_cleanup": "{{count}} taak/taken met ontbrekende bestanden uit geschiedenis verwijderd.", "custom_storage_path_set": "Aangepast opslagpad ingesteld: {{path}}", "default_storage_path": "Terug naar standaard opslagpad", "settings_imported": "Instellingen succesvol geïmporteerd.", "share_link_copied": "Deellink gekopieerd naar klembord", "image_copied_to_clipboard": "Afbeelding data-URI gekopieerd naar klembord", "image_saved": "Afbeelding opgeslagen naar {{path}}", "organization_share_link_copied": "Organisatie deel-link gekopieerd naar klembord!", "public_share_link_copied": "Openbare deel-link gekopieerd naar klembord!"}, "answers": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Verwijderen", "keep": "Behouden"}, "tasks": {"canceled": "Taakfout: gestopt en geannuleerd door gebruiker.", "deleted": "Taakfout: gestopt en verwijderd door gebruiker."}, "storage": {"prompt_custom_path": "Voer een aangepast opslagpad voor gespreksgeschiedenis in, laat leeg voor standaardlocatie", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Voer een absoluut pad in (bijv. D:\\RooCodeStorage of /home/<USER>/storage)", "enter_valid_path": "<PERSON>oer een geldig pad in"}, "input": {"task_prompt": "Wat moet Roo doen?", "task_placeholder": "<PERSON>p hier je taak"}, "mdm": {"errors": {"cloud_auth_required": "Je organisatie vereist Roo Code Cloud-authenticatie. Log in om door te gaan.", "organization_mismatch": "<PERSON> moet geauthe<PERSON><PERSON><PERSON>jn met het Roo Code Cloud-account van je organisatie.", "verification_failed": "Kan organisatie-authenticatie niet veri<PERSON>."}}}