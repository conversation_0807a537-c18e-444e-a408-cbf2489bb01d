(()=>{var e={};e.id=877,e.ids=[877],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9869:()=>{},10616:(e,r,o)=>{"use strict";o.r(r),o.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=o(88253),t=o(21418),d=o(52052),n=o.n(d),a=o(75779),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);o.d(r,i);let l={children:["",{children:["privacy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,61599)),"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\app\\privacy\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,36295)),"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,64544,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(o.t.bind(o,20441,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(o.t.bind(o,93670,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\app\\privacy\\page.tsx"],x={require:o,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/privacy/page",pathname:"/privacy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},57133:()=>{},61599:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>d,metadata:()=>t});var s=o(90811);let t={title:"Privacy Policy - Roo Code",description:"Privacy policy for Roo Code Cloud and marketing website. Learn how we handle your data and protect your privacy."};function d(){return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-12 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"prose prose-lg mx-auto max-w-4xl dark:prose-invert",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl",children:"Roo Code Cloud Privacy Policy"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Last Updated: June 19, 2025"}),(0,s.jsx)("p",{className:"lead",children:'This Privacy Policy explains how Roo Code, Inc. ("Roo Code," "we," "our," or "us") collects, uses, and shares information when you:'}),(0,s.jsxs)("ul",{className:"lead",children:[(0,s.jsxs)("li",{children:["browse any page under ",(0,s.jsx)("strong",{children:"roocode.com"})," (the ",(0,s.jsx)("em",{children:"Marketing Site"}),"); and/or"]}),(0,s.jsxs)("li",{children:["create an account for, sign in to, or otherwise use ",(0,s.jsx)("strong",{children:"Roo Code Cloud"})," at"," ",(0,s.jsx)("strong",{children:"app.roocode.com"})," or through the Roo Code extension while authenticated to that Cloud account (the ",(0,s.jsx)("em",{children:"Cloud Service"}),")."]})]}),(0,s.jsxs)("div",{className:"my-8 rounded-lg border border-border bg-muted/50 p-6",children:[(0,s.jsx)("h3",{className:"mt-0 text-lg font-semibold",children:"Extension‑Only Usage"}),(0,s.jsxs)("p",{className:"mb-0",children:["If you run the Roo Code extension ",(0,s.jsx)("strong",{children:"without"})," connecting to a Cloud account, your data is governed by the standalone"," ",(0,s.jsx)("a",{href:"https://github.com/RooCodeInc/Roo-Code/blob/main/PRIVACY.md",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Roo Code Extension Privacy Policy"}),"."]})]}),(0,s.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"Quick Summary"}),(0,s.jsxs)("ul",{children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Your source code never transits Roo Code servers."})," It stays on your device and is sent ",(0,s.jsx)("strong",{children:"directly"}),"—via a client‑to‑provider TLS connection—to the third‑party AI model you select. Roo Code never stores, inspects, or trains on your code."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Prompts and chat snippets are collected by default"})," in Roo Code Cloud so you can search and re‑use past conversations. Organization admins can disable this collection at any time."]}),(0,s.jsxs)("li",{children:["We collect only the data needed to operate Roo Code Cloud, do ",(0,s.jsx)("strong",{children:"not"})," sell customer data, and do ",(0,s.jsx)("strong",{children:"not"})," use your content to train models."]})]}),(0,s.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"1. Information We Collect"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full border-collapse border border-border",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"bg-muted/50",children:[(0,s.jsx)("th",{className:"border border-border px-4 py-2 text-left font-semibold",children:"Category"}),(0,s.jsx)("th",{className:"border border-border px-4 py-2 text-left font-semibold",children:"Examples"}),(0,s.jsx)("th",{className:"border border-border px-4 py-2 text-left font-semibold",children:"Source"})]})}),(0,s.jsxs)("tbody",{children:[(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Account Information"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Name, email, organization, auth tokens"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"You"})]}),(0,s.jsxs)("tr",{className:"bg-muted/25",children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Workspace Configuration"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Org settings, allow‑lists, rules files, modes, dashboards"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"You / Extension (when signed in)"})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Prompts, Chat Snippets & Token Counts"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Text prompts, model outputs, token counts"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Extension (when signed in)"})]}),(0,s.jsxs)("tr",{className:"bg-muted/25",children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Usage Data"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Feature clicks, error logs, performance metrics (captured via PostHog)"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Services automatically (PostHog)"})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Payment Data"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Tokenized card details, billing address, invoices"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Payment processor (Stripe)"})]}),(0,s.jsxs)("tr",{className:"bg-muted/25",children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Marketing Data"}),(0,s.jsxs)("td",{className:"border border-border px-4 py-2",children:["Cookies, IP address, browser type, page views,"," ",(0,s.jsx)("strong",{children:"voluntary form submissions"})," (e.g., newsletter or wait‑list sign‑ups)"]}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Marketing Site automatically / You"})]})]})]})}),(0,s.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"2. How We Use Information"}),(0,s.jsxs)("ul",{children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Operate & secure Roo Code Cloud"})," (authentication, completions, abuse prevention)"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Provide support & improve features"})," (debugging, analytics, product decisions)"]}),(0,s.jsx)("li",{children:(0,s.jsx)("strong",{children:"Process payments & manage subscriptions"})}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Send product updates and roadmap communications"})," (opt‑out available)"]})]}),(0,s.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"3. Where Your Data Goes (And Doesn't)"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full border-collapse border border-border",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"bg-muted/50",children:[(0,s.jsx)("th",{className:"border border-border px-4 py-2 text-left font-semibold",children:"Data"}),(0,s.jsx)("th",{className:"border border-border px-4 py-2 text-left font-semibold",children:"Sent To"}),(0,s.jsxs)("th",{className:"border border-border px-4 py-2 text-left font-semibold",children:[(0,s.jsx)("strong",{children:"Not"})," Sent To"]})]})}),(0,s.jsxs)("tbody",{children:[(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Code & files you work on"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Your chosen model provider (direct client → provider TLS)"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Roo Code servers; ad networks; model‑training pipelines"})]}),(0,s.jsxs)("tr",{className:"bg-muted/25",children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Prompts, chat snippets & token counts (Cloud)"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Roo Code Cloud (encrypted at rest)"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Any third‑party"})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Workspace Configuration"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Roo Code Cloud (encrypted at rest)"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Any third-party"})]}),(0,s.jsxs)("tr",{className:"bg-muted/25",children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Usage & Telemetry"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"PostHog (self‑hosted analytics platform)"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Ad networks or data brokers"})]}),(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"border border-border px-4 py-2 font-medium",children:"Payment Data"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Stripe (PCI‑DSS Level 1)"}),(0,s.jsx)("td",{className:"border border-border px-4 py-2",children:"Roo Code servers (we store only the Stripe customer ID)"})]})]})]})}),(0,s.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"4. Data Retention"}),(0,s.jsxs)("ul",{children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Source Code:"})," Never stored on Roo Code servers."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Prompts & Chat Snippets:"})," Persist in your Cloud workspace until you or your organization admin deletes them or disables collection."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Operational Logs & Analytics:"})," Retained only as needed to operate and secure Roo Code Cloud."]})]}),(0,s.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"5. Your Choices"}),(0,s.jsxs)("ul",{children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Manage cookies:"})," You can block or delete cookies in your browser settings; some site features may not function without them."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Disable prompt collection"})," in Organization settings."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Delete your Cloud account"})," at any time from"," ",(0,s.jsx)("strong",{children:"Security Settings"})," inside Roo Code Cloud."]})]}),(0,s.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"6. Security Practices"}),(0,s.jsx)("p",{children:"We use TLS for all data in transit, AES‑256 encryption at rest, least‑privilege IAM, continuous monitoring, routine penetration testing, and maintain a SOC 2 program."}),(0,s.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"7. Updates to This Policy"}),(0,s.jsxs)("p",{children:["If our privacy practices change, we will update this policy and note the new"," ",(0,s.jsx)("strong",{children:"Last Updated"})," date at the top. For material changes that affect Cloud workspaces, we will also email registered workspace owners before the changes take effect."]}),(0,s.jsx)("h2",{className:"mt-12 text-2xl font-bold",children:"8. Contact Us"}),(0,s.jsxs)("p",{children:["Questions or concerns? Email"," ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-primary hover:underline",children:"<EMAIL>"}),"."]})]})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),s=r.X(0,[595,570,153],()=>o(10616));module.exports=s})();