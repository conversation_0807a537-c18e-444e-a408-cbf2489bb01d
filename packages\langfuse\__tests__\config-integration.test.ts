import { describe, it, expect, beforeEach, afterEach, vi } from "vitest"
import * as vscode from "vscode"
import { LangfuseConfigManager, LANGFUSE_CONFIG_KEYS, DEFAULT_LANGFUSE_CONFIG } from "../src/config-integration"

// Mock VSCode API
vi.mock("vscode", () => ({
	workspace: {
		getConfiguration: vi.fn(),
		onDidChangeConfiguration: vi.fn(),
	},
	ConfigurationTarget: {
		Global: 1,
		Workspace: 2,
		WorkspaceFolder: 3,
	},
	Disposable: vi.fn().mockImplementation((fn) => ({ dispose: fn })),
}))

describe("LangfuseConfigManager", () => {
	let configManager: LangfuseConfigManager
	let mockContext: vscode.ExtensionContext
	let mockConfiguration: any
	let mockConfigChangeListeners: Array<(e: any) => void> = []

	beforeEach(() => {
		// Reset environment variables
		delete process.env.LANGFUSE_ENABLED
		delete process.env.LANGFUSE_PUBLIC_KEY
		delete process.env.LANGFUSE_SECRET_KEY
		delete process.env.LANGFUSE_BASE_URL
		delete process.env.LANGFUSE_MAX_CONTENT_LENGTH

		// Mock configuration
		mockConfiguration = {
			get: vi.fn().mockImplementation((key, defaultValue) => defaultValue),
			update: vi.fn().mockResolvedValue(undefined),
		}

		// Mock workspace configuration
		vi.mocked(vscode.workspace.getConfiguration).mockReturnValue(mockConfiguration)

		// Mock configuration change listener
		vi.mocked(vscode.workspace.onDidChangeConfiguration).mockImplementation((listener) => {
			mockConfigChangeListeners.push(listener)
			return new vscode.Disposable(() => {
				const index = mockConfigChangeListeners.indexOf(listener)
				if (index >= 0) {
					mockConfigChangeListeners.splice(index, 1)
				}
			})
		})

		// Mock extension context
		mockContext = {
			subscriptions: [],
		} as any

		configManager = new LangfuseConfigManager(mockContext)
	})

	afterEach(() => {
		vi.clearAllMocks()
		mockConfigChangeListeners = []
	})

	describe("getCurrentConfig", () => {
		it("should return default config when no settings are set", () => {
			const config = configManager.getCurrentConfig()
			expect(config).toEqual(DEFAULT_LANGFUSE_CONFIG)
		})

		it("should merge settings with defaults", () => {
			mockConfiguration.get.mockImplementation((key, defaultValue) => {
				if (key === LANGFUSE_CONFIG_KEYS.enabled) return true
				if (key === LANGFUSE_CONFIG_KEYS.maxContentLength) return 2000
				return defaultValue
			})

			const config = configManager.getCurrentConfig()
			expect(config.enabled).toBe(true)
			expect(config.maxContentLength).toBe(2000)
			expect(config.traceLlmCalls).toBe(DEFAULT_LANGFUSE_CONFIG.traceLlmCalls)
		})

		it("should prioritize environment variables over settings", () => {
			process.env.LANGFUSE_ENABLED = "true"
			process.env.LANGFUSE_PUBLIC_KEY = "env-public-key"
			process.env.LANGFUSE_SECRET_KEY = "env-secret-key"
			process.env.LANGFUSE_BASE_URL = "https://env.langfuse.com"
			process.env.LANGFUSE_MAX_CONTENT_LENGTH = "5000"

			mockConfiguration.get.mockImplementation((key, defaultValue) => {
				if (key === LANGFUSE_CONFIG_KEYS.enabled) return false
				if (key === LANGFUSE_CONFIG_KEYS.publicKey) return "settings-public-key"
				if (key === LANGFUSE_CONFIG_KEYS.secretKey) return "settings-secret-key"
				if (key === LANGFUSE_CONFIG_KEYS.baseUrl) return "https://settings.langfuse.com"
				if (key === LANGFUSE_CONFIG_KEYS.maxContentLength) return 1000
				return defaultValue
			})

			const config = configManager.getCurrentConfig()
			expect(config.enabled).toBe(true) // From env
			expect(config.publicKey).toBe("env-public-key") // From env
			expect(config.secretKey).toBe("env-secret-key") // From env
			expect(config.baseUrl).toBe("https://env.langfuse.com") // From env
			expect(config.maxContentLength).toBe(5000) // From env
		})

		it("should validate config and throw on invalid data", () => {
			mockConfiguration.get.mockImplementation((key, defaultValue) => {
				if (key === LANGFUSE_CONFIG_KEYS.maxContentLength) return -1 // Invalid
				return defaultValue
			})

			expect(() => configManager.getCurrentConfig()).toThrow("Invalid Langfuse configuration")
		})
	})

	describe("updateConfig", () => {
		it("should update configuration value", async () => {
			await configManager.updateConfig("enabled", true)
			
			expect(mockConfiguration.update).toHaveBeenCalledWith(
				LANGFUSE_CONFIG_KEYS.enabled,
				true,
				vscode.ConfigurationTarget.Global
			)
		})

		it("should update with custom target", async () => {
			await configManager.updateConfig("maxContentLength", 2000, vscode.ConfigurationTarget.Workspace)
			
			expect(mockConfiguration.update).toHaveBeenCalledWith(
				LANGFUSE_CONFIG_KEYS.maxContentLength,
				2000,
				vscode.ConfigurationTarget.Workspace
			)
		})

		it("should throw error for unknown config key", async () => {
			await expect(
				configManager.updateConfig("unknownKey" as any, "value")
			).rejects.toThrow("Unknown configuration key")
		})
	})

	describe("resetConfig", () => {
		it("should reset all configuration values", async () => {
			await configManager.resetConfig()
			
			// Should call update for each config key
			const configKeys = Object.values(LANGFUSE_CONFIG_KEYS)
			expect(mockConfiguration.update).toHaveBeenCalledTimes(configKeys.length)
			
			configKeys.forEach(key => {
				expect(mockConfiguration.update).toHaveBeenCalledWith(
					key,
					undefined,
					vscode.ConfigurationTarget.Global
				)
			})
		})

		it("should reset with custom target", async () => {
			await configManager.resetConfig(vscode.ConfigurationTarget.Workspace)
			
			const configKeys = Object.values(LANGFUSE_CONFIG_KEYS)
			configKeys.forEach(key => {
				expect(mockConfiguration.update).toHaveBeenCalledWith(
					key,
					undefined,
					vscode.ConfigurationTarget.Workspace
				)
			})
		})
	})

	describe("configuration change handling", () => {
		it("should register configuration change listener", () => {
			expect(vscode.workspace.onDidChangeConfiguration).toHaveBeenCalled()
			expect(mockContext.subscriptions).toHaveLength(1)
		})

		it("should call listeners when Langfuse config changes", () => {
			const listener = vi.fn()
			const disposable = configManager.onConfigChange(listener)

			// Simulate configuration change
			const changeEvent = {
				affectsConfiguration: vi.fn().mockImplementation((key) => 
					key === LANGFUSE_CONFIG_KEYS.enabled
				),
			}

			mockConfigChangeListeners.forEach(l => l(changeEvent))

			expect(listener).toHaveBeenCalledWith(expect.any(Object))

			disposable.dispose()
		})

		it("should not call listeners when non-Langfuse config changes", () => {
			const listener = vi.fn()
			configManager.onConfigChange(listener)

			// Simulate configuration change for non-Langfuse setting
			const changeEvent = {
				affectsConfiguration: vi.fn().mockReturnValue(false),
			}

			mockConfigChangeListeners.forEach(l => l(changeEvent))

			expect(listener).not.toHaveBeenCalled()
		})

		it("should handle listener errors gracefully", () => {
			const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {})
			const errorListener = vi.fn().mockImplementation(() => {
				throw new Error("Listener error")
			})
			
			configManager.onConfigChange(errorListener)

			// Simulate configuration change
			const changeEvent = {
				affectsConfiguration: vi.fn().mockReturnValue(true),
			}

			expect(() => {
				mockConfigChangeListeners.forEach(l => l(changeEvent))
			}).not.toThrow()

			expect(consoleSpy).toHaveBeenCalledWith(
				expect.stringContaining("Error in config change listener"),
				expect.any(Error)
			)

			consoleSpy.mockRestore()
		})

		it("should remove listeners when disposed", () => {
			const listener = vi.fn()
			const disposable = configManager.onConfigChange(listener)

			disposable.dispose()

			// Simulate configuration change
			const changeEvent = {
				affectsConfiguration: vi.fn().mockReturnValue(true),
			}

			mockConfigChangeListeners.forEach(l => l(changeEvent))

			expect(listener).not.toHaveBeenCalled()
		})
	})

	describe("validateCurrentConfig", () => {
		it("should return valid for correct config", () => {
			mockConfiguration.get.mockImplementation((key, defaultValue) => {
				if (key === LANGFUSE_CONFIG_KEYS.enabled) return true
				if (key === LANGFUSE_CONFIG_KEYS.publicKey) return "test-public-key"
				if (key === LANGFUSE_CONFIG_KEYS.secretKey) return "test-secret-key"
				return defaultValue
			})

			const validation = configManager.validateCurrentConfig()
			expect(validation.isValid).toBe(true)
			expect(validation.issues).toHaveLength(0)
		})

		it("should return issues for incomplete config", () => {
			mockConfiguration.get.mockImplementation((key, defaultValue) => {
				if (key === LANGFUSE_CONFIG_KEYS.enabled) return true
				// Missing publicKey and secretKey
				return defaultValue
			})

			const validation = configManager.validateCurrentConfig()
			expect(validation.isValid).toBe(false)
			expect(validation.issues).toContain("Public key is required when Langfuse is enabled")
			expect(validation.issues).toContain("Secret key is required when Langfuse is enabled")
		})

		it("should validate base URL format", () => {
			mockConfiguration.get.mockImplementation((key, defaultValue) => {
				if (key === LANGFUSE_CONFIG_KEYS.enabled) return true
				if (key === LANGFUSE_CONFIG_KEYS.publicKey) return "test-public-key"
				if (key === LANGFUSE_CONFIG_KEYS.secretKey) return "test-secret-key"
				if (key === LANGFUSE_CONFIG_KEYS.baseUrl) return "invalid-url"
				return defaultValue
			})

			const validation = configManager.validateCurrentConfig()
			console.log("Validation issues:", validation.issues)
			expect(validation.isValid).toBe(false)
			expect(validation.issues).toContain("Base URL must be a valid HTTP/HTTPS URL")
		})

		it("should handle validation errors", () => {
			mockConfiguration.get.mockImplementation(() => {
				throw new Error("Config error")
			})

			const validation = configManager.validateCurrentConfig()
			expect(validation.isValid).toBe(false)
			expect(validation.issues).toContain("Config error")
		})
	})

	describe("getConfigStatus", () => {
		it("should return status for settings-based config", () => {
			mockConfiguration.get.mockImplementation((key, defaultValue) => {
				if (key === LANGFUSE_CONFIG_KEYS.enabled) return true
				if (key === LANGFUSE_CONFIG_KEYS.publicKey) return "test-public-key"
				if (key === LANGFUSE_CONFIG_KEYS.secretKey) return "test-secret-key"
				return defaultValue
			})

			const status = configManager.getConfigStatus()
			expect(status.enabled).toBe(true)
			expect(status.configured).toBe(true)
			expect(status.source).toBe("settings")
			expect(status.issues).toHaveLength(0)
		})

		it("should return status for environment-based config", () => {
			process.env.LANGFUSE_PUBLIC_KEY = "env-public-key"
			process.env.LANGFUSE_SECRET_KEY = "env-secret-key"

			mockConfiguration.get.mockImplementation((key, defaultValue) => {
				if (key === LANGFUSE_CONFIG_KEYS.enabled) return true
				return defaultValue
			})

			const status = configManager.getConfigStatus()
			expect(status.enabled).toBe(true)
			expect(status.configured).toBe(true)
			expect(status.source).toBe("environment")
		})

		it("should return status for mixed config", () => {
			process.env.LANGFUSE_PUBLIC_KEY = "env-public-key"
			// Missing LANGFUSE_SECRET_KEY

			mockConfiguration.get.mockImplementation((key, defaultValue) => {
				if (key === LANGFUSE_CONFIG_KEYS.enabled) return true
				if (key === LANGFUSE_CONFIG_KEYS.secretKey) return "settings-secret-key"
				return defaultValue
			})

			const status = configManager.getConfigStatus()
			expect(status.source).toBe("mixed")
		})

		it("should return disabled status", () => {
			const status = configManager.getConfigStatus()
			expect(status.enabled).toBe(false)
			expect(status.configured).toBe(false)
		})
	})
})
