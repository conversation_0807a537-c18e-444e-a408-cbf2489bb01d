(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[448],{8198:(t,e,s)=>{"use strict";s.d(e,{so:()=>a});var i=s(39582),r=s(34545),n=(0,r.createContext)({client:i.Ay});function a(t){var e=t.children,s=t.client,a=t.apiKey,o=t.options,u=(0,r.useRef)(null),l=(0,r.useMemo)(function(){return s?(a&&console.warn("[PostHog.js] You have provided both `client` and `apiKey` to `PostHogProvider`. `api<PERSON>ey` will be ignored in favour of `client`."),o&&console.warn("[PostHog.js] You have provided both `client` and `options` to `PostHogProvider`. `options` will be ignored in favour of `client`."),s):(a||console.warn("[PostHog.js] No `api<PERSON>ey` or `client` were provided to `PostHogProvider`. Using default global `window.posthog` instance. You must initialize it manually. This is not recommended behavior."),i.Ay)},[s,a,JSON.stringify(o)]);return(0,r.useEffect)(function(){if(!s){var t=u.current;t?(a!==t.apiKey&&console.warn("[PostHog.js] You have provided a different `apiKey` to `PostHogProvider` than the one that was already initialized. This is not supported by our provider and we'll keep using the previous key. If you need to toggle between API Keys you need to control the `client` yourself and pass it in as a prop rather than an `apiKey` prop."),o&&!function t(e,s,i){if(void 0===i&&(i=new WeakMap),e===s)return!0;if("object"!=typeof e||null===e||"object"!=typeof s||null===s)return!1;if(i.has(e)&&i.get(e)===s)return!0;i.set(e,s);var r=Object.keys(e),n=Object.keys(s);if(r.length!==n.length)return!1;for(var a=0;a<r.length;a++){var o=r[a];if(!n.includes(o)||!t(e[o],s[o],i))return!1}return!0}(o,t.options)&&i.Ay.set_config(o)):(i.Ay.__loaded&&console.warn("[PostHog.js] `posthog` was already loaded elsewhere. This may cause issues."),i.Ay.init(a,o)),u.current={apiKey:a,options:null!=o?o:{}}}},[s,a,JSON.stringify(o)]),r.createElement(n.Provider,{value:{client:l}},e)}var o=function(){return useContext(n).client},u=function(t,e){return(u=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s])})(t,e)},l=function(){return(l=Object.assign||function(t){for(var e,s=1,i=arguments.length;s<i;s++)for(var r in e=arguments[s])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function c(t,e){var s={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&0>e.indexOf(i)&&(s[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,i=Object.getOwnPropertySymbols(t);r<i.length;r++)0>e.indexOf(i[r])&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(s[i[r]]=t[i[r]]);return s}var h=function(t){return"function"==typeof t};function d(t){var e=t.flag,s=t.children,i=t.onIntersect,r=t.onClick,n=t.trackView,a=t.options,u=c(t,["flag","children","onIntersect","onClick","trackView","options"]),h=useRef(null);return useEffect(function(){if(null!==h.current&&n){var t=new IntersectionObserver(function(t){return i(t[0])},l({threshold:.1},a));return t.observe(h.current),function(){return t.disconnect()}}},[e,a,o(),h,n,i]),React.createElement("div",l({ref:h},u,{onClick:r}),s)}var f={componentStack:null,error:null},p={INVALID_FALLBACK:"[PostHog.js][PostHogErrorBoundary] Invalid fallback prop, provide a valid React element or a function that returns a valid React element."};!function(t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function e(){this.constructor=s}function s(e){var s=t.call(this,e)||this;return s.state=f,s}u(s,t),s.prototype=null===t?Object.create(t):(e.prototype=t.prototype,new e),s.prototype.componentDidCatch=function(t,e){var s,i=e.componentStack,r=this.props.additionalProperties;this.setState({error:t,componentStack:i}),h(r)?s=r(t):"object"==typeof r&&(s=r),this.context.client.captureException(t,s)},s.prototype.render=function(){var t=this.props,e=t.children,s=t.fallback,i=this.state;if(null==i.componentStack)return h(e)?e():e;var n=h(s)?r.createElement(s,{error:i.error,componentStack:i.componentStack}):s;return r.isValidElement(n)?n:(console.warn(p.INVALID_FALLBACK),r.createElement(r.Fragment,null))},s.contextType=n}(r.Component)},80890:t=>{t.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},83154:(t,e,s)=>{"use strict";s.d(e,{E:()=>g});var i=s(18490),r=s(85283),n=s(69303),a=s(23792),o=class extends a.Q{constructor(t={}){super(),this.config=t,this.#t=new Map}#t;build(t,e,s){let n=e.queryKey,a=e.queryHash??(0,i.F$)(n,e),o=this.get(a);return o||(o=new r.X({client:t,queryKey:n,queryHash:a,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(n)}),this.add(o)),o}add(t){this.#t.has(t.queryHash)||(this.#t.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){let e=this.#t.get(t.queryHash);e&&(t.destroy(),e===t&&this.#t.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){n.jG.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#t.get(t)}getAll(){return[...this.#t.values()]}find(t){let e={exact:!0,...t};return this.getAll().find(t=>(0,i.MK)(e,t))}findAll(t={}){let e=this.getAll();return Object.keys(t).length>0?e.filter(e=>(0,i.MK)(t,e)):e}notify(t){n.jG.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){n.jG.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){n.jG.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},u=s(44254),l=s(73598),c=class extends u.k{#e;#s;#i;constructor(t){super(),this.mutationId=t.mutationId,this.#s=t.mutationCache,this.#e=[],this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#e.includes(t)||(this.#e.push(t),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#e=this.#e.filter(e=>e!==t),this.scheduleGc(),this.#s.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#e.length||("pending"===this.state.status?this.scheduleGc():this.#s.remove(this))}continue(){return this.#i?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#r({type:"continue"})};this.#i=(0,l.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#r({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#r({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#s.canRun(this)});let s="pending"===this.state.status,i=!this.#i.canStart();try{if(s)e();else{this.#r({type:"pending",variables:t,isPaused:i}),await this.#s.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#r({type:"pending",context:e,variables:t,isPaused:i})}let r=await this.#i.start();return await this.#s.config.onSuccess?.(r,t,this.state.context,this),await this.options.onSuccess?.(r,t,this.state.context),await this.#s.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,t,this.state.context),this.#r({type:"success",data:r}),r}catch(e){try{throw await this.#s.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#s.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#r({type:"error",error:e})}}finally{this.#s.runNext(this)}}#r(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),n.jG.batch(()=>{this.#e.forEach(e=>{e.onMutationUpdate(t)}),this.#s.notify({mutation:this,type:"updated",action:t})})}},h=class extends a.Q{constructor(t={}){super(),this.config=t,this.#n=new Set,this.#a=new Map,this.#o=0}#n;#a;#o;build(t,e,s){let i=new c({mutationCache:this,mutationId:++this.#o,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){this.#n.add(t);let e=d(t);if("string"==typeof e){let s=this.#a.get(e);s?s.push(t):this.#a.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#n.delete(t)){let e=d(t);if("string"==typeof e){let s=this.#a.get(e);if(s)if(s.length>1){let e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&this.#a.delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){let e=d(t);if("string"!=typeof e)return!0;{let s=this.#a.get(e),i=s?.find(t=>"pending"===t.state.status);return!i||i===t}}runNext(t){let e=d(t);if("string"!=typeof e)return Promise.resolve();{let s=this.#a.get(e)?.find(e=>e!==t&&e.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){n.jG.batch(()=>{this.#n.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#n.clear(),this.#a.clear()})}getAll(){return Array.from(this.#n)}find(t){let e={exact:!0,...t};return this.getAll().find(t=>(0,i.nJ)(e,t))}findAll(t={}){return this.getAll().filter(e=>(0,i.nJ)(t,e))}notify(t){n.jG.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){let t=this.getAll().filter(t=>t.state.isPaused);return n.jG.batch(()=>Promise.all(t.map(t=>t.continue().catch(i.lQ))))}};function d(t){return t.options.scope?.id}var f=s(52778),p=s(8665);function y(t){return{onFetch:(e,s)=>{let r=e.options,n=e.fetchOptions?.meta?.fetchMore?.direction,a=e.state.data?.pages||[],o=e.state.data?.pageParams||[],u={pages:[],pageParams:[]},l=0,c=async()=>{let s=!1,c=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",()=>{s=!0}),e.signal)})},h=(0,i.ZM)(e.options,e.fetchOptions),d=async(t,r,n)=>{if(s)return Promise.reject();if(null==r&&t.pages.length)return Promise.resolve(t);let a=(()=>{let t={client:e.client,queryKey:e.queryKey,pageParam:r,direction:n?"backward":"forward",meta:e.options.meta};return c(t),t})(),o=await h(a),{maxPages:u}=e.options,l=n?i.ZZ:i.y9;return{pages:l(t.pages,o,u),pageParams:l(t.pageParams,r,u)}};if(n&&a.length){let t="backward"===n,e={pages:a,pageParams:o},s=(t?function(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}:m)(r,e);u=await d(e,s,t)}else{let e=t??a.length;do{let t=0===l?o[0]??r.initialPageParam:m(r,u);if(l>0&&null==t)break;u=await d(u,t),l++}while(l<e)}return u};e.options.persister?e.fetchFn=()=>e.options.persister?.(c,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=c}}}function m(t,{pages:e,pageParams:s}){let i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}var g=class{#u;#s;#l;#c;#h;#d;#f;#p;constructor(t={}){this.#u=t.queryCache||new o,this.#s=t.mutationCache||new h,this.#l=t.defaultOptions||{},this.#c=new Map,this.#h=new Map,this.#d=0}mount(){this.#d++,1===this.#d&&(this.#f=f.m.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#u.onFocus())}),this.#p=p.t.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#u.onOnline())}))}unmount(){this.#d--,0===this.#d&&(this.#f?.(),this.#f=void 0,this.#p?.(),this.#p=void 0)}isFetching(t){return this.#u.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#s.findAll({...t,status:"pending"}).length}getQueryData(t){let e=this.defaultQueryOptions({queryKey:t});return this.#u.get(e.queryHash)?.state.data}ensureQueryData(t){let e=this.defaultQueryOptions(t),s=this.#u.build(this,e),r=s.state.data;return void 0===r?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime((0,i.d2)(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(r))}getQueriesData(t){return this.#u.findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,s){let r=this.defaultQueryOptions({queryKey:t}),n=this.#u.get(r.queryHash),a=n?.state.data,o=(0,i.Zw)(e,a);if(void 0!==o)return this.#u.build(this,r).setData(o,{...s,manual:!0})}setQueriesData(t,e,s){return n.jG.batch(()=>this.#u.findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,s)]))}getQueryState(t){let e=this.defaultQueryOptions({queryKey:t});return this.#u.get(e.queryHash)?.state}removeQueries(t){let e=this.#u;n.jG.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){let s=this.#u;return n.jG.batch(()=>(s.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){let s={revert:!0,...e};return Promise.all(n.jG.batch(()=>this.#u.findAll(t).map(t=>t.cancel(s)))).then(i.lQ).catch(i.lQ)}invalidateQueries(t,e={}){return n.jG.batch(()=>(this.#u.findAll(t).forEach(t=>{t.invalidate()}),t?.refetchType==="none")?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e))}refetchQueries(t,e={}){let s={...e,cancelRefetch:e.cancelRefetch??!0};return Promise.all(n.jG.batch(()=>this.#u.findAll(t).filter(t=>!t.isDisabled()&&!t.isStatic()).map(t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(i.lQ)),"paused"===t.state.fetchStatus?Promise.resolve():e}))).then(i.lQ)}fetchQuery(t){let e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);let s=this.#u.build(this,e);return s.isStaleByTime((0,i.d2)(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(i.lQ).catch(i.lQ)}fetchInfiniteQuery(t){return t.behavior=y(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(i.lQ).catch(i.lQ)}ensureInfiniteQueryData(t){return t.behavior=y(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return p.t.isOnline()?this.#s.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#u}getMutationCache(){return this.#s}getDefaultOptions(){return this.#l}setDefaultOptions(t){this.#l=t}setQueryDefaults(t,e){this.#c.set((0,i.EN)(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){let e=[...this.#c.values()],s={};return e.forEach(e=>{(0,i.Cp)(t,e.queryKey)&&Object.assign(s,e.defaultOptions)}),s}setMutationDefaults(t,e){this.#h.set((0,i.EN)(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){let e=[...this.#h.values()],s={};return e.forEach(e=>{(0,i.Cp)(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;let e={...this.#l.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=(0,i.F$)(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===i.hT&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#l.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#u.clear(),this.#s.clear()}}}}]);