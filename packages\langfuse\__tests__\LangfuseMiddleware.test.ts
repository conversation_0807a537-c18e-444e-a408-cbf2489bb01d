import { describe, it, expect, beforeEach, afterEach, vi } from "vitest"
import { LangfuseMiddleware, wrapApiHandlerBuilder } from "../src/LangfuseMiddleware"
import { LangfuseService } from "../src/LangfuseService"
import type { A<PERSON>Hand<PERSON>, ApiStreamChunk } from "@roo-code/types"

// Mock dependencies
vi.mock("../src/LangfuseService")

describe("LangfuseMiddleware", () => {
	let mockHandler: ApiHandler
	let middleware: LangfuseMiddleware
	let mockLangfuseService: any

	beforeEach(() => {
		// Create mock API handler
		mockHandler = {
			createMessage: vi.fn(),
			getModel: vi.fn().mockReturnValue({
				id: "gpt-4",
				info: { maxTokens: 4096 },
			}),
			countTokens: vi.fn().mockResolvedValue(100),
		}

		// Mock LangfuseService
		mockLangfuseService = {
			isReady: vi.fn().mockReturnValue(true),
			traceLlmCall: vi.fn().mockResolvedValue("trace-id"),
		}
		vi.mocked(LangfuseService.getInstance).mockReturnValue(mockLangfuseService)

		middleware = new LangfuseMiddleware(mockHandler, "test-provider")
	})

	afterEach(() => {
		vi.clearAllMocks()
	})

	describe("createMessage wrapping", () => {
		it("should wrap createMessage and forward chunks", async () => {
			const mockChunks: ApiStreamChunk[] = [
				{ type: "text", text: "Hello" },
				{ type: "text", text: " world" },
				{ type: "usage", inputTokens: 10, outputTokens: 5, cacheWriteTokens: 0, cacheReadTokens: 0 },
			]

			// Mock the original handler to return chunks
			const mockStream = async function* () {
				for (const chunk of mockChunks) {
					yield chunk
				}
			}
			vi.mocked(mockHandler.createMessage).mockReturnValue(mockStream())

			const systemPrompt = "You are a helpful assistant"
			const messages = [{ role: "user" as const, content: "Hello" }]

			// Collect all chunks from the middleware
			const chunks: ApiStreamChunk[] = []
			for await (const chunk of middleware.createMessage(systemPrompt, messages)) {
				chunks.push(chunk)
			}

			// Should forward all chunks
			expect(chunks).toEqual(mockChunks)
			expect(mockHandler.createMessage).toHaveBeenCalledWith(systemPrompt, messages, undefined)
		})

		it("should trace successful LLM calls", async () => {
			const mockChunks: ApiStreamChunk[] = [
				{ type: "text", text: "Response" },
				{ type: "usage", inputTokens: 10, outputTokens: 5, cacheWriteTokens: 0, cacheReadTokens: 0, totalCost: 0.001 },
			]

			const mockStream = async function* () {
				for (const chunk of mockChunks) {
					yield chunk
				}
			}
			vi.mocked(mockHandler.createMessage).mockReturnValue(mockStream())

			const systemPrompt = "You are a helpful assistant"
			const messages = [{ role: "user" as const, content: "Hello" }]

			// Consume the stream
			const chunks: ApiStreamChunk[] = []
			for await (const chunk of middleware.createMessage(systemPrompt, messages)) {
				chunks.push(chunk)
			}

			// Wait for async trace to be sent
			await new Promise(resolve => setTimeout(resolve, 10))

			expect(mockLangfuseService.traceLlmCall).toHaveBeenCalledWith(
				expect.objectContaining({
					model: "gpt-4",
					provider: "test-provider",
					input: expect.objectContaining({
						systemPrompt,
						messages: expect.any(Array),
					}),
					output: expect.objectContaining({
						content: "Response",
						finishReason: "completed",
					}),
					usage: expect.objectContaining({
						inputTokens: 10,
						outputTokens: 5,
						totalTokens: 15,
						cost: 0.001,
					}),
				}),
				expect.objectContaining({
					tags: ["llm", "api-call", "test-provider"],
				})
			)
		})

		it("should trace failed LLM calls", async () => {
			const error = new Error("API Error")
			vi.mocked(mockHandler.createMessage).mockImplementation(async function* () {
				throw error
			})

			const systemPrompt = "You are a helpful assistant"
			const messages = [{ role: "user" as const, content: "Hello" }]

			// Should re-throw the error
			await expect(async () => {
				for await (const chunk of middleware.createMessage(systemPrompt, messages)) {
					// Should not reach here
				}
			}).rejects.toThrow("API Error")

			// Wait for async trace to be sent
			await new Promise(resolve => setTimeout(resolve, 10))

			expect(mockLangfuseService.traceLlmCall).toHaveBeenCalledWith(
				expect.objectContaining({
					error: "Error: API Error",
				}),
				expect.any(Object)
			)
		})

		it("should not trace when Langfuse is not ready", async () => {
			mockLangfuseService.isReady.mockReturnValue(false)

			const mockStream = async function* () {
				yield { type: "text" as const, text: "Response" }
			}
			vi.mocked(mockHandler.createMessage).mockReturnValue(mockStream())

			const systemPrompt = "You are a helpful assistant"
			const messages = [{ role: "user" as const, content: "Hello" }]

			// Consume the stream
			for await (const chunk of middleware.createMessage(systemPrompt, messages)) {
				// Just consume
			}

			// Wait for potential async trace
			await new Promise(resolve => setTimeout(resolve, 10))

			expect(mockLangfuseService.traceLlmCall).not.toHaveBeenCalled()
		})

		it("should handle tracing errors gracefully", async () => {
			const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {})
			mockLangfuseService.traceLlmCall.mockRejectedValue(new Error("Trace error"))

			const mockStream = async function* () {
				yield { type: "text" as const, text: "Response" }
			}
			vi.mocked(mockHandler.createMessage).mockReturnValue(mockStream())

			const systemPrompt = "You are a helpful assistant"
			const messages = [{ role: "user" as const, content: "Hello" }]

			// Should not throw even if tracing fails
			await expect(async () => {
				for await (const chunk of middleware.createMessage(systemPrompt, messages)) {
					// Just consume
				}
			}).resolves.not.toThrow()

			// Wait for async trace to complete
			await new Promise(resolve => setTimeout(resolve, 10))

			expect(consoleSpy).toHaveBeenCalledWith(
				expect.stringContaining("Failed to send trace")
			)

			consoleSpy.mockRestore()
		})
	})

	describe("delegation methods", () => {
		it("should delegate getModel to original handler", () => {
			const result = middleware.getModel()
			expect(result).toEqual({ id: "gpt-4", info: { maxTokens: 4096 } })
			expect(mockHandler.getModel).toHaveBeenCalled()
		})

		it("should delegate countTokens to original handler", async () => {
			const content = [{ type: "text" as const, text: "test" }]
			const result = await middleware.countTokens(content)
			
			expect(result).toBe(100)
			expect(mockHandler.countTokens).toHaveBeenCalledWith(content)
		})
	})

	describe("static utility methods", () => {
		it("should wrap handler with middleware", () => {
			const wrapped = LangfuseMiddleware.wrap(mockHandler, "test-provider")
			expect(wrapped).toBeInstanceOf(LangfuseMiddleware)
		})

		it("should wrap multiple handlers", () => {
			const handlers = {
				openai: mockHandler,
				anthropic: mockHandler,
			}

			const wrapped = LangfuseMiddleware.wrapHandlers(handlers)
			
			expect(wrapped.openai).toBeInstanceOf(LangfuseMiddleware)
			expect(wrapped.anthropic).toBeInstanceOf(LangfuseMiddleware)
		})

		it("should conditionally wrap when Langfuse is ready", () => {
			const wrapped = LangfuseMiddleware.conditionalWrap(mockHandler, "test-provider")
			expect(wrapped).toBeInstanceOf(LangfuseMiddleware)
		})

		it("should not wrap when Langfuse is not ready", () => {
			mockLangfuseService.isReady.mockReturnValue(false)
			
			const wrapped = LangfuseMiddleware.conditionalWrap(mockHandler, "test-provider")
			expect(wrapped).toBe(mockHandler)
		})

		it("should not wrap when Langfuse service is null", () => {
			vi.mocked(LangfuseService.getInstance).mockReturnValue(null)
			
			const wrapped = LangfuseMiddleware.conditionalWrap(mockHandler, "test-provider")
			expect(wrapped).toBe(mockHandler)
		})
	})

	describe("API handler builder wrapping", () => {
		it("should wrap API handler builder function", () => {
			const originalBuilder = vi.fn().mockReturnValue(mockHandler)
			const wrappedBuilder = wrapApiHandlerBuilder(originalBuilder)

			const config = { apiProvider: "openai" }
			const result = wrappedBuilder(config)

			expect(originalBuilder).toHaveBeenCalledWith(config)
			expect(result).toBeInstanceOf(LangfuseMiddleware)
		})

		it("should handle unknown provider in builder", () => {
			const originalBuilder = vi.fn().mockReturnValue(mockHandler)
			const wrappedBuilder = wrapApiHandlerBuilder(originalBuilder)

			const config = {} // No apiProvider
			const result = wrappedBuilder(config)

			expect(result).toBeInstanceOf(LangfuseMiddleware)
		})
	})
})
