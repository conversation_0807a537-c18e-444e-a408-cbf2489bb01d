(()=>{var e={};e.id=128,e.ids=[128],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13742:(e,r,t)=>{Promise.resolve().then(t.bind(t,57103))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23640:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>c});var n=t(88253),s=t(21418),a=t(52052),o=t.n(a),i=t(75779),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c={children:["",{children:["runs",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,56444)),"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\app\\runs\\new\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,29230))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,56910)),"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,64544,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,20441,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,93670,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,29230))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\app\\runs\\new\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/runs/new/page",pathname:"/runs/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44006:(e,r,t)=>{Promise.resolve().then(t.bind(t,86871))},55511:e=>{"use strict";e.exports=require("crypto")},56444:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var n=t(90811),s=t(86871);function a(){return(0,n.jsx)("div",{className:"max-w-3xl mx-auto px-12 p-12",children:(0,n.jsx)(s.NewRun,{})})}},57103:(e,r,t)=>{"use strict";t.d(r,{NewRun:()=>er});var n=t(3641),s=t(44508),a=t(30427),o=t(27013),i=t(8257),l=t(73466);let c=(e,r,t)=>{if(e&&"reportValidity"in e){let n=(0,l.Jt)(t,r);e.setCustomValidity(n&&n.message||""),e.reportValidity()}},u=(e,r)=>{for(let t in r.fields){let n=r.fields[t];n&&n.ref&&"reportValidity"in n.ref?c(n.ref,t,e):n&&n.refs&&n.refs.forEach(r=>c(r,t,e))}},d=(e,r)=>{r.shouldUseNativeValidation&&u(e,r);let t={};for(let n in e){let s=(0,l.Jt)(r.fields,n),a=Object.assign(e[n]||{},{ref:s&&s.ref});if(f(r.names||Object.keys(e),n)){let e=Object.assign({},(0,l.Jt)(t,n));(0,l.hZ)(e,"root",a),(0,l.hZ)(t,n,e)}else(0,l.hZ)(t,n,a)}return t},f=(e,r)=>{let t=p(r);return e.some(e=>p(e).match(`^${t}\\.\\d+`))};function p(e){return e.replace(/\]|\[/g,"")}function m(e,r,t){function n(t,n){var s;for(let a in Object.defineProperty(t,"_zod",{value:t._zod??{},enumerable:!1}),(s=t._zod).traits??(s.traits=new Set),t._zod.traits.add(e),r(t,n),o.prototype)a in t||Object.defineProperty(t,a,{value:o.prototype[a].bind(t)});t._zod.constr=o,t._zod.def=n}let s=t?.Parent??Object;class a extends s{}function o(e){var r;let s=t?.Parent?new a:this;for(let t of(n(s,e),(r=s._zod).deferred??(r.deferred=[]),s._zod.deferred))t();return s}return Object.defineProperty(a,"name",{value:e}),Object.defineProperty(o,"init",{value:n}),Object.defineProperty(o,Symbol.hasInstance,{value:r=>!!t?.Parent&&r instanceof t.Parent||r?._zod?.traits?.has(e)}),Object.defineProperty(o,"name",{value:e}),o}Symbol("zod_brand");class h extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let x={};function y(e){return e&&Object.assign(x,e),x}function v(e,r){return"bigint"==typeof r?r.toString():r}function g(e){return"string"==typeof e?e:e?.message}function b(e,r,t){let n={...e,path:e.path??[]};return e.message||(n.message=g(e.inst?._zod.def?.error?.(e))??g(r?.error?.(e))??g(t.customError?.(e))??g(t.localeError?.(e))??"Invalid input"),delete n.inst,delete n.continue,r?.reportInput||delete n.input,n}Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MAX_VALUE,Number.MAX_VALUE;let j=(e,r)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:r,enumerable:!1}),Object.defineProperty(e,"message",{get:()=>JSON.stringify(r,v,2),enumerable:!0})},w=m("$ZodError",j),N=m("$ZodError",j,{Parent:Error}),P=(e,r,t,n)=>{let s=t?Object.assign(t,{async:!1}):{async:!1},a=e._zod.run({value:r,issues:[]},s);if(a instanceof Promise)throw new h;if(a.issues.length){let e=new(n?.Err??N)(a.issues.map(e=>b(e,s,y())));throw Error.captureStackTrace(e,n?.callee),e}return a.value},S=async(e,r,t,n)=>{let s=t?Object.assign(t,{async:!0}):{async:!0},a=e._zod.run({value:r,issues:[]},s);if(a instanceof Promise&&(a=await a),a.issues.length){let e=new(n?.Err??N)(a.issues.map(e=>b(e,s,y())));throw Error.captureStackTrace(e,n?.callee),e}return a.value};function E(e,r,t,n){let s=Math.abs(e),a=s%10,o=s%100;return o>=11&&o<=19?n:1===a?r:a>=2&&a<=4?t:n}let k=e=>{let r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return r};function z(e,r,t,n){let s=Math.abs(e),a=s%10,o=s%100;return o>=11&&o<=19?n:1===a?r:a>=2&&a<=4?t:n}let _=e=>{let r=typeof e;switch(r){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return r};Symbol("ZodOutput"),Symbol("ZodInput");function C(e,r){try{var t=e()}catch(e){return r(e)}return t&&t.then?t.then(void 0,r):t}var O=t(38131),A=t.n(O),I=t(91149),R=t(1821),M=t(24378),q=t(72364);let T=(0,q.A)("sliders-horizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]);var V=t(17237);let $=(0,q.A)("book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]);var D=t(62248),U=t(20574),L=t(13461),F=t(29269);let G=(0,F.createServerReference)("4091b401de92e6c0f5dbb6ec6e2898a9f5ec057637",F.callServer,void 0,F.findSourceMapURL,"createRun"),J=(0,F.createServerReference)("7f4dfccfb1ce82bde7fe6bae316a3b65cfa8f3c634",F.callServer,void 0,F.findSourceMapURL,"getExercises"),Z=o.z.object({model:o.z.string().min(1,{message:"Model is required."}),description:o.z.string().optional(),suite:o.z.enum(["full","partial"]),exercises:o.z.array(o.z.string()).optional(),settings:L.us.optional(),concurrency:o.z.number().int().min(1).max(25),systemPrompt:o.z.string().optional()}).refine(e=>"full"===e.suite||(e.exercises||[]).length>0,{message:"Exercises are required when running a partial suite.",path:["exercises"]});var X=t(10244);let B=o.z.object({id:o.z.string(),name:o.z.string()}),H=async()=>{let e=await fetch("https://openrouter.ai/api/v1/models");if(!e.ok)return[];let r=o.z.object({data:o.z.array(B)}).safeParse(await e.json());return r.success?r.data.data.sort((e,r)=>e.name.localeCompare(r.name)):(console.error(r.error),[])},K=()=>(0,i.I)({queryKey:["getOpenRouterModels"],queryFn:H});var Y=t(15361);let W=[...L.jK,...L.LT];function Q({customSettings:{experiments:e,...r},defaultSettings:{experiments:t,...s},className:a,...o}){let i={...s,...t},l={...r,...e};return(0,n.jsxs)("div",{className:(0,X.cn)("grid grid-cols-3 gap-2 text-sm p-2",a),...o,children:[(0,n.jsx)("div",{className:"font-medium text-muted-foreground",children:"Setting"}),(0,n.jsx)("div",{className:"font-medium text-muted-foreground",children:"Default"}),(0,n.jsx)("div",{className:"font-medium text-muted-foreground",children:"Custom"}),W.map(e=>{let r=i[e],t=l[e];return JSON.stringify(r)===JSON.stringify(t)?null:(0,n.jsx)(ee,{name:e,defaultValue:JSON.stringify(r,null,2),customValue:JSON.stringify(t,null,2)},e)})]})}function ee({name:e,defaultValue:r,customValue:t,...a}){return(0,n.jsxs)(s.Fragment,{...a,children:[(0,n.jsx)("div",{className:"overflow-hidden font-mono",title:e,children:e}),(0,n.jsx)("pre",{className:"overflow-hidden inline text-rose-500 line-through",title:r,children:r}),(0,n.jsx)("pre",{className:"overflow-hidden inline text-teal-500",title:t,children:t})]})}function er(){let e=(0,a.useRouter)(),[r,t]=(0,s.useState)("openrouter"),[c,f]=(0,s.useState)(""),[p,m]=(0,s.useState)(!1),h=(0,s.useRef)(new Map),x=(0,s.useRef)(""),y=K(),v=(0,i.I)({queryKey:["getExercises"],queryFn:()=>J()}),g=(0,l.mN)({resolver:function(e,r,t){if(void 0===t&&(t={}),"_def"in e&&"object"==typeof e._def&&"typeName"in e._def)return function(r,n,s){try{return Promise.resolve(C(function(){return Promise.resolve(e["sync"===t.mode?"parse":"parseAsync"](r,void 0)).then(function(e){return s.shouldUseNativeValidation&&u({},s),{errors:{},values:t.raw?Object.assign({},r):e}})},function(e){if(Array.isArray(null==e?void 0:e.issues))return{values:{},errors:d(function(e,r){for(var t={};e.length;){var n=e[0],s=n.code,a=n.message,o=n.path.join(".");if(!t[o])if("unionErrors"in n){var i=n.unionErrors[0].errors[0];t[o]={message:i.message,type:i.code}}else t[o]={message:a,type:s};if("unionErrors"in n&&n.unionErrors.forEach(function(r){return r.errors.forEach(function(r){return e.push(r)})}),r){var c=t[o].types,u=c&&c[n.code];t[o]=(0,l.Gb)(o,r,t,s,u?[].concat(u,n.message):n.message)}e.shift()}return t}(e.errors,!s.shouldUseNativeValidation&&"all"===s.criteriaMode),s)};throw e}))}catch(e){return Promise.reject(e)}};if("_zod"in e&&"object"==typeof e._zod)return function(r,n,s){try{return Promise.resolve(C(function(){return Promise.resolve(("sync"===t.mode?P:S)(e,r,void 0)).then(function(e){return s.shouldUseNativeValidation&&u({},s),{errors:{},values:t.raw?Object.assign({},r):e}})},function(e){if(e instanceof w)return{values:{},errors:d(function(e,r){for(var t={};e.length;){var n=e[0],s=n.code,a=n.message,o=n.path.join(".");if(!t[o])if("invalid_union"===n.code){var i=n.errors[0][0];t[o]={message:i.message,type:i.code}}else t[o]={message:a,type:s};if("invalid_union"===n.code&&n.errors.forEach(function(r){return r.forEach(function(r){return e.push(r)})}),r){var c=t[o].types,u=c&&c[n.code];t[o]=(0,l.Gb)(o,r,t,s,u?[].concat(u,n.message):n.message)}e.shift()}return t}(e.issues,!s.shouldUseNativeValidation&&"all"===s.criteriaMode),s)};throw e}))}catch(e){return Promise.reject(e)}};throw Error("Invalid input: not a Zod schema")}(Z),defaultValues:{model:"anthropic/claude-sonnet-4",description:"",suite:"full",exercises:[],settings:void 0,concurrency:1}}),{setValue:b,clearErrors:j,watch:N,formState:{isSubmitting:E}}=g,[k,z,_]=N(["model","suite","settings","concurrency"]),[O,q]=(0,s.useState)(!1),[F,B]=(0,s.useState)(""),H=(0,s.useRef)(null),W=(0,s.useCallback)(async t=>{try{"openrouter"===r&&(t.settings={...t.settings||{},openRouterModelId:k});let{id:n}=await G({...t,systemPrompt:F});e.push(`/runs/${n}`)}catch(e){I.oR.error(e instanceof Error?e.message:"An unknown error occurred.")}},[r,k,e,F]),ee=(0,s.useCallback)((e,r)=>{if(x.current!==r)for(let{obj:{id:e},score:t}of(x.current=r,h.current.clear(),A().go(r,y.data||[],{key:"name"})))h.current.set(e,t);return h.current.get(e)??0},[y.data]),er=(0,s.useCallback)(e=>{b("model",e),m(!1)},[b]),et=(0,s.useCallback)(async e=>{let r=e.target.files?.[0];if(r){j("settings");try{let{providerProfiles:n,globalSettings:s}=o.z.object({providerProfiles:o.z.object({currentApiConfigName:o.z.string(),apiConfigs:o.z.record(o.z.string(),L.AQ)}),globalSettings:L.YZ}).parse(JSON.parse(await r.text())),a=n.apiConfigs[n.currentApiConfigName]??{};b("model",(0,L.Xx)(a)??""),b("settings",{...L.Ur,...a,...s}),t("settings"),e.target.value=""}catch(e){console.error(e),I.oR.error(e instanceof Error?e.message:"An unknown error occurred.")}}},[j,b]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l.Op,{...g,children:(0,n.jsxs)("form",{onSubmit:g.handleSubmit(W),className:"flex flex-col justify-center divide-y divide-primary *:py-5",children:[(0,n.jsxs)("div",{className:"flex flex-row justify-between gap-4",children:["openrouter"===r&&(0,n.jsx)(Y.zB,{control:g.control,name:"model",render:()=>(0,n.jsxs)(Y.eI,{className:"flex-1",children:[(0,n.jsxs)(Y.AM,{open:p,onOpenChange:m,children:[(0,n.jsx)(Y.Wv,{asChild:!0,children:(0,n.jsxs)(Y.$n,{variant:"input",role:"combobox","aria-expanded":p,className:"flex items-center justify-between",children:[(0,n.jsx)("div",{children:y.data?.find(({id:e})=>e===k)?.name||k||"Select OpenRouter Model"}),(0,n.jsx)(R.A,{className:"opacity-50"})]})}),(0,n.jsx)(Y.hl,{className:"p-0 w-[var(--radix-popover-trigger-width)]",children:(0,n.jsxs)(Y.uB,{filter:ee,children:[(0,n.jsx)(Y.G7,{placeholder:"Search",value:c,onValueChange:f,className:"h-9"}),(0,n.jsxs)(Y.oI,{children:[(0,n.jsx)(Y.xL,{children:"No model found."}),(0,n.jsx)(Y.L$,{children:y.data?.map(({id:e,name:r})=>n.jsxs(Y.h_,{value:e,onSelect:er,children:[r,n.jsx(M.A,{className:X.cn("ml-auto text-accent group-data-[selected=true]:text-accent-foreground size-4",e===k?"opacity-100":"opacity-0")})]},e))})]})]})})]}),(0,n.jsx)(Y.C5,{})]})}),(0,n.jsxs)(Y.eI,{className:"flex-1",children:[(0,n.jsxs)(Y.$n,{type:"button",variant:"secondary",onClick:()=>document.getElementById("json-upload")?.click(),children:[(0,n.jsx)(T,{}),"Import Settings"]}),(0,n.jsx)("input",{id:"json-upload",type:"file",accept:"application/json",className:"hidden",onChange:et}),_&&(0,n.jsx)(Y.FK,{className:"max-h-64 border rounded-sm",children:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"flex items-center gap-1 p-2 border-b",children:[(0,n.jsx)(V.A,{className:"size-4 text-ring"}),(0,n.jsx)("div",{className:"text-sm",children:"Imported valid Roo Code settings. Showing differences from default settings."})]}),(0,n.jsx)(Q,{defaultSettings:L.Ur,customSettings:_})]})}),(0,n.jsx)(Y.C5,{})]}),(0,n.jsxs)(Y.$n,{type:"button",variant:"secondary",onClick:()=>q(!0),children:[(0,n.jsx)($,{}),"Override System Prompt"]}),(0,n.jsx)(Y.lG,{open:O,onOpenChange:q,children:(0,n.jsxs)(Y.Cf,{children:[(0,n.jsx)(Y.L3,{children:"Override System Prompt"}),(0,n.jsx)(Y.TM,{ref:H,value:F,onChange:e=>B(e.target.value)}),(0,n.jsx)(Y.Es,{children:(0,n.jsx)(Y.$n,{onClick:()=>q(!1),children:"Done"})})]})})]}),(0,n.jsx)(Y.zB,{control:g.control,name:"suite",render:()=>(0,n.jsxs)(Y.eI,{children:[(0,n.jsx)(Y.lR,{children:"Exercises"}),(0,n.jsx)(Y.tU,{defaultValue:"full",onValueChange:e=>b("suite",e),children:(0,n.jsxs)(Y.j7,{children:[(0,n.jsx)(Y.Xi,{value:"full",children:"All"}),(0,n.jsx)(Y.Xi,{value:"partial",children:"Some"})]})}),"partial"===z&&(0,n.jsx)(Y.KF,{options:v.data?.map(e=>({value:e,label:e}))||[],onValueChange:e=>b("exercises",e),placeholder:"Select",variant:"inverted",maxCount:4}),(0,n.jsx)(Y.C5,{})]})}),(0,n.jsx)(Y.zB,{control:g.control,name:"concurrency",render:({field:e})=>(0,n.jsxs)(Y.eI,{children:[(0,n.jsx)(Y.lR,{children:"Concurrency"}),(0,n.jsx)(Y.MJ,{children:(0,n.jsxs)("div",{className:"flex flex-row items-center gap-2",children:[(0,n.jsx)(Y.Ap,{defaultValue:[e.value],min:1,max:25,step:1,onValueChange:r=>e.onChange(r[0])}),(0,n.jsx)("div",{children:e.value})]})}),(0,n.jsx)(Y.C5,{})]})}),(0,n.jsx)(Y.zB,{control:g.control,name:"description",render:({field:e})=>(0,n.jsxs)(Y.eI,{children:[(0,n.jsx)(Y.lR,{children:"Description / Notes"}),(0,n.jsx)(Y.MJ,{children:(0,n.jsx)(Y.TM,{placeholder:"Optional",...e})}),(0,n.jsx)(Y.C5,{})]})}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsxs)(Y.$n,{size:"lg",type:"submit",disabled:E,children:[(0,n.jsx)(D.A,{className:"size-4"}),"Launch"]})})]})}),(0,n.jsx)(Y.$n,{variant:"default",className:"absolute top-4 right-12 size-12 rounded-full",onClick:()=>e.push("/"),children:(0,n.jsx)(U.A,{className:"size-6"})})]})}},62248:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(72364).A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69366:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"40886c0d1e8f36a8f924a7167a2bcfa9d073422992":()=>n.x,"4091b401de92e6c0f5dbb6ec6e2898a9f5ec057637":()=>n.u,"7f4dfccfb1ce82bde7fe6bae316a3b65cfa8f3c634":()=>d});var n=t(92369),s=t(96401);t(90109);var a=t(33873),o=t(79551),i=t(65391),l=t(29701);let c=a.dirname((0,o.fileURLToPath)("file:///D:/Documents/Python/Roo-Code/apps/web-evals/src/actions/exercises.ts")),u=a.resolve(c,"../../../../../evals"),d=async()=>(await Promise.all(i.w8.map(async e=>{let r=a.join(u,e);return(await (0,i.Sk)(c,r)).map(r=>`${e}/${r}`)}))).flat();(0,l.D)([d]),(0,s.A)(d,"7f4dfccfb1ce82bde7fe6bae316a3b65cfa8f3c634",null)},74998:e=>{"use strict";e.exports=require("perf_hooks")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},86871:(e,r,t)=>{"use strict";t.d(r,{NewRun:()=>n});let n=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call NewRun() from the server but NewRun is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\app\\runs\\new\\new-run.tsx","NewRun")},91645:e=>{"use strict";e.exports=require("net")},92369:(e,r,t)=>{"use strict";t.d(r,{u:()=>h,x:()=>x});var n=t(96401);t(90109);var s=t(33873),a=t(29021),o=t.n(a),i=t(79551);let l=require("child_process");var c=t(10606);async function u(e,r,{concurrency:t=Number.POSITIVE_INFINITY,stopOnError:n=!0,signal:s}={}){return new Promise((a,o)=>{if(void 0===e[Symbol.iterator]&&void 0===e[Symbol.asyncIterator])throw TypeError(`Expected \`input\` to be either an \`Iterable\` or \`AsyncIterable\`, got (${typeof e})`);if("function"!=typeof r)throw TypeError("Mapper function is required");if(!(Number.isSafeInteger(t)&&t>=1||t===Number.POSITIVE_INFINITY))throw TypeError(`Expected \`concurrency\` to be an integer from 1 and up or \`Infinity\`, got \`${t}\` (${typeof t})`);let i=[],l=[],c=new Map,u=!1,f=!1,p=!1,m=0,h=0,x=void 0===e[Symbol.iterator]?e[Symbol.asyncIterator]():e[Symbol.iterator](),y=()=>{b(s.reason)},v=()=>{s?.removeEventListener("abort",y)},g=e=>{a(e),v()},b=e=>{u=!0,f=!0,o(e),v()};s&&(s.aborted&&b(s.reason),s.addEventListener("abort",y,{once:!0}));let j=async()=>{if(f)return;let e=await x.next(),t=h;if(h++,e.done){if(p=!0,0===m&&!f){if(!n&&l.length>0){b(AggregateError(l));return}if(f=!0,0===c.size){g(i);return}let e=[];for(let[r,t]of i.entries())c.get(r)!==d&&e.push(t);g(e)}return}m++,(async()=>{try{let n=await e.value;if(f)return;let s=await r(n,t);s===d&&c.set(t,s),i[t]=s,m--,await j()}catch(e){if(n)b(e);else{l.push(e),m--;try{await j()}catch(e){b(e)}}}})()};(async()=>{for(let e=0;e<t;e++){try{await j()}catch(e){b(e);break}if(p||u)break}})()})}let d=Symbol("skip");var f=t(65391),p=t(29701);let m=s.resolve(s.dirname((0,i.fileURLToPath)("file:///D:/Documents/Python/Roo-Code/apps/web-evals/src/actions/runs.ts")),"../../../../../evals");async function h({suite:e,exercises:r=[],systemPrompt:t,...n}){let s=await (0,f.ut)({...n,socketPath:""});if("partial"===e)for(let e of r){let[r,t]=e.split("/");if(!r||!t)throw Error("Invalid exercise path: "+e);await (0,f.UT)({...n,runId:s.id,language:r,exercise:t})}else for(let e of f.w8){let r=await (0,f.z)(m,e);await u(r,r=>(0,f.UT)({runId:s.id,language:e,exercise:r}),{concurrency:10})}(0,c.revalidatePath)("/runs");try{let e=o().existsSync("/.dockerenv"),r=[`--name evals-controller-${s.id}`,"--network evals_default","-v /var/run/docker.sock:/var/run/docker.sock","-v /tmp/evals:/var/log/evals","-e HOST_EXECUTION_METHOD=docker"],t=`pnpm --filter @roo-code/evals cli --runId ${s.id}`,n=e?`docker run ${r.join(" ")} evals-runner sh -c "${t}"`:t;console.log("spawn ->",n);let a=(0,l.spawn)("sh",["-c",n],{detached:!0,stdio:["ignore","pipe","pipe"]}),i=o().createWriteStream("/tmp/roo-code-evals.log",{flags:"a"});a.stdout&&a.stdout.pipe(i),a.stderr&&a.stderr.pipe(i),a.unref()}catch(e){console.error(e)}return s}async function x(e){await (0,f.xF)(e),(0,c.revalidatePath)("/runs")}(0,p.D)([h,x]),(0,n.A)(h,"4091b401de92e6c0f5dbb6ec6e2898a9f5ec057637",null),(0,n.A)(x,"40886c0d1e8f36a8f924a7167a2bcfa9d073422992",null)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[105,91,342,561,994,328,461],()=>t(23640));module.exports=n})();