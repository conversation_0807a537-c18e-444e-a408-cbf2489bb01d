import { EventEmitter } from "events"
import type { TelemetryClient, TelemetryEvent, TelemetryPropertiesProvider } from "@roo-code/types"

import { LangfuseClient } from "./LangfuseClient"
import { LangfuseTracer } from "./LangfuseTracer"
import type {
	LangfuseConfig,
	LangfuseEvents,
	LlmCallTrace,
	ConversationTrace,
	ToolUsageTrace,
	LangfuseTraceMetadata
} from "./types"
import { LangfuseStatus } from "./types"
import { validateConfig, isConfigurationComplete } from "./utils/validators"
import { generateTraceId } from "./utils/formatters"

/**
 * Main Langfuse service that integrates with the Roo Code telemetry system
 * Implements TelemetryClient interface to plug into existing telemetry infrastructure
 */
export class LangfuseService extends EventEmitter<LangfuseEvents> implements TelemetryClient {
	private static _instance: LangfuseService | null = null
	
	private client: LangfuseClient
	private tracer: LangfuseTracer
	private config: LangfuseConfig
	private status: LangfuseStatus = LangfuseStatus.DISABLED
	private providerRef: WeakRef<TelemetryPropertiesProvider> | null = null
	private activeSessions = new Map<string, { sessionId: string; startTime: Date }>()

	private constructor(config: LangfuseConfig) {
		super()
		this.config = validateConfig(config)
		this.client = new LangfuseClient(this.config)
		this.tracer = new LangfuseTracer(this.client)
	}

	/**
	 * Creates or gets the singleton instance
	 */
	static async createInstance(config: LangfuseConfig): Promise<LangfuseService> {
		if (this._instance) {
			await this._instance.updateConfig(config)
			return this._instance
		}

		this._instance = new LangfuseService(config)
		await this._instance.initialize()
		return this._instance
	}

	/**
	 * Gets the singleton instance
	 */
	static getInstance(): LangfuseService | null {
		return this._instance
	}

	/**
	 * Resets the singleton instance
	 */
	static resetInstance(): void {
		if (this._instance) {
			this._instance.shutdown()
			this._instance = null
		}
	}

	/**
	 * Initializes the Langfuse service
	 */
	private async initialize(): Promise<void> {
		if (!this.config.enabled) {
			this.status = LangfuseStatus.DISABLED
			return
		}

		try {
			this.status = LangfuseStatus.INITIALIZING
			await this.client.initialize()
			this.status = LangfuseStatus.READY
			this.emit("configChanged", this.config)
		} catch (error) {
			this.status = LangfuseStatus.ERROR
			this.emit("error", error instanceof Error ? error : new Error(String(error)), "initialize")
		}
	}

	/**
	 * Updates the configuration
	 */
	async updateConfig(newConfig: Partial<LangfuseConfig>): Promise<void> {
		const updatedConfig = { ...this.config, ...newConfig }
		this.config = validateConfig(updatedConfig)
		
		this.tracer.updateConfig(this.config)
		await this.client.updateConfig(this.config)
		
		if (this.config.enabled && this.status === LangfuseStatus.DISABLED) {
			await this.initialize()
		} else if (!this.config.enabled && this.status !== LangfuseStatus.DISABLED) {
			this.status = LangfuseStatus.DISABLED
		}

		this.emit("configChanged", this.config)
	}

	/**
	 * Gets the current configuration
	 */
	getConfig(): LangfuseConfig {
		return { ...this.config }
	}

	/**
	 * Gets the current status
	 */
	getStatus(): LangfuseStatus {
		return this.status
	}

	/**
	 * Checks if the service is ready
	 */
	isReady(): boolean {
		return this.status === LangfuseStatus.READY && this.client.isReady()
	}

	// TelemetryClient interface implementation

	/**
	 * Sets the provider for getting global telemetry properties
	 */
	setProvider(provider: TelemetryPropertiesProvider): void {
		this.providerRef = new WeakRef(provider)
	}

	/**
	 * Captures telemetry events and converts them to Langfuse traces
	 */
	async capture(event: TelemetryEvent): Promise<void> {
		if (!this.isReady()) {
			return
		}

		try {
			await this.handleTelemetryEvent(event)
		} catch (error) {
			this.emit("error", error instanceof Error ? error : new Error(String(error)), "capture")
		}
	}

	/**
	 * Updates telemetry state (respects privacy settings)
	 */
	updateTelemetryState(didUserOptIn: boolean): void {
		if (this.config.respectPrivacySettings && !didUserOptIn) {
			// Disable tracing if user opted out and we respect privacy settings
			this.updateConfig({ enabled: false })
		}
	}

	/**
	 * Checks if telemetry is enabled
	 */
	isTelemetryEnabled(): boolean {
		return this.isReady()
	}

	/**
	 * Shuts down the service
	 */
	async shutdown(): Promise<void> {
		try {
			await this.tracer.flush()
			await this.client.shutdown()
			this.status = LangfuseStatus.DISABLED
			this.activeSessions.clear()
		} catch (error) {
			this.emit("error", error instanceof Error ? error : new Error(String(error)), "shutdown")
		}
	}

	// Public API methods for direct usage

	/**
	 * Starts a new session
	 */
	async startSession(sessionId: string, metadata?: LangfuseTraceMetadata): Promise<string | null> {
		if (!this.isReady()) {
			return null
		}

		const traceId = await this.tracer.startSession(sessionId, metadata)
		if (traceId) {
			this.activeSessions.set(sessionId, { sessionId, startTime: new Date() })
			this.emit("traceCreated", traceId, { sessionId, ...metadata })
		}
		return traceId
	}

	/**
	 * Ends a session
	 */
	async endSession(sessionId: string, metadata?: Record<string, unknown>): Promise<void> {
		if (!this.isReady()) {
			return
		}

		await this.tracer.endSession(sessionId, metadata)
		this.activeSessions.delete(sessionId)
		this.emit("traceCompleted", sessionId, metadata)
	}

	/**
	 * Traces an LLM call
	 */
	async traceLlmCall(trace: LlmCallTrace, metadata?: LangfuseTraceMetadata): Promise<string | null> {
		if (!this.isReady()) {
			return null
		}

		const traceId = await this.tracer.traceLlmCall(trace, metadata)
		if (traceId) {
			this.emit("llmCallTraced", traceId, { model: trace.model, provider: trace.provider, ...metadata })
		}
		return traceId
	}

	/**
	 * Traces a conversation
	 */
	async traceConversation(trace: ConversationTrace, metadata?: LangfuseTraceMetadata): Promise<string | null> {
		if (!this.isReady()) {
			return null
		}

		const traceId = await this.tracer.traceConversation(trace, metadata)
		if (traceId) {
			this.emit("conversationTraced", traceId, { taskId: trace.taskId, messageCount: trace.messages.length, ...metadata })
		}
		return traceId
	}

	/**
	 * Traces tool usage
	 */
	async traceToolUsage(trace: ToolUsageTrace, metadata?: LangfuseTraceMetadata): Promise<string | null> {
		if (!this.isReady()) {
			return null
		}

		const traceId = await this.tracer.traceToolUsage(trace, metadata)
		if (traceId) {
			this.emit("toolUsageTraced", traceId, { toolName: trace.toolName, ...metadata })
		}
		return traceId
	}

	/**
	 * Gets active sessions
	 */
	getActiveSessions(): Array<{ sessionId: string; startTime: Date }> {
		return Array.from(this.activeSessions.values())
	}

	/**
	 * Flushes pending traces
	 */
	async flush(): Promise<void> {
		if (this.isReady()) {
			await this.tracer.flush()
		}
	}

	// Private methods for handling telemetry events

	/**
	 * Handles incoming telemetry events and converts them to Langfuse traces
	 */
	private async handleTelemetryEvent(event: TelemetryEvent): Promise<void> {
		const properties = event.properties || {}

		switch (event.event) {
			case "Task Created":
				await this.handleTaskCreated(properties)
				break
			case "Task Completed":
				await this.handleTaskCompleted(properties)
				break
			case "LLM Completion":
				await this.handleLlmCompletion(properties)
				break
			case "Tool Used":
				await this.handleToolUsed(properties)
				break
			case "Task Message":
				await this.handleTaskMessage(properties)
				break
			default:
				// For other events, create a generic span
				await this.handleGenericEvent(event)
		}
	}

	/**
	 * Handles task creation events
	 */
	private async handleTaskCreated(properties: Record<string, unknown>): Promise<void> {
		const taskId = properties.taskId as string
		if (!taskId) return

		const conversationTrace: ConversationTrace = {
			id: generateTraceId(),
			taskId,
			messages: [],
			startTime: new Date(),
			metadata: properties,
		}

		await this.traceConversation(conversationTrace, {
			taskId,
			tags: ["task", "created"],
		})
	}

	/**
	 * Handles task completion events
	 */
	private async handleTaskCompleted(properties: Record<string, unknown>): Promise<void> {
		const taskId = properties.taskId as string
		if (!taskId) return

		// End the session for this task
		await this.endSession(taskId, properties)
	}

	/**
	 * Handles LLM completion events
	 */
	private async handleLlmCompletion(properties: Record<string, unknown>): Promise<void> {
		const taskId = properties.taskId as string
		if (!taskId) return

		// Extract LLM completion data from telemetry properties
		const llmTrace: LlmCallTrace = {
			id: generateTraceId(),
			model: "unknown", // Will be enhanced by middleware
			provider: "unknown", // Will be enhanced by middleware
			input: {
				messages: [],
			},
			usage: {
				inputTokens: (properties.inputTokens as number) || 0,
				outputTokens: (properties.outputTokens as number) || 0,
				totalTokens: ((properties.inputTokens as number) || 0) + ((properties.outputTokens as number) || 0),
				cost: properties.cost as number,
			},
			startTime: new Date(),
			endTime: new Date(),
			metadata: properties,
		}

		await this.traceLlmCall(llmTrace, {
			taskId,
			tags: ["llm", "completion"],
		})
	}

	/**
	 * Handles tool usage events
	 */
	private async handleToolUsed(properties: Record<string, unknown>): Promise<void> {
		const taskId = properties.taskId as string
		const tool = properties.tool as string
		if (!taskId || !tool) return

		const toolTrace: ToolUsageTrace = {
			id: generateTraceId(),
			toolName: tool,
			input: properties.input,
			output: properties.output,
			startTime: new Date(),
			endTime: new Date(),
			metadata: properties,
		}

		await this.traceToolUsage(toolTrace, {
			taskId,
			tags: ["tool", tool],
		})
	}

	/**
	 * Handles task message events
	 */
	private async handleTaskMessage(properties: Record<string, unknown>): Promise<void> {
		// Task messages are handled by the conversation tracing
		// This could be enhanced to update existing conversation traces
	}

	/**
	 * Handles generic events by creating spans
	 */
	private async handleGenericEvent(event: TelemetryEvent): Promise<void> {
		if (!this.client.isReady()) return

		const span = await this.client.createSpan({
			name: event.event,
			input: event.properties,
			metadata: {
				eventType: event.event,
				...event.properties,
			},
			startTime: new Date(),
			endTime: new Date(),
		})

		if (span) {
			this.emit("traceCreated", event.event, event.properties)
		}
	}
}
