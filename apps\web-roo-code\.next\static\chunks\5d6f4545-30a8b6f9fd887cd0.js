"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[872],{39582:(e,t,i)=>{i.d(t,{Ay:()=>nO});var s="undefined"!=typeof window?window:void 0,r="undefined"!=typeof globalThis?globalThis:s,n=Array.prototype,o=n.forEach,a=n.indexOf,l=null==r?void 0:r.navigator,c=null==r?void 0:r.document,u=null==r?void 0:r.location,h=null==r?void 0:r.fetch,d=null!=r&&r.XMLHttpRequest&&"withCredentials"in new r.XMLHttpRequest?r.XMLHttpRequest:void 0,p=null==r?void 0:r.<PERSON>bortController,g=null==l?void 0:l.userAgent,_=null!=s?s:{},v={DEBUG:!1,LIB_VERSION:"1.249.2"},f="$copy_autocapture",m=["$snapshot","$pageview","$pageleave","$set","survey dismissed","survey sent","survey shown","$identify","$groupidentify","$create_alias","$$client_ingestion_warning","$web_experiment_applied","$feature_enrollment_update","$feature_flag_called"],y=function(e){return e.GZipJS="gzip-js",e.Base64="base64",e}({}),b=["fatal","error","warning","log","info","debug"];function w(e,t){return -1!==e.indexOf(t)}var E=function(e){return e.trim()},k=function(e){return e.replace(/^\$/,"")},S=Array.isArray,x=Object.prototype,I=x.hasOwnProperty,$=x.toString,F=S||function(e){return"[object Array]"===$.call(e)},R=e=>"function"==typeof e,P=e=>e===Object(e)&&!F(e),M=e=>{if(P(e)){for(var t in e)if(I.call(e,t))return!1;return!0}return!1},T=e=>void 0===e,C=e=>"[object String]"==$.call(e),O=e=>C(e)&&0===e.trim().length,A=e=>null===e,L=e=>T(e)||A(e),D=e=>"[object Number]"==$.call(e),N=e=>"[object Boolean]"===$.call(e),q=e=>e instanceof FormData,B=e=>w(m,e),H=e=>{var t={t:function(t){if(s&&(v.DEBUG||_.POSTHOG_DEBUG)&&!T(s.console)&&s.console){for(var i=("__rrweb_original__"in s.console[t])?s.console[t].__rrweb_original__:s.console[t],r=arguments.length,n=Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];i(e,...n)}},info:function(){for(var e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=arguments[s];t.t("log",...i)},warn:function(){for(var e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=arguments[s];t.t("warn",...i)},error:function(){for(var e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=arguments[s];t.t("error",...i)},critical:function(){for(var t=arguments.length,i=Array(t),s=0;s<t;s++)i[s]=arguments[s];console.error(e,...i)},uninitializedWarning:e=>{t.error("You must initialize PostHog before calling "+e)},createLogger:t=>H(e+" "+t)};return t},j=H("[PostHog.js]"),U=j.createLogger,z=U("[ExternalScriptsLoader]"),W=(e,t,i)=>{if(e.config.disable_external_dependency_loading)return z.warn(t+" was requested but loading of external scripts is disabled."),i("Loading of external scripts is disabled");var s=null==c?void 0:c.querySelectorAll("script");if(s){for(var r=0;r<s.length;r++)if(s[r].src===t)return i()}var n=()=>{if(!c)return i("document not found");var s=c.createElement("script");if(s.type="text/javascript",s.crossOrigin="anonymous",s.src=t,s.onload=e=>i(void 0,e),s.onerror=e=>i(e),e.config.prepare_external_dependency_script&&(s=e.config.prepare_external_dependency_script(s)),!s)return i("prepare_external_dependency_script returned null");var r,n=c.querySelectorAll("body > script");n.length>0?null==(r=n[0].parentNode)||r.insertBefore(s,n[0]):c.body.appendChild(s)};null!=c&&c.body?n():null==c||c.addEventListener("DOMContentLoaded",n)};function G(){return(G=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var s in i)({}).hasOwnProperty.call(i,s)&&(e[s]=i[s])}return e}).apply(null,arguments)}function V(e,t){if(null==e)return{};var i={};for(var s in e)if(({}).hasOwnProperty.call(e,s)){if(-1!==t.indexOf(s))continue;i[s]=e[s]}return i}_.__PosthogExtensions__=_.__PosthogExtensions__||{},_.__PosthogExtensions__.loadExternalDependency=(e,t,i)=>{var s="/static/"+t+".js?v="+e.version;"remote-config"===t&&(s="/array/"+e.config.token+"/config.js"),"toolbar"===t&&(s=s+"&t="+3e5*Math.floor(Date.now()/3e5));var r=e.requestRouter.endpointFor("assets",s);W(e,r,i)},_.__PosthogExtensions__.loadSiteApp=(e,t,i)=>{var s=e.requestRouter.endpointFor("api",t);W(e,s,i)};var Y={};function X(e,t,i){if(F(e)){if(o&&e.forEach===o)e.forEach(t,i);else if("length"in e&&e.length===+e.length){for(var s=0,r=e.length;s<r;s++)if(s in e&&t.call(i,e[s],s)===Y)return}}}function J(e,t,i){if(!L(e)){if(F(e))return X(e,t,i);if(q(e)){for(var s of e.entries())if(t.call(i,s[1],s[0])===Y)return}else for(var r in e)if(I.call(e,r)&&t.call(i,e[r],r)===Y)return}}var Z=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];return X(i,function(t){for(var i in t)void 0!==t[i]&&(e[i]=t[i])}),e},K=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];return X(i,function(t){X(t,function(t){e.push(t)})}),e};function Q(e){for(var t=Object.keys(e),i=t.length,s=Array(i);i--;)s[i]=[t[i],e[t[i]]];return s}var ee=function(e){try{return e()}catch(e){return}},et=function(e){return function(){try{for(var t=arguments.length,i=Array(t),s=0;s<t;s++)i[s]=arguments[s];return e.apply(this,i)}catch(e){j.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."),j.critical(e)}}},ei=function(e){var t={};return J(e,function(e,i){(C(e)&&e.length>0||D(e))&&(t[i]=e)}),t},es=["herokuapp.com","vercel.app","netlify.app"];function er(e,t){for(var i=0;i<e.length;i++)if(t(e[i]))return e[i]}function en(e,t,i,s){var{capture:r=!1,passive:n=!0}=null!=s?s:{};null==e||e.addEventListener(t,i,{capture:r,passive:n})}var eo="$people_distinct_id",ea="__alias",el="__timers",ec="$autocapture_disabled_server_side",eu="$heatmaps_enabled_server_side",eh="$exception_capture_enabled_server_side",ed="$error_tracking_suppression_rules",ep="$web_vitals_enabled_server_side",eg="$dead_clicks_enabled_server_side",e_="$web_vitals_allowed_metrics",ev="$session_recording_enabled_server_side",ef="$console_log_recording_enabled_server_side",em="$session_recording_network_payload_capture",ey="$session_recording_masking",eb="$session_recording_canvas_recording",ew="$replay_sample_rate",eE="$replay_minimum_duration",ek="$replay_script_config",eS="$sesid",ex="$session_is_sampled",eI="$session_recording_url_trigger_activated_session",e$="$session_recording_event_trigger_activated_session",eF="$enabled_feature_flags",eR="$early_access_features",eP="$feature_flag_details",eM="$stored_person_properties",eT="$stored_group_properties",eC="$surveys",eO="$surveys_activated",eA="$flag_call_reported",eL="$user_state",eD="$client_session_props",eN="$capture_rate_limit",eq="$initial_campaign_params",eB="$initial_referrer_info",eH="$initial_person_info",ej="$epp",eU="__POSTHOG_TOOLBAR__",ez="$posthog_cookieless",eW=[eo,ea,"__cmpns",el,ev,eu,eS,eF,ed,eL,eR,eP,eT,eM,eC,eA,eD,eN,eq,eB,ej,eH];function eG(e){return e instanceof Element&&(e.id===eU||!(null==e.closest||!e.closest(".toolbar-global-fade-container")))}function eV(e){return!!e&&1===e.nodeType}function eY(e,t){return!!e&&!!e.tagName&&e.tagName.toLowerCase()===t.toLowerCase()}function eX(e){return!!e&&3===e.nodeType}function eJ(e){return!!e&&11===e.nodeType}function eZ(e){return e?E(e).split(/\s+/):[]}function eK(e){var t=null==s?void 0:s.location.href;return!!(t&&e&&e.some(e=>t.match(e)))}function eQ(e){var t="";switch(typeof e.className){case"string":t=e.className;break;case"object":t=(e.className&&"baseVal"in e.className?e.className.baseVal:null)||e.getAttribute("class")||"";break;default:t=""}return eZ(t)}function e0(e){return L(e)?null:E(e).split(/(\s+)/).filter(e=>ts(e)).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)}function e1(e){var t="";return e6(e)&&!e4(e)&&e.childNodes&&e.childNodes.length&&J(e.childNodes,function(e){var i;eX(e)&&e.textContent&&(t+=null!==(i=e0(e.textContent))&&void 0!==i?i:"")}),E(t)}function e2(e){var t;return T(e.target)?e.srcElement||null:null!=(t=e.target)&&t.shadowRoot?e.composedPath()[0]||null:e.target||null}var e3=["a","button","form","input","select","textarea","label"];function e5(e){var t=e.parentNode;return!(!t||!eV(t))&&t}function e6(e){for(var t=e;t.parentNode&&!eY(t,"body");t=t.parentNode){var i=eQ(t);if(w(i,"ph-sensitive")||w(i,"ph-no-capture"))return!1}if(w(eQ(e),"ph-include"))return!0;var s=e.type||"";if(C(s))switch(s.toLowerCase()){case"hidden":case"password":return!1}var r=e.name||e.id||"";return!(C(r)&&/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(r.replace(/[^a-zA-Z0-9]/g,"")))}function e4(e){return!!(eY(e,"input")&&!["button","checkbox","submit","reset"].includes(e.type)||eY(e,"select")||eY(e,"textarea")||"true"===e.getAttribute("contenteditable"))}var e8="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",e7=RegExp("^(?:"+e8+")$"),e9=new RegExp(e8),te="\\d{3}-?\\d{2}-?\\d{4}",tt=RegExp("^("+te+")$"),ti=RegExp("("+te+")");function ts(e,t){return void 0===t&&(t=!0),!(L(e)||C(e)&&(e=E(e),(t?e7:e9).test((e||"").replace(/[- ]/g,""))||(t?tt:ti).test(e)))}function tr(e){var t=e1(e);return ts(t=(t+" "+function e(t){var i="";return t&&t.childNodes&&t.childNodes.length&&J(t.childNodes,function(t){var s;if(t&&"span"===(null==(s=t.tagName)?void 0:s.toLowerCase()))try{var r=e1(t);i=(i+" "+r).trim(),t.childNodes&&t.childNodes.length&&(i=(i+" "+e(t)).trim())}catch(e){j.error("[AutoCapture]",e)}}),i}(e)).trim())?t:""}function tn(e){return e.replace(/"|\\"/g,'\\"')}class to{constructor(){this.clicks=[]}isRageClick(e,t,i){var s=this.clicks[this.clicks.length-1];if(s&&Math.abs(e-s.x)+Math.abs(t-s.y)<30&&i-s.timestamp<1e3){if(this.clicks.push({x:e,y:t,timestamp:i}),3===this.clicks.length)return!0}else this.clicks=[{x:e,y:t,timestamp:i}];return!1}}var ta=["localhost","127.0.0.1"],tl=e=>{var t=null==c?void 0:c.createElement("a");return T(t)?null:(t.href=e,t)},tc=function(e,t){void 0===t&&(t="&");var i,s,r=[];return J(e,function(e,t){T(e)||T(t)||"undefined"===t||(i=encodeURIComponent(e instanceof File?e.name:e.toString()),s=encodeURIComponent(t),r[r.length]=s+"="+i)}),r.join(t)},tu=function(e,t){for(var i,s=((e.split("#")[0]||"").split(/\?(.*)/)[1]||"").replace(/^\?+/g,"").split("&"),r=0;r<s.length;r++){var n=s[r].split("=");if(n[0]===t){i=n;break}}if(!F(i)||i.length<2)return"";var o=i[1];try{o=decodeURIComponent(o)}catch(e){j.error("Skipping decoding for malformed query param: "+o)}return o.replace(/\+/g," ")},th=function(e,t,i){if(!e||!t||!t.length)return e;for(var s=e.split("#"),r=s[0]||"",n=s[1],o=r.split("?"),a=o[1],l=o[0],c=(a||"").split("&"),u=[],h=0;h<c.length;h++){var d=c[h].split("=");F(d)&&(t.includes(d[0])?u.push(d[0]+"="+i):u.push(c[h]))}var p=l;return null!=a&&(p+="?"+u.join("&")),null!=n&&(p+="#"+n),p},td=function(e,t){var i=e.match(RegExp(t+"=([^&]*)"));return i?i[1]:null},tp=U("[AutoCapture]");function tg(e,t){return t.length>e?t.slice(0,e)+"...":t}class t_{constructor(e){this.i=!1,this.o=null,this.rageclicks=new to,this.h=!1,this.instance=e,this.m=null}get S(){var e,t,i=P(this.instance.config.autocapture)?this.instance.config.autocapture:{};return i.url_allowlist=null==(e=i.url_allowlist)?void 0:e.map(e=>new RegExp(e)),i.url_ignorelist=null==(t=i.url_ignorelist)?void 0:t.map(e=>new RegExp(e)),i}$(){if(this.isBrowserSupported()){if(s&&c){var e=e=>{e=e||(null==s?void 0:s.event);try{this.k(e)}catch(e){tp.error("Failed to capture event",e)}};if(en(c,"submit",e,{capture:!0}),en(c,"change",e,{capture:!0}),en(c,"click",e,{capture:!0}),this.S.capture_copied_text){var t=e=>{e=e||(null==s?void 0:s.event),this.k(e,f)};en(c,"copy",t,{capture:!0}),en(c,"cut",t,{capture:!0})}}}else tp.info("Disabling Automatic Event Collection because this browser is not supported")}startIfEnabled(){this.isEnabled&&!this.i&&(this.$(),this.i=!0)}onRemoteConfig(e){e.elementsChainAsString&&(this.h=e.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[ec]:!!e.autocapture_opt_out}),this.o=!!e.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(e){this.m=e}getElementSelectors(e){var t,i=[];return null==(t=this.m)||t.forEach(t=>{var s=null==c?void 0:c.querySelectorAll(t);null==s||s.forEach(s=>{e===s&&i.push(t)})}),i}get isEnabled(){var e,t,i=null==(e=this.instance.persistence)?void 0:e.props[ec];if(A(this.o)&&!N(i)&&!this.instance.config.advanced_disable_decide)return!1;var s=null!==(t=this.o)&&void 0!==t?t:!!i;return!!this.instance.config.autocapture&&!s}k(e,t){if(void 0===t&&(t="$autocapture"),this.isEnabled){var i,r=e2(e);eX(r)&&(r=r.parentNode||null),"$autocapture"===t&&"click"===e.type&&e instanceof MouseEvent&&this.instance.config.rageclick&&null!=(i=this.rageclicks)&&i.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this.k(e,"$rageclick");var n=t===f;if(r&&function(e,t,i,r,n){if(void 0===i&&(i=void 0),!s||!e||eY(e,"html")||!eV(e)||null!=(o=i)&&o.url_allowlist&&!eK(i.url_allowlist)||null!=(a=i)&&a.url_ignorelist&&eK(i.url_ignorelist))return!1;if(null!=(l=i)&&l.dom_event_allowlist){var o,a,l,c=i.dom_event_allowlist;if(c&&!c.some(e=>t.type===e))return!1}for(var u=!1,h=[e],d=!0,p=e;p.parentNode&&!eY(p,"body");)if(eJ(p.parentNode))h.push(p.parentNode.host),p=p.parentNode.host;else{if(!(d=e5(p)))break;if(r||e3.indexOf(d.tagName.toLowerCase())>-1)u=!0;else{var g=s.getComputedStyle(d);g&&"pointer"===g.getPropertyValue("cursor")&&(u=!0)}h.push(d),p=d}if(!function(e,t){var i=null==t?void 0:t.element_allowlist;if(T(i))return!0;var s,r=function(e){if(i.some(t=>e.tagName.toLowerCase()===t))return{v:!0}};for(var n of e)if(s=r(n))return s.v;return!1}(h,i)||!function(e,t){var i=null==t?void 0:t.css_selector_allowlist;if(T(i))return!0;var s,r=function(e){if(i.some(t=>e.matches(t)))return{v:!0}};for(var n of e)if(s=r(n))return s.v;return!1}(h,i))return!1;var _=s.getComputedStyle(e);if(_&&"pointer"===_.getPropertyValue("cursor")&&"click"===t.type)return!0;var v=e.tagName.toLowerCase();switch(v){case"html":return!1;case"form":return(n||["submit"]).indexOf(t.type)>=0;case"input":case"select":case"textarea":return(n||["change","click"]).indexOf(t.type)>=0;default:return u?(n||["click"]).indexOf(t.type)>=0:(n||["click"]).indexOf(t.type)>=0&&(e3.indexOf(v)>-1||"true"===e.getAttribute("contenteditable"))}}(r,e,this.S,n,n?["copy","cut"]:void 0)){var{props:o,explicitNoCapture:a}=function(e,t){for(var i,r,{e:n,maskAllElementAttributes:o,maskAllText:a,elementAttributeIgnoreList:l,elementsChainAsString:c}=t,u=[e],h=e;h.parentNode&&!eY(h,"body");)eJ(h.parentNode)?(u.push(h.parentNode.host),h=h.parentNode.host):(u.push(h.parentNode),h=h.parentNode);var d,p=[],g={},_=!1,v=!1;if(J(u,e=>{var t=e6(e);"a"===e.tagName.toLowerCase()&&(_=e.getAttribute("href"),_=t&&_&&ts(_)&&_),w(eQ(e),"ph-no-capture")&&(v=!0),p.push(function(e,t,i,s){var r=e.tagName.toLowerCase(),n={tag_name:r};e3.indexOf(r)>-1&&!i&&("a"===r.toLowerCase()||"button"===r.toLowerCase()?n.$el_text=tg(1024,tr(e)):n.$el_text=tg(1024,e1(e)));var o=eQ(e);o.length>0&&(n.classes=o.filter(function(e){return""!==e})),J(e.attributes,function(i){var r;if((!e4(e)||-1!==["name","id","class","aria-label"].indexOf(i.name))&&(null==s||!s.includes(i.name))&&!t&&ts(i.value)&&(!C(r=i.name)||"_ngcontent"!==r.substring(0,10)&&"_nghost"!==r.substring(0,7))){var o=i.value;"class"===i.name&&(o=eZ(o).join(" ")),n["attr__"+i.name]=tg(1024,o)}});for(var a=1,l=1,c=e;c=function(e){if(e.previousElementSibling)return e.previousElementSibling;var t=e;do t=t.previousSibling;while(t&&!eV(t));return t}(c);)a++,c.tagName===e.tagName&&l++;return n.nth_child=a,n.nth_of_type=l,n}(e,o,a,l)),Z(g,function(e){if(!e6(e))return{};var t={};return J(e.attributes,function(e){if(e.name&&0===e.name.indexOf("data-ph-capture-attribute")){var i=e.name.replace("data-ph-capture-attribute-",""),s=e.value;i&&s&&ts(s)&&(t[i]=s)}}),t}(e))}),v)return{props:{},explicitNoCapture:v};if(a||("a"===e.tagName.toLowerCase()||"button"===e.tagName.toLowerCase()?p[0].$el_text=tr(e):p[0].$el_text=e1(e)),_){p[0].attr__href=_;var f,m,y=null==(f=tl(_))?void 0:f.host,b=null==s||null==(m=s.location)?void 0:m.host;y&&b&&y!==b&&(d=_)}return{props:Z({$event_type:n.type,$ce_version:1},c?{}:{$elements:p},{$elements_chain:p.map(e=>{var t,i,s,r={text:null==(i=e.$el_text)?void 0:i.slice(0,400),tag_name:e.tag_name,href:null==(s=e.attr__href)?void 0:s.slice(0,2048),attr_class:(t=e.attr__class)?F(t)?t:eZ(t):void 0,attr_id:e.attr__id,nth_child:e.nth_child,nth_of_type:e.nth_of_type,attributes:{}};return Q(e).filter(e=>{var[t]=e;return 0===t.indexOf("attr__")}).forEach(e=>{var[t,i]=e;return r.attributes[t]=i}),r}).map(e=>{var t,i,s="";if(e.tag_name&&(s+=e.tag_name),e.attr_class)for(var r of(e.attr_class.sort(),e.attr_class))s+="."+r.replace(/"/g,"");var n=G({},e.text?{text:e.text}:{},{"nth-child":null!==(t=e.nth_child)&&void 0!==t?t:0,"nth-of-type":null!==(i=e.nth_of_type)&&void 0!==i?i:0},e.href?{href:e.href}:{},e.attr_id?{attr_id:e.attr_id}:{},e.attributes),o={};return Q(n).sort((e,t)=>{var[i]=e,[s]=t;return i.localeCompare(s)}).forEach(e=>{var[t,i]=e;return o[tn(t.toString())]=tn(i.toString())}),s+=":",s+=Q(o).map(e=>{var[t,i]=e;return t+'="'+i+'"'}).join("")}).join(";")},null!=(i=p[0])&&i.$el_text?{$el_text:null==(r=p[0])?void 0:r.$el_text}:{},d&&"click"===n.type?{$external_click_url:d}:{},g)}}(r,{e:e,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.S.element_attribute_ignorelist,elementsChainAsString:this.h});if(a)return!1;var l=this.getElementSelectors(r);if(l&&l.length>0&&(o.$element_selectors=l),t===f){var c,u=e0(null==s||null==(c=s.getSelection())?void 0:c.toString()),h=e.type||"clipboard";if(!u)return!1;o.$selected_content=u,o.$copy_type=h}return this.instance.capture(t,o),!0}}}isBrowserSupported(){return R(null==c?void 0:c.querySelectorAll)}}Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Number.isInteger||(Number.isInteger=function(e){return D(e)&&isFinite(e)&&Math.floor(e)===e});var tv="0123456789abcdef";class tf{constructor(e){if(this.bytes=e,16!==e.length)throw TypeError("not 128-bit length")}static fromFieldsV7(e,t,i,s){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(i)||!Number.isInteger(s)||e<0||t<0||i<0||s<0||e>0xffffffffffff||t>4095||i>0x3fffffff||s>0xffffffff)throw RangeError("invalid field value");var r=new Uint8Array(16);return r[0]=e/0x10000000000,r[1]=e/0x100000000,r[2]=e/0x1000000,r[3]=e/65536,r[4]=e/256,r[5]=e,r[6]=112|t>>>8,r[7]=t,r[8]=128|i>>>24,r[9]=i>>>16,r[10]=i>>>8,r[11]=i,r[12]=s>>>24,r[13]=s>>>16,r[14]=s>>>8,r[15]=s,new tf(r)}toString(){for(var e="",t=0;t<this.bytes.length;t++)e=e+tv.charAt(this.bytes[t]>>>4)+tv.charAt(15&this.bytes[t]),3!==t&&5!==t&&7!==t&&9!==t||(e+="-");if(36!==e.length)throw Error("Invalid UUIDv7 was generated");return e}clone(){return new tf(this.bytes.slice(0))}equals(e){return 0===this.compareTo(e)}compareTo(e){for(var t=0;t<16;t++){var i=this.bytes[t]-e.bytes[t];if(0!==i)return Math.sign(i)}return 0}}class tm{constructor(){this.I=0,this.P=0,this.R=new tw}generate(){var e=this.generateOrAbort();if(T(e)){this.I=0;var t=this.generateOrAbort();if(T(t))throw Error("Could not generate UUID after timestamp reset");return t}return e}generateOrAbort(){var e=Date.now();if(e>this.I)this.I=e,this.T();else{if(!(e+1e4>this.I))return;this.P++,this.P>0x3ffffffffff&&(this.I++,this.T())}return tf.fromFieldsV7(this.I,Math.trunc(this.P/0x40000000),0x3fffffff&this.P,this.R.nextUint32())}T(){this.P=1024*this.R.nextUint32()+(1023&this.R.nextUint32())}}var ty,tb=e=>{if("undefined"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw Error("no cryptographically strong RNG available");for(var t=0;t<e.length;t++)e[t]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return e};s&&!T(s.crypto)&&crypto.getRandomValues&&(tb=e=>crypto.getRandomValues(e));class tw{constructor(){this.M=new Uint32Array(8),this.C=1/0}nextUint32(){return this.C>=this.M.length&&(tb(this.M),this.C=0),this.M[this.C++]}}var tE=()=>tk().toString(),tk=()=>(ty||(ty=new tm)).generate(),tS="",tx=/[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i,tI={O:()=>!!c,F:function(e){j.error("cookieStore error: "+e)},A:function(e){if(c){try{for(var t=e+"=",i=c.cookie.split(";").filter(e=>e.length),s=0;s<i.length;s++){for(var r=i[s];" "==r.charAt(0);)r=r.substring(1,r.length);if(0===r.indexOf(t))return decodeURIComponent(r.substring(t.length,r.length))}}catch(e){}return null}},D:function(e){var t;try{t=JSON.parse(tI.A(e))||{}}catch(e){}return t},L:function(e,t,i,s,r){if(c)try{var n="",o="",a=function(e,t){if(t){var i=function(e,t){if(void 0===t&&(t=c),tS)return tS;if(!t||["localhost","127.0.0.1"].includes(e))return"";for(var i=e.split("."),s=Math.min(i.length,8),r="dmn_chk_"+tE();!tS&&s--;){var n=i.slice(s).join("."),o=r+"=1;domain=."+n+";path=/";t.cookie=o+";max-age=3",t.cookie.includes(r)&&(t.cookie=o+";max-age=0",tS=n)}return tS}(e);if(!i){var s,r=(s=e.match(tx))?s[0]:"";r!==i&&j.info("Warning: cookie subdomain discovery mismatch",r,i),i=r}return i?"; domain=."+i:""}return""}(c.location.hostname,s);if(i){var l=new Date;l.setTime(l.getTime()+24*i*36e5),n="; expires="+l.toUTCString()}r&&(o="; secure");var u=e+"="+encodeURIComponent(JSON.stringify(t))+n+"; SameSite=Lax; path=/"+a+o;return u.length>3686.4&&j.warn("cookieStore warning: large cookie, len="+u.length),c.cookie=u,u}catch(e){return}},N:function(e,t){try{tI.L(e,"",-1,t)}catch(e){return}}},t$=null,tF={O:function(){if(!A(t$))return t$;var e=!0;if(T(s))e=!1;else try{var t="__mplssupport__";tF.L(t,"xyz"),'"xyz"'!==tF.A(t)&&(e=!1),tF.N(t)}catch(t){e=!1}return e||j.error("localStorage unsupported; falling back to cookie store"),t$=e,e},F:function(e){j.error("localStorage error: "+e)},A:function(e){try{return null==s?void 0:s.localStorage.getItem(e)}catch(e){tF.F(e)}return null},D:function(e){try{return JSON.parse(tF.A(e))||{}}catch(e){}return null},L:function(e,t){try{null==s||s.localStorage.setItem(e,JSON.stringify(t))}catch(e){tF.F(e)}},N:function(e){try{null==s||s.localStorage.removeItem(e)}catch(e){tF.F(e)}}},tR=["distinct_id",eS,ex,ej,eH],tP=G({},tF,{D:function(e){try{var t={};try{t=tI.D(e)||{}}catch(e){}var i=Z(t,JSON.parse(tF.A(e)||"{}"));return tF.L(e,i),i}catch(e){}return null},L:function(e,t,i,s,r,n){try{tF.L(e,t,void 0,void 0,n);var o={};tR.forEach(e=>{t[e]&&(o[e]=t[e])}),Object.keys(o).length&&tI.L(e,o,i,s,r,n)}catch(e){tF.F(e)}},N:function(e,t){try{null==s||s.localStorage.removeItem(e),tI.N(e,t)}catch(e){tF.F(e)}}}),tM={},tT={O:function(){return!0},F:function(e){j.error("memoryStorage error: "+e)},A:function(e){return tM[e]||null},D:function(e){return tM[e]||null},L:function(e,t){tM[e]=t},N:function(e){delete tM[e]}},tC=null,tO={O:function(){if(!A(tC))return tC;if(tC=!0,T(s))tC=!1;else try{var e="__support__";tO.L(e,"xyz"),'"xyz"'!==tO.A(e)&&(tC=!1),tO.N(e)}catch(e){tC=!1}return tC},F:function(e){j.error("sessionStorage error: ",e)},A:function(e){try{return null==s?void 0:s.sessionStorage.getItem(e)}catch(e){tO.F(e)}return null},D:function(e){try{return JSON.parse(tO.A(e))||null}catch(e){}return null},L:function(e,t){try{null==s||s.sessionStorage.setItem(e,JSON.stringify(t))}catch(e){tO.F(e)}},N:function(e){try{null==s||s.sessionStorage.removeItem(e)}catch(e){tO.F(e)}}},tA=function(e){return e[e.PENDING=-1]="PENDING",e[e.DENIED=0]="DENIED",e[e.GRANTED=1]="GRANTED",e}({});class tL{constructor(e){this._instance=e}get S(){return this._instance.config}get consent(){return this.j()?tA.DENIED:this.U}isOptedOut(){return this.consent===tA.DENIED||this.consent===tA.PENDING&&this.S.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(e){this.q.L(this.B,+!!e,this.S.cookie_expiration,this.S.cross_subdomain_cookie,this.S.secure_cookie)}reset(){this.q.N(this.B,this.S.cross_subdomain_cookie)}get B(){var{token:e,opt_out_capturing_cookie_prefix:t}=this._instance.config;return(t||"__ph_opt_in_out_")+e}get U(){var e=this.q.A(this.B);return"1"===e?tA.GRANTED:"0"===e?tA.DENIED:tA.PENDING}get q(){if(!this.H){var e=this.S.opt_out_capturing_persistence_type;this.H="localStorage"===e?tF:tI;var t="localStorage"===e?tI:tF;t.A(this.B)&&(this.H.A(this.B)||this.optInOut("1"===t.A(this.B)),t.N(this.B,this.S.cross_subdomain_cookie))}return this.H}j(){return!!this.S.respect_dnt&&!!er([null==l?void 0:l.doNotTrack,null==l?void 0:l.msDoNotTrack,_.doNotTrack],e=>w([!0,1,"1","yes"],e))}}var tD=U("[Dead Clicks]"),tN=()=>!0,tq=e=>{var t,i=!(null==(t=e.instance.persistence)||!t.get_property(eg)),s=e.instance.config.capture_dead_clicks;return N(s)?s:i};class tB{get lazyLoadedDeadClicksAutocapture(){return this.W}constructor(e,t,i){this.instance=e,this.isEnabled=t,this.onCapture=i,this.startIfEnabled()}onRemoteConfig(e){this.instance.persistence&&this.instance.persistence.register({[eg]:null==e?void 0:e.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.G(()=>{this.J()})}G(e){var t,i;null!=(t=_.__PosthogExtensions__)&&t.initDeadClicksAutocapture&&e(),null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this.instance,"dead-clicks-autocapture",t=>{t?tD.error("failed to load script",t):e()})}J(){var e;if(c){if(!this.W&&null!=(e=_.__PosthogExtensions__)&&e.initDeadClicksAutocapture){var t=P(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};t.__onCapture=this.onCapture,this.W=_.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,t),this.W.start(c),tD.info("starting...")}}else tD.error("`document` not found. Cannot start.")}stop(){this.W&&(this.W.stop(),this.W=void 0,tD.info("stopping..."))}}var tH=U("[ExceptionAutocapture]");class tj{constructor(e){var t;this.V=()=>{var e;if(s&&this.isEnabled&&null!=(e=_.__PosthogExtensions__)&&e.errorWrappingFunctions){var t=_.__PosthogExtensions__.errorWrappingFunctions.wrapOnError,i=_.__PosthogExtensions__.errorWrappingFunctions.wrapUnhandledRejection,r=_.__PosthogExtensions__.errorWrappingFunctions.wrapConsoleError;try{!this.K&&this.S.capture_unhandled_errors&&(this.K=t(this.captureException.bind(this))),!this.Y&&this.S.capture_unhandled_rejections&&(this.Y=i(this.captureException.bind(this))),!this.X&&this.S.capture_console_errors&&(this.X=r(this.captureException.bind(this)))}catch(e){tH.error("failed to start",e),this.Z()}}},this._instance=e,this.tt=!(null==(t=this._instance.persistence)||!t.props[eh]),this.S=this.it(),this.startIfEnabled()}it(){var e=this._instance.config.capture_exceptions,t={capture_unhandled_errors:!1,capture_unhandled_rejections:!1,capture_console_errors:!1};return P(e)?t=G({},t,e):(T(e)?this.tt:e)&&(t=G({},t,{capture_unhandled_errors:!0,capture_unhandled_rejections:!0})),t}get isEnabled(){return this.S.capture_console_errors||this.S.capture_unhandled_errors||this.S.capture_unhandled_rejections}startIfEnabled(){this.isEnabled&&(tH.info("enabled"),this.G(this.V))}G(e){var t,i;null!=(t=_.__PosthogExtensions__)&&t.errorWrappingFunctions&&e(),null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"exception-autocapture",t=>{if(t)return tH.error("failed to load script",t);e()})}Z(){var e,t,i;null==(e=this.K)||e.call(this),this.K=void 0,null==(t=this.Y)||t.call(this),this.Y=void 0,null==(i=this.X)||i.call(this),this.X=void 0}onRemoteConfig(e){var t=e.autocaptureExceptions;this.tt=!!t,this.S=this.it(),this._instance.persistence&&this._instance.persistence.register({[eh]:this.tt}),this.startIfEnabled()}captureException(e){e.$exception_personURL=this._instance.requestRouter.endpointFor("ui")+"/project/"+this._instance.config.token+"/person/"+this._instance.get_distinct_id(),this._instance.exceptions.sendExceptionEvent(e)}}function tU(e){return!T(Event)&&tz(e,Event)}function tz(e,t){try{return e instanceof t}catch(e){return!1}}function tW(e){switch(Object.prototype.toString.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object DOMError]":return!0;default:return tz(e,Error)}}function tG(e,t){return Object.prototype.toString.call(e)==="[object "+t+"]"}function tV(e){return tG(e,"DOMError")}var tY=/\(error: (.*)\)/;function tX(e,t,i,s){var r={platform:"web:javascript",filename:e,function:"<anonymous>"===t?"?":t,in_app:!0};return T(i)||(r.lineno=i),T(s)||(r.colno=s),r}var tJ,tZ,tK,tQ=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,t0=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,t1=/\((\S*)(?::(\d+))(?::(\d+))\)/,t2=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,t3=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,t5=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];var s=t.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return function(e,t){void 0===t&&(t=0);for(var i=[],r=e.split("\n"),n=t;n<r.length;n++){var o=r[n];if(!(o.length>1024)){var a=tY.test(o)?o.replace(tY,"$1"):o;if(!a.match(/\S*Error: /)){for(var l of s){var c=l(a);if(c){i.push(c);break}}if(i.length>=50)break}}}if(!i.length)return[];var u=Array.from(i);return u.reverse(),u.slice(0,50).map(e=>{var t;return G({},e,{filename:e.filename||((t=u)[t.length-1]||{}).filename,function:e.function||"?"})})}}([30,e=>{var t=tQ.exec(e);if(t){var[,i,s,r]=t;return tX(i,"?",+s,+r)}var n=t0.exec(e);if(n){if(n[2]&&0===n[2].indexOf("eval")){var o=t1.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}var[a,l]=t6(n[1]||"?",n[2]);return tX(l,a,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,e=>{var t=t2.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){var i=t3.exec(t[3]);i&&(t[1]=t[1]||"eval",t[3]=i[1],t[4]=i[2],t[5]="")}var s=t[3],r=t[1]||"?";return[r,s]=t6(r,s),tX(s,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),t6=(e,t)=>{var i=-1!==e.indexOf("safari-extension"),s=-1!==e.indexOf("safari-web-extension");return i||s?[-1!==e.indexOf("@")?e.split("@")[0]:"?",i?"safari-extension:"+t:"safari-web-extension:"+t]:[e,t]},t4=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function t8(e,t){void 0===t&&(t=0);var i=e.stacktrace||e.stack||"",s=e&&t7.test(e.message)?1:0;try{var r,n,o=(r=t5(i,s),n=function(e){var t=globalThis._posthogChunkIds;if(!t)return{};var i=Object.keys(t);return tK&&i.length===tZ||(tZ=i.length,tK=i.reduce((i,s)=>{tJ||(tJ={});var r=tJ[s];if(r)i[r[0]]=r[1];else for(var n=e(s),o=n.length-1;o>=0;o--){var a=n[o],l=null==a?void 0:a.filename,c=t[s];if(l&&c){i[l]=c,tJ[s]=[l,c];break}}return i},{})),tK}(t5),r.forEach(e=>{e.filename&&(e.chunk_id=n[e.filename])}),r);return o.slice(0,o.length-t)}catch(e){}return[]}var t7=/Minified React error #\d+;/i;function t9(e,t){return{$exception_list:function e(t,i){var s,r,n,o,a,l,c,u,h=(s=t,r=i,l=t8(s),c=null===(o=null==r?void 0:r.handled)||void 0===o||o,u=null!==(a=null==r?void 0:r.synthetic)&&void 0!==a&&a,{type:null!=r&&r.overrideExceptionType?r.overrideExceptionType:s.name,value:(n=s.message).error&&"string"==typeof n.error.message?String(n.error.message):String(n),stacktrace:{frames:l,type:"raw"},mechanism:{handled:c,synthetic:u}});return t.cause&&tW(t.cause)&&t.cause!==t?[h,...e(t.cause,{handled:null==i?void 0:i.handled,synthetic:null==i?void 0:i.synthetic})]:[h]}(e,t),$exception_level:"error"}}function ie(e,t){var i,s,r,n=null===(i=null==t?void 0:t.handled)||void 0===i||i,o=null===(s=null==t?void 0:t.synthetic)||void 0===s||s,a={type:null!=t&&t.overrideExceptionType?t.overrideExceptionType:null!==(r=null==t?void 0:t.defaultExceptionType)&&void 0!==r?r:"Error",value:e||(null==t?void 0:t.defaultExceptionMessage),mechanism:{handled:n,synthetic:o}};if(null!=t&&t.syntheticException){var l=t8(t.syntheticException,1);l.length&&(a.stacktrace={frames:l,type:"raw"})}return{$exception_list:[a],$exception_level:"error"}}function it(e,t,i){try{if(!(t in e))return()=>{};var s=e[t],r=i(s);return R(r)&&(r.prototype=r.prototype||{},Object.defineProperties(r,{__posthog_wrapped__:{enumerable:!1,value:!0}})),e[t]=r,()=>{e[t]=s}}catch(e){return()=>{}}}class ii{constructor(e){var t;this._instance=e,this.et=(null==s||null==(t=s.location)?void 0:t.pathname)||""}get isEnabled(){return"history_change"===this._instance.config.capture_pageview}startIfEnabled(){this.isEnabled&&(j.info("History API monitoring enabled, starting..."),this.monitorHistoryChanges())}stop(){this.rt&&this.rt(),this.rt=void 0,j.info("History API monitoring stopped")}monitorHistoryChanges(){var e,t;if(s&&s.history){var i=this;null!=(e=s.history.pushState)&&e.__posthog_wrapped__||it(s.history,"pushState",e=>function(t,s,r){e.call(this,t,s,r),i.st("pushState")}),null!=(t=s.history.replaceState)&&t.__posthog_wrapped__||it(s.history,"replaceState",e=>function(t,s,r){e.call(this,t,s,r),i.st("replaceState")}),this.nt()}}st(e){try{var t,i=null==s||null==(t=s.location)?void 0:t.pathname;if(!i)return;i!==this.et&&this.isEnabled&&this._instance.capture("$pageview",{navigation_type:e}),this.et=i}catch(t){j.error("Error capturing "+e+" pageview",t)}}nt(){if(!this.rt){var e=()=>{this.st("popstate")};en(s,"popstate",e),this.rt=()=>{s&&s.removeEventListener("popstate",e)}}}}function is(e){var t,i;return(null==(t=JSON.stringify(e,(i=[],function(e,t){if(P(t)){for(;i.length>0&&i[i.length-1]!==this;)i.pop();return i.includes(t)?"[Circular]":(i.push(t),t)}return t})))?void 0:t.length)||0}var ir=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(ir||{}),io=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(io||{}),ia="[SessionRecording]",il="redacted",ic={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:e=>e,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io",".clarity.ms","analytics.google.com","bam.nr-data.net"]},iu=["authorization","x-forwarded-for","authorization","cookie","set-cookie","x-api-key","x-real-ip","remote-addr","forwarded","proxy-authorization","x-csrf-token","x-csrftoken","x-xsrf-token"],ih=["password","secret","passwd","api_key","apikey","auth","credentials","mysql_pwd","privatekey","private_key","token"],id=["/s/","/e/","/i/"];function ip(e,t,i,s){if(L(e))return e;var r=(null==t?void 0:t["content-length"])||new Blob([e]).size;return C(r)&&(r=parseInt(r)),r>i?ia+" "+s+" body too large to record ("+r+" bytes)":e}function ig(e,t){if(L(e))return e;var i=e;return ts(i,!1)||(i=ia+" "+t+" body "+il),J(ih,e=>{var s,r;null!=(s=i)&&s.length&&-1!==(null==(r=i)?void 0:r.indexOf(e))&&(i=ia+" "+t+" body "+il+" as might contain: "+e)}),i}var i_=(e,t)=>{var i,s,r={payloadSizeLimitBytes:ic.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...ic.performanceEntryTypeToObserve],payloadHostDenyList:[...t.payloadHostDenyList||[],...ic.payloadHostDenyList]},n=!1!==e.session_recording.recordHeaders&&t.recordHeaders,o=!1!==e.session_recording.recordBody&&t.recordBody,a=!1!==e.capture_performance&&t.recordPerformance,l=(s=Math.min(1e6,null!==(i=r.payloadSizeLimitBytes)&&void 0!==i?i:1e6),e=>(null!=e&&e.requestBody&&(e.requestBody=ip(e.requestBody,e.requestHeaders,s,"Request")),null!=e&&e.responseBody&&(e.responseBody=ip(e.responseBody,e.responseHeaders,s,"Response")),e)),c=t=>{var i;return l(((e,t)=>{var i,s=tl(e.name),r=0===t.indexOf("http")?null==(i=tl(t))?void 0:i.pathname:t;"/"===r&&(r="");var n=null==s?void 0:s.pathname.replace(r||"","");if(!(s&&n&&id.some(e=>0===n.indexOf(e))))return e})((L(i=t.requestHeaders)||J(Object.keys(null!=i?i:{}),e=>{iu.includes(e.toLowerCase())&&(i[e]=il)}),t),e.api_host))},u=R(e.session_recording.maskNetworkRequestFn);return u&&R(e.session_recording.maskCapturedNetworkRequestFn)&&j.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."),u&&(e.session_recording.maskCapturedNetworkRequestFn=t=>{var i=e.session_recording.maskNetworkRequestFn({url:t.name});return G({},t,{name:null==i?void 0:i.url})}),r.maskRequestFn=R(e.session_recording.maskCapturedNetworkRequestFn)?t=>{var i,s=c(t);return s&&null!==(i=null==e.session_recording.maskCapturedNetworkRequestFn?void 0:e.session_recording.maskCapturedNetworkRequestFn(s))&&void 0!==i?i:void 0}:e=>(function(e){if(!T(e))return e.requestBody=ig(e.requestBody,"Request"),e.responseBody=ig(e.responseBody,"Response"),e})(c(e)),G({},ic,r,{recordHeaders:n,recordBody:o,recordPerformance:a,recordInitialRequests:a})};function iv(e,t,i,s,r){return t>i&&(j.warn("min cannot be greater than max."),t=i),D(e)?e>i?(s&&j.warn(s+" cannot be  greater than max: "+i+". Using max value instead."),i):e<t?(s&&j.warn(s+" cannot be less than min: "+t+". Using min value instead."),t):e:(s&&j.warn(s+" must be a number. using max or fallback. max: "+i+", fallback: "+r),iv(r||i,t,i,s))}class im{constructor(e,t){var i,s;void 0===t&&(t={}),this.ot=100,this.lt=10,this.ut={},this.ht={},this.dt=()=>{Object.keys(this.ut).forEach(e=>{this.ut[e]=this.ut[e]+this.lt,this.ut[e]>=this.ot&&delete this.ut[e]})},this.vt=e=>{var t=this._rrweb.mirror.getNode(e);if("svg"!==(null==t?void 0:t.nodeName)&&t instanceof Element){var i=t.closest("svg");if(i)return[this._rrweb.mirror.getId(i),i]}return[e,t]},this.ct=e=>{var t,i,s,r,n,o,a,l;return(null!==(t=null==(i=e.removes)?void 0:i.length)&&void 0!==t?t:0)+(null!==(s=null==(r=e.attributes)?void 0:r.length)&&void 0!==s?s:0)+(null!==(n=null==(o=e.texts)?void 0:o.length)&&void 0!==n?n:0)+(null!==(a=null==(l=e.adds)?void 0:l.length)&&void 0!==a?a:0)},this.throttleMutations=e=>{if(3!==e.type||0!==e.data.source)return e;var t=e.data,i=this.ct(t);t.attributes&&(t.attributes=t.attributes.filter(e=>{var t,i,s,[r,n]=this.vt(e.id);return 0!==this.ut[r]&&(this.ut[r]=null!==(t=this.ut[r])&&void 0!==t?t:this.ot,this.ut[r]=Math.max(this.ut[r]-1,0),0===this.ut[r]&&(this.ht[r]||(this.ht[r]=!0,null==(i=(s=this.ft).onBlockedNode)||i.call(s,r,n))),e)}));var s=this.ct(t);return 0!==s||i===s?e:void 0},this._rrweb=e,this.ft=t,this.lt=iv(null!==(i=this.ft.refillRate)&&void 0!==i?i:this.lt,0,100,"mutation throttling refill rate"),this.ot=iv(null!==(s=this.ft.bucketSize)&&void 0!==s?s:this.ot,0,100,"mutation throttling bucket size"),setInterval(()=>{this.dt()},1e3)}}var iy=Uint8Array,ib=Uint16Array,iw=Uint32Array,iE=new iy([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),ik=new iy([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),iS=new iy([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),ix=function(e,t){for(var i=new ib(31),s=0;s<31;++s)i[s]=t+=1<<e[s-1];var r=new iw(i[30]);for(s=1;s<30;++s)for(var n=i[s];n<i[s+1];++n)r[n]=n-i[s]<<5|s;return[i,r]},iI=ix(iE,2),i$=iI[0],iF=iI[1];i$[28]=258,iF[258]=28;for(var iR=ix(ik,0)[1],iP=new ib(32768),iM=0;iM<32768;++iM){var iT=(43690&iM)>>>1|(21845&iM)<<1;iT=(61680&(iT=(52428&iT)>>>2|(13107&iT)<<2))>>>4|(3855&iT)<<4,iP[iM]=((65280&iT)>>>8|(255&iT)<<8)>>>1}var iC=function(e,t,i){for(var s=e.length,r=0,n=new ib(t);r<s;++r)++n[e[r]-1];var o,a=new ib(t);for(r=0;r<t;++r)a[r]=a[r-1]+n[r-1]<<1;if(i){o=new ib(1<<t);var l=15-t;for(r=0;r<s;++r)if(e[r])for(var c=r<<4|e[r],u=t-e[r],h=a[e[r]-1]++<<u,d=h|(1<<u)-1;h<=d;++h)o[iP[h]>>>l]=c}else for(o=new ib(s),r=0;r<s;++r)o[r]=iP[a[e[r]-1]++]>>>15-e[r];return o},iO=new iy(288);for(iM=0;iM<144;++iM)iO[iM]=8;for(iM=144;iM<256;++iM)iO[iM]=9;for(iM=256;iM<280;++iM)iO[iM]=7;for(iM=280;iM<288;++iM)iO[iM]=8;var iA=new iy(32);for(iM=0;iM<32;++iM)iA[iM]=5;var iL=iC(iO,9,0),iD=iC(iA,5,0),iN=function(e){return(e/8>>0)+(7&e&&1)},iq=function(e,t,i){(null==i||i>e.length)&&(i=e.length);var s=new(e instanceof ib?ib:e instanceof iw?iw:iy)(i-t);return s.set(e.subarray(t,i)),s},iB=function(e,t,i){i<<=7&t;var s=t/8>>0;e[s]|=i,e[s+1]|=i>>>8},iH=function(e,t,i){i<<=7&t;var s=t/8>>0;e[s]|=i,e[s+1]|=i>>>8,e[s+2]|=i>>>16},ij=function(e,t){for(var i=[],s=0;s<e.length;++s)e[s]&&i.push({s:s,f:e[s]});var r=i.length,n=i.slice();if(!r)return[new iy(0),0];if(1==r){var o=new iy(i[0].s+1);return o[i[0].s]=1,[o,1]}i.sort(function(e,t){return e.f-t.f}),i.push({s:-1,f:25001});var a=i[0],l=i[1],c=0,u=1,h=2;for(i[0]={s:-1,f:a.f+l.f,l:a,r:l};u!=r-1;)a=i[i[c].f<i[h].f?c++:h++],l=i[c!=u&&i[c].f<i[h].f?c++:h++],i[u++]={s:-1,f:a.f+l.f,l:a,r:l};var d=n[0].s;for(s=1;s<r;++s)n[s].s>d&&(d=n[s].s);var p=new ib(d+1),g=iU(i[u-1],p,0);if(g>t){s=0;var _=0,v=g-t,f=1<<v;for(n.sort(function(e,t){return p[t.s]-p[e.s]||e.f-t.f});s<r;++s){var m=n[s].s;if(!(p[m]>t))break;_+=f-(1<<g-p[m]),p[m]=t}for(_>>>=v;_>0;){var y=n[s].s;p[y]<t?_-=1<<t-p[y]++-1:++s}for(;s>=0&&_;--s){var b=n[s].s;p[b]==t&&(--p[b],++_)}g=t}return[new iy(p),g]},iU=function(e,t,i){return -1==e.s?Math.max(iU(e.l,t,i+1),iU(e.r,t,i+1)):t[e.s]=i},iz=function(e){for(var t=e.length;t&&!e[--t];);for(var i=new ib(++t),s=0,r=e[0],n=1,o=function(e){i[s++]=e},a=1;a<=t;++a)if(e[a]==r&&a!=t)++n;else{if(!r&&n>2){for(;n>138;n-=138)o(32754);n>2&&(o(n>10?n-11<<5|28690:n-3<<5|12305),n=0)}else if(n>3){for(o(r),--n;n>6;n-=6)o(8304);n>2&&(o(n-3<<5|8208),n=0)}for(;n--;)o(r);n=1,r=e[a]}return[i.subarray(0,s),t]},iW=function(e,t){for(var i=0,s=0;s<t.length;++s)i+=e[s]*t[s];return i},iG=function(e,t,i){var s=i.length,r=iN(t+2);e[r]=255&s,e[r+1]=s>>>8,e[r+2]=255^e[r],e[r+3]=255^e[r+1];for(var n=0;n<s;++n)e[r+n+4]=i[n];return 8*(r+4+s)},iV=function(e,t,i,s,r,n,o,a,l,c,u){iB(t,u++,i),++r[256];for(var h=ij(r,15),d=h[0],p=h[1],g=ij(n,15),_=g[0],v=g[1],f=iz(d),m=f[0],y=f[1],b=iz(_),w=b[0],E=b[1],k=new ib(19),S=0;S<m.length;++S)k[31&m[S]]++;for(S=0;S<w.length;++S)k[31&w[S]]++;for(var x=ij(k,7),I=x[0],$=x[1],F=19;F>4&&!I[iS[F-1]];--F);var R,P,M,T,C=c+5<<3,O=iW(r,iO)+iW(n,iA)+o,A=iW(r,d)+iW(n,_)+o+14+3*F+iW(k,I)+(2*k[16]+3*k[17]+7*k[18]);if(C<=O&&C<=A)return iG(t,u,e.subarray(l,l+c));if(iB(t,u,1+(A<O)),u+=2,A<O){R=iC(d,p,0),P=d,M=iC(_,v,0),T=_;var L=iC(I,$,0);for(iB(t,u,y-257),iB(t,u+5,E-1),iB(t,u+10,F-4),u+=14,S=0;S<F;++S)iB(t,u+3*S,I[iS[S]]);u+=3*F;for(var D=[m,w],N=0;N<2;++N){var q=D[N];for(S=0;S<q.length;++S){var B=31&q[S];iB(t,u,L[B]),u+=I[B],B>15&&(iB(t,u,q[S]>>>5&127),u+=q[S]>>>12)}}}else R=iL,P=iO,M=iD,T=iA;for(S=0;S<a;++S)if(s[S]>255){iH(t,u,R[(B=s[S]>>>18&31)+257]),u+=P[B+257],B>7&&(iB(t,u,s[S]>>>23&31),u+=iE[B]);var H=31&s[S];iH(t,u,M[H]),u+=T[H],H>3&&(iH(t,u,s[S]>>>5&8191),u+=ik[H])}else iH(t,u,R[s[S]]),u+=P[s[S]];return iH(t,u,R[256]),u+P[256]},iY=new iw([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),iX=function(){for(var e=new iw(256),t=0;t<256;++t){for(var i=t,s=9;--s;)i=(1&i&&0xedb88320)^i>>>1;e[t]=i}return e}(),iJ=function(){var e=0xffffffff;return{p:function(t){for(var i=e,s=0;s<t.length;++s)i=iX[255&i^t[s]]^i>>>8;e=i},d:function(){return 0xffffffff^e}}},iZ=function(e,t,i){for(;i;++t)e[t]=i,i>>>=8},iK=function(e,t){var i=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:2*(9==t.level),e[9]=3,0!=t.mtime&&iZ(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),i){e[3]=8;for(var s=0;s<=i.length;++s)e[s+10]=i.charCodeAt(s)}};function iQ(e,t){void 0===t&&(t={});var i,s,r,n=iJ(),o=e.length;n.p(e);var a=(s=t,r=10+((i=t).filename&&i.filename.length+1||0),function(e,t,i,s,r,n){var o=e.length,a=new iy(s+o+5*(1+Math.floor(o/7e3))+8),l=a.subarray(s,a.length-r),c=0;if(!t||o<8)for(var u=0;u<=o;u+=65535){var h=u+65535;h<o?c=iG(l,c,e.subarray(u,h)):(l[u]=n,c=iG(l,c,e.subarray(u,o)))}else{for(var d=iY[t-1],p=d>>>13,g=8191&d,_=(1<<i)-1,v=new ib(32768),f=new ib(_+1),m=Math.ceil(i/3),y=2*m,b=function(t){return(e[t]^e[t+1]<<m^e[t+2]<<y)&_},w=new iw(25e3),E=new ib(288),k=new ib(32),S=0,x=0,I=(u=0,0),$=0,F=0;u<o;++u){var R=b(u),P=32767&u,M=f[R];if(v[P]=M,f[R]=P,$<=u){var T=o-u;if((S>7e3||I>24576)&&T>423){c=iV(e,l,0,w,E,k,x,I,F,u-F,c),I=S=x=0,F=u;for(var C=0;C<286;++C)E[C]=0;for(C=0;C<30;++C)k[C]=0}var O=2,A=0,L=g,D=P-M&32767;if(T>2&&R==b(u-D))for(var N=Math.min(p,T)-1,q=Math.min(32767,u),B=Math.min(258,T);D<=q&&--L&&P!=M;){if(e[u+O]==e[u+O-D]){for(var H=0;H<B&&e[u+H]==e[u+H-D];++H);if(H>O){if(O=H,A=D,H>N)break;var j=Math.min(D,H-2),U=0;for(C=0;C<j;++C){var z=u-D+C+32768&32767,W=z-v[z]+32768&32767;W>U&&(U=W,M=z)}}}D+=(P=M)-(M=v[P])+32768&32767}if(A){w[I++]=0x10000000|iF[O]<<18|iR[A];var G=31&iF[O],V=31&iR[A];x+=iE[G]+ik[V],++E[257+G],++k[V],$=u+O,++S}else w[I++]=e[u],++E[e[u]]}}c=iV(e,l,n,w,E,k,x,I,F,u-F,c)}return iq(a,0,s+iN(c)+r)}(e,null==s.level?6:s.level,null==s.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):12+s.mem,r,8,!0)),l=a.length;return iK(a,t),iZ(a,l-8,n.d()),iZ(a,l-4,o),a}function i0(e,t){var i=e.length;if("undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);for(var s=new iy(e.length+(e.length>>>1)),r=0,n=function(e){s[r++]=e},o=0;o<i;++o){if(r+5>s.length){var a=new iy(r+8+(i-o<<1));a.set(s),s=a}var l=e.charCodeAt(o);l<128||t?n(l):(l<2048?n(192|l>>>6):(l>55295&&l<57344?(n(240|(l=65536+(1047552&l)|1023&e.charCodeAt(++o))>>>18),n(128|l>>>12&63)):n(224|l>>>12),n(128|l>>>6&63)),n(128|63&l))}return iq(s,0,r)}var i1="disabled",i2="sampled",i3="active",i5="buffering",i6="paused",i4="trigger",i8=i4+"_activated",i7=i4+"_pending",i9=i4+"_"+i1;function se(e,t){return t.some(t=>"regex"===t.matching&&new RegExp(t.url).test(e))}class st{constructor(e){this._t=e}triggerStatus(e){var t=this._t.map(t=>t.triggerStatus(e));return t.includes(i8)?i8:t.includes(i7)?i7:i9}stop(){this._t.forEach(e=>e.stop())}}class si{constructor(e){this._t=e}triggerStatus(e){var t=new Set;for(var i of this._t)t.add(i.triggerStatus(e));switch(t.delete(i9),t.size){case 0:return i9;case 1:return Array.from(t)[0];default:return i7}}stop(){this._t.forEach(e=>e.stop())}}class ss{triggerStatus(){return i7}stop(){}}class sr{constructor(e){this.gt=[],this.bt=[],this.urlBlocked=!1,this._instance=e}onRemoteConfig(e){var t,i;this.gt=(null==(t=e.sessionRecording)?void 0:t.urlTriggers)||[],this.bt=(null==(i=e.sessionRecording)?void 0:i.urlBlocklist)||[]}yt(e){var t;return 0===this.gt.length?i9:(null==(t=this._instance)?void 0:t.get_property(eI))===e?i8:i7}triggerStatus(e){var t=this.yt(e),i=t===i8?i8:t===i7?i7:i9;return this._instance.register_for_session({$sdk_debug_replay_url_trigger_status:i}),i}checkUrlTriggerConditions(e,t,i){if(void 0!==s&&s.location.href){var r=s.location.href,n=this.urlBlocked,o=se(r,this.bt);n&&o||(o&&!n?e():!o&&n&&t(),se(r,this.gt)&&i("url"))}}stop(){}}class sn{constructor(e){this.linkedFlag=null,this.linkedFlagSeen=!1,this.wt=()=>{},this._instance=e}triggerStatus(){var e=i7;return L(this.linkedFlag)&&(e=i9),this.linkedFlagSeen&&(e=i8),this._instance.register_for_session({$sdk_debug_replay_linked_flag_trigger_status:e}),e}onRemoteConfig(e,t){var i;if(this.linkedFlag=(null==(i=e.sessionRecording)?void 0:i.linkedFlag)||null,!L(this.linkedFlag)&&!this.linkedFlagSeen){var s=C(this.linkedFlag)?this.linkedFlag:this.linkedFlag.flag,r=C(this.linkedFlag)?null:this.linkedFlag.variant;this.wt=this._instance.onFeatureFlags((e,i)=>{var n=!1;if(P(i)&&s in i){var o=i[s];n=N(o)?!0===o:r?o===r:!!o}this.linkedFlagSeen=n,n&&t(s,r)})}}stop(){this.wt()}}class so{constructor(e){this.St=[],this._instance=e}onRemoteConfig(e){var t;this.St=(null==(t=e.sessionRecording)?void 0:t.eventTriggers)||[]}$t(e){var t;return 0===this.St.length?i9:(null==(t=this._instance)?void 0:t.get_property(e$))===e?i8:i7}triggerStatus(e){var t=this.$t(e),i=t===i8?i8:t===i7?i7:i9;return this._instance.register_for_session({$sdk_debug_replay_event_trigger_status:i}),i}stop(){}}function sa(e){return e.isRecordingEnabled?i5:i1}function sl(e){if(!e.receivedDecide)return i5;if(!e.isRecordingEnabled)return i1;if(e.urlTriggerMatching.urlBlocked)return i6;var t=!0===e.isSampled,i=new st([e.eventTriggerMatching,e.urlTriggerMatching,e.linkedFlagMatching]).triggerStatus(e.sessionId);return t?i2:i===i8?i3:i===i7?i5:!1===e.isSampled?i1:i3}function sc(e){if(!e.receivedDecide)return i5;if(!e.isRecordingEnabled)return i1;if(e.urlTriggerMatching.urlBlocked)return i6;var t=new si([e.eventTriggerMatching,e.urlTriggerMatching,e.linkedFlagMatching]).triggerStatus(e.sessionId),i=t!==i9,s=N(e.isSampled);return i&&t===i7?i5:i&&t===i9||s&&!e.isSampled?i1:!0===e.isSampled?i2:i3}var su="[SessionRecording]",sh=U(su);function sd(){var e;return null==_||null==(e=_.__PosthogExtensions__)||null==(e=e.rrweb)?void 0:e.record}var sp=[io.MouseMove,io.MouseInteraction,io.Scroll,io.ViewportResize,io.Input,io.TouchMove,io.MediaInteraction,io.Drag],sg=e=>({rrwebMethod:e,enqueuedAt:Date.now(),attempt:1});function s_(e){return function(e,t){for(var i="",s=0;s<e.length;){var r=e[s++];r<128||t?i+=String.fromCharCode(r):r<224?i+=String.fromCharCode((31&r)<<6|63&e[s++]):r<240?i+=String.fromCharCode((15&r)<<12|(63&e[s++])<<6|63&e[s++]):i+=String.fromCharCode(55296|(r=((15&r)<<18|(63&e[s++])<<12|(63&e[s++])<<6|63&e[s++])-65536)>>10,56320|1023&r)}return i}(iQ(i0(JSON.stringify(e))),!0)}function sv(e){return e.type===ir.Custom&&"sessionIdle"===e.data.tag}class sf{get sessionId(){return this.kt}get xt(){return this._instance.config.session_recording.session_idle_threshold_ms||3e5}get started(){return this.Et}get It(){if(!this._instance.sessionManager)throw Error(su+" must be started with a valid sessionManager.");return this._instance.sessionManager}get Pt(){var e,t;return this.Rt.triggerStatus(this.sessionId)===i7?6e4:null!==(e=null==(t=this._instance.config.session_recording)?void 0:t.full_snapshot_interval_millis)&&void 0!==e?e:3e5}get Tt(){var e=this._instance.get_property(ex);return N(e)?e:null}get Mt(){var e,t,i=null==(e=this.M)?void 0:e.data[(null==(t=this.M)?void 0:t.data.length)-1],{sessionStartTimestamp:s}=this.It.checkAndGetSessionAndWindowId(!0);return i?i.timestamp-s:null}get Ct(){var e=!!this._instance.get_property(ev),t=!this._instance.config.disable_session_recording;return s&&e&&t}get Ot(){var e=!!this._instance.get_property(ef),t=this._instance.config.enable_recording_console_log;return null!=t?t:e}get Ft(){var e,t,i,s,r,n,o=this._instance.config.session_recording.captureCanvas,a=this._instance.get_property(eb),l=null!==(e=null!==(t=null==o?void 0:o.recordCanvas)&&void 0!==t?t:null==a?void 0:a.enabled)&&void 0!==e&&e,c=null!==(i=null!==(s=null==o?void 0:o.canvasFps)&&void 0!==s?s:null==a?void 0:a.fps)&&void 0!==i?i:4,u=null!==(r=null!==(n=null==o?void 0:o.canvasQuality)&&void 0!==n?n:null==a?void 0:a.quality)&&void 0!==r?r:.4;if("string"==typeof u){var h=parseFloat(u);u=isNaN(h)?.4:h}return{enabled:l,fps:iv(c,0,12,"canvas recording fps",4),quality:iv(u,0,1,"canvas recording quality",.4)}}get At(){var e,t,i=this._instance.get_property(em),s={recordHeaders:null==(e=this._instance.config.session_recording)?void 0:e.recordHeaders,recordBody:null==(t=this._instance.config.session_recording)?void 0:t.recordBody},r=(null==s?void 0:s.recordHeaders)||(null==i?void 0:i.recordHeaders),n=(null==s?void 0:s.recordBody)||(null==i?void 0:i.recordBody),o=P(this._instance.config.capture_performance)?this._instance.config.capture_performance.network_timing:this._instance.config.capture_performance,a=!!(N(o)?o:null==i?void 0:i.capturePerformance);return r||n||a?{recordHeaders:r,recordBody:n,recordPerformance:a}:void 0}get Dt(){var e,t,i,s,r,n,o=this._instance.get_property(ey),a={maskAllInputs:null==(e=this._instance.config.session_recording)?void 0:e.maskAllInputs,maskTextSelector:null==(t=this._instance.config.session_recording)?void 0:t.maskTextSelector,blockSelector:null==(i=this._instance.config.session_recording)?void 0:i.blockSelector},l=null!==(s=null==a?void 0:a.maskAllInputs)&&void 0!==s?s:null==o?void 0:o.maskAllInputs,c=null!==(r=null==a?void 0:a.maskTextSelector)&&void 0!==r?r:null==o?void 0:o.maskTextSelector,u=null!==(n=null==a?void 0:a.blockSelector)&&void 0!==n?n:null==o?void 0:o.blockSelector;return T(l)&&T(c)&&T(u)?void 0:{maskAllInputs:null==l||l,maskTextSelector:c,blockSelector:u}}get Lt(){var e=this._instance.get_property(ew);return D(e)?e:null}get Nt(){var e=this._instance.get_property(eE);return D(e)?e:null}get status(){return this.jt?this.zt({receivedDecide:this.jt,isRecordingEnabled:this.Ct,isSampled:this.Tt,urlTriggerMatching:this.Ut,eventTriggerMatching:this.qt,linkedFlagMatching:this.Bt,sessionId:this.sessionId}):i5}constructor(e){if(this.zt=sa,this.jt=!1,this.Ht=[],this.Wt="unknown",this.Gt=Date.now(),this.Rt=new ss,this.Jt=void 0,this.Vt=void 0,this.Kt=void 0,this.Yt=void 0,this.Xt=void 0,this._forceAllowLocalhostNetworkCapture=!1,this.Qt=()=>{this.Zt()},this.ti=()=>{this.ii("browser offline",{})},this.ei=()=>{this.ii("browser online",{})},this.ri=()=>{if(null!=c&&c.visibilityState){var e="window "+c.visibilityState;this.ii(e,{})}},this._instance=e,this.Et=!1,this.si="/s/",this.ni=void 0,this.jt=!1,!this._instance.sessionManager)throw sh.error("started without valid sessionManager"),Error(su+" started without valid sessionManager. This is a bug.");if(this._instance.config.__preview_experimental_cookieless_mode)throw Error(su+" cannot be used with __preview_experimental_cookieless_mode.");this.Bt=new sn(this._instance),this.Ut=new sr(this._instance),this.qt=new so(this._instance);var{sessionId:t,windowId:i}=this.It.checkAndGetSessionAndWindowId();this.kt=t,this.oi=i,this.M=this.ai(),this.xt>=this.It.sessionTimeoutMs&&sh.warn("session_idle_threshold_ms ("+this.xt+") is greater than the session timeout ("+this.It.sessionTimeoutMs+"). Session will never be detected as idle")}startIfEnabledOrStop(e){this.Ct?(this.li(e),en(s,"beforeunload",this.Qt),en(s,"offline",this.ti),en(s,"online",this.ei),en(s,"visibilitychange",this.ri),this.ui(),this.hi(),L(this.Jt)&&(this.Jt=this._instance.on("eventCaptured",e=>{try{if("$pageview"===e.event){var t=null!=e&&e.properties.$current_url?this.di(null==e?void 0:e.properties.$current_url):"";if(!t)return;this.ii("$pageview",{href:t})}}catch(e){sh.error("Could not add $pageview to rrweb session",e)}})),this.Vt||(this.Vt=this.It.onSessionId((e,t,i)=>{var s,r;i&&(this.ii("$session_id_change",{sessionId:e,windowId:t,changeReason:i}),null==(s=this._instance)||null==(s=s.persistence)||s.unregister(e$),null==(r=this._instance)||null==(r=r.persistence)||r.unregister(eI))}))):this.stopRecording()}stopRecording(){var e,t,i,r;this.Et&&this.ni&&(this.ni(),this.ni=void 0,this.Et=!1,null==s||s.removeEventListener("beforeunload",this.Qt),null==s||s.removeEventListener("offline",this.ti),null==s||s.removeEventListener("online",this.ei),null==s||s.removeEventListener("visibilitychange",this.ri),this.ai(),clearInterval(this.vi),null==(e=this.Jt)||e.call(this),this.Jt=void 0,null==(t=this.Xt)||t.call(this),this.Xt=void 0,null==(i=this.Vt)||i.call(this),this.Vt=void 0,null==(r=this.Yt)||r.call(this),this.Yt=void 0,this.qt.stop(),this.Ut.stop(),this.Bt.stop(),sh.info("stopped"))}ci(){var e;null==(e=this._instance.persistence)||e.unregister(ex)}fi(e){var t,i=this.kt!==e,s=this.Lt;if(D(s)){var r=this.Tt,n=i||!N(r),o=n?function(e){for(var t=0,i=0;i<e.length;i++)t=(t<<5)-t+e.charCodeAt(i)|0;return Math.abs(t)}(e)%100<iv(100*s,0,100):r;n&&(o?this.pi(i2):sh.warn("Sample rate ("+s+") has determined that this sessionId ("+e+") will not be sent to the server."),this.ii("samplingDecisionMade",{sampleRate:s,isSampled:o})),null==(t=this._instance.persistence)||t.register({[ex]:o})}else this.ci()}onRemoteConfig(e){var t,i,s,r;this.ii("$remote_config_received",e),this.gi(e),null!=(t=e.sessionRecording)&&t.endpoint&&(this.si=null==(r=e.sessionRecording)?void 0:r.endpoint),this.ui(),"any"===(null==(i=e.sessionRecording)?void 0:i.triggerMatchType)?(this.zt=sl,this.Rt=new st([this.qt,this.Ut])):(this.zt=sc,this.Rt=new si([this.qt,this.Ut])),this._instance.register_for_session({$sdk_debug_replay_remote_trigger_matching_config:null==(s=e.sessionRecording)?void 0:s.triggerMatchType}),this.Ut.onRemoteConfig(e),this.qt.onRemoteConfig(e),this.Bt.onRemoteConfig(e,(e,t)=>{this.pi("linked_flag_matched",{flag:e,variant:t})}),this.jt=!0,this.startIfEnabledOrStop()}ui(){D(this.Lt)&&L(this.Yt)&&(this.Yt=this.It.onSessionId(e=>{this.fi(e)}))}gi(e){if(this._instance.persistence){var t,i=this._instance.persistence,s=()=>{var t,s,r,n,o,a,l,c,u,h=null==(t=e.sessionRecording)?void 0:t.sampleRate,d=L(h)?null:parseFloat(h);L(d)&&this.ci();var p=null==(s=e.sessionRecording)?void 0:s.minimumDurationMilliseconds;i.register({[ev]:!!e.sessionRecording,[ef]:null==(r=e.sessionRecording)?void 0:r.consoleLogRecordingEnabled,[em]:G({capturePerformance:e.capturePerformance},null==(n=e.sessionRecording)?void 0:n.networkPayloadCapture),[ey]:null==(o=e.sessionRecording)?void 0:o.masking,[eb]:{enabled:null==(a=e.sessionRecording)?void 0:a.recordCanvas,fps:null==(l=e.sessionRecording)?void 0:l.canvasFps,quality:null==(c=e.sessionRecording)?void 0:c.canvasQuality},[ew]:d,[eE]:T(p)?null:p,[ek]:null==(u=e.sessionRecording)?void 0:u.scriptConfig})};s(),null==(t=this.Kt)||t.call(this),this.Kt=this.It.onSessionId(s)}}log(e,t){var i;void 0===t&&(t="log"),null==(i=this._instance.sessionRecording)||i.onRRwebEmit({type:6,data:{plugin:"rrweb/console@1",payload:{level:t,trace:[],payload:[JSON.stringify(e)]}},timestamp:Date.now()})}li(e){if(!T(Object.assign)&&!T(Array.from)&&!(this.Et||this._instance.config.disable_session_recording||this._instance.consent.isOptedOut())){var t;(this.Et=!0,this.It.checkAndGetSessionAndWindowId(),sd())?this.mi():null==(t=_.__PosthogExtensions__)||null==t.loadExternalDependency||t.loadExternalDependency(this._instance,this.bi,e=>{if(e)return sh.error("could not load recorder",e);this.mi()}),sh.info("starting"),this.status===i3&&this.pi(e||"recording_initialized")}}get bi(){var e;return(null==(e=this._instance)||null==(e=e.persistence)||null==(e=e.get_property(ek))?void 0:e.script)||"recorder"}yi(e){var t;return 3===e.type&&-1!==sp.indexOf(null==(t=e.data)?void 0:t.source)}wi(e){var t=this.yi(e);t||this.Wt||e.timestamp-this.Gt>this.xt&&(this.Wt=!0,clearInterval(this.vi),this.ii("sessionIdle",{eventTimestamp:e.timestamp,lastActivityTimestamp:this.Gt,threshold:this.xt,bufferLength:this.M.data.length,bufferSize:this.M.size}),this.Zt());var i=!1;if(t&&(this.Gt=e.timestamp,this.Wt)){var s="unknown"===this.Wt;this.Wt=!1,s||(this.ii("sessionNoLongerIdle",{reason:"user activity",type:e.type}),i=!0)}if(!this.Wt){var{windowId:r,sessionId:n}=this.It.checkAndGetSessionAndWindowId(!t,e.timestamp),o=this.kt!==n,a=this.oi!==r;this.oi=r,this.kt=n,o||a?(this.stopRecording(),this.startIfEnabledOrStop("session_id_changed")):i&&this.Si()}}$i(e){try{return e.rrwebMethod(),!0}catch(t){return this.Ht.length<10?this.Ht.push({enqueuedAt:e.enqueuedAt||Date.now(),attempt:e.attempt++,rrwebMethod:e.rrwebMethod}):sh.warn("could not emit queued rrweb event.",t,e),!1}}ii(e,t){return this.$i(sg(()=>sd().addCustomEvent(e,t)))}ki(){return this.$i(sg(()=>sd().takeFullSnapshot()))}mi(){var e,t,i,s,r={blockClass:"ph-no-capture",blockSelector:void 0,ignoreClass:"ph-ignore-input",maskTextClass:"ph-mask",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1};for(var[n,o]of Object.entries(this._instance.config.session_recording||{}))n in r&&("maskInputOptions"===n?r.maskInputOptions=G({password:!0},o):r[n]=o);this.Ft&&this.Ft.enabled&&(r.recordCanvas=!0,r.sampling={canvas:this.Ft.fps},r.dataURLOptions={type:"image/webp",quality:this.Ft.quality}),this.Dt&&(r.maskAllInputs=null===(t=this.Dt.maskAllInputs)||void 0===t||t,r.maskTextSelector=null!==(i=this.Dt.maskTextSelector)&&void 0!==i?i:void 0,r.blockSelector=null!==(s=this.Dt.blockSelector)&&void 0!==s?s:void 0);var a=sd();if(a){this.xi=null!==(e=this.xi)&&void 0!==e?e:new im(a,{refillRate:this._instance.config.session_recording.__mutationRateLimiterRefillRate,bucketSize:this._instance.config.session_recording.__mutationRateLimiterBucketSize,onBlockedNode:(e,t)=>{var i="Too many mutations on node '"+e+"'. Rate limiting. This could be due to SVG animations or something similar";sh.info(i,{node:t}),this.log(su+" "+i,"warn")}});var l=this.Ei();this.ni=a(G({emit:e=>{this.onRRwebEmit(e)},plugins:l},r)),this.Gt=Date.now(),this.Wt=N(this.Wt)?this.Wt:"unknown",this.ii("$session_options",{sessionRecordingOptions:r,activePlugins:l.map(e=>null==e?void 0:e.name)}),this.ii("$posthog_config",{config:this._instance.config})}else sh.error("onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")}Si(){if(this.vi&&clearInterval(this.vi),!0!==this.Wt){var e=this.Pt;e&&(this.vi=setInterval(()=>{this.ki()},e))}}Ei(){var e,t,i=[],s=null==(e=_.__PosthogExtensions__)||null==(e=e.rrwebPlugins)?void 0:e.getRecordConsolePlugin;s&&this.Ot&&i.push(s());var r=null==(t=_.__PosthogExtensions__)||null==(t=t.rrwebPlugins)?void 0:t.getRecordNetworkPlugin;return this.At&&R(r)&&(!ta.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?i.push(r(i_(this._instance.config,this.At))):sh.info("NetworkCapture not started because we are on localhost.")),i}onRRwebEmit(e){var t;if(this.Ii(),e&&P(e)){if(e.type===ir.Meta){var i=this.di(e.data.href);if(this.Pi=i,!i)return;e.data.href=i}else this.Ri();if(this.Ut.checkUrlTriggerConditions(()=>this.Ti(),()=>this.Mi(),e=>this.Ci(e)),!this.Ut.urlBlocked||e.type===ir.Custom&&"recording paused"===e.data.tag){e.type===ir.FullSnapshot&&this.Si(),e.type===ir.FullSnapshot&&this.jt&&this.Rt.triggerStatus(this.sessionId)===i7&&this.ai();var s=this.xi?this.xi.throttleMutations(e):e;if(s){var r=function(e){if(e&&P(e)&&6===e.type&&P(e.data)&&"rrweb/console@1"===e.data.plugin){e.data.payload.payload.length>10&&(e.data.payload.payload=e.data.payload.payload.slice(0,10),e.data.payload.payload.push("...[truncated]"));for(var t=[],i=0;i<e.data.payload.payload.length;i++)e.data.payload.payload[i]&&e.data.payload.payload[i].length>2e3?t.push(e.data.payload.payload[i].slice(0,2e3)+"...[truncated]"):t.push(e.data.payload.payload[i]);return e.data.payload.payload=t,e}return e}(s);if(this.wi(r),!0!==this.Wt||sv(r)){if(sv(r)){var n=r.data.payload;n&&(r.timestamp=n.lastActivityTimestamp+n.threshold)}var o=null===(t=this._instance.config.session_recording.compress_events)||void 0===t||t?function(e){if(1024>is(e))return e;try{if(e.type===ir.FullSnapshot)return G({},e,{data:s_(e.data),cv:"2024-10"});if(e.type===ir.IncrementalSnapshot&&e.data.source===io.Mutation)return G({},e,{cv:"2024-10",data:G({},e.data,{texts:s_(e.data.texts),attributes:s_(e.data.attributes),removes:s_(e.data.removes),adds:s_(e.data.adds)})});if(e.type===ir.IncrementalSnapshot&&e.data.source===io.StyleSheetRule)return G({},e,{cv:"2024-10",data:G({},e.data,{adds:e.data.adds?s_(e.data.adds):void 0,removes:e.data.removes?s_(e.data.removes):void 0})})}catch(e){sh.error("could not compress event - will use uncompressed event",e)}return e}(r):r,a={$snapshot_bytes:is(o),$snapshot_data:o,$session_id:this.kt,$window_id:this.oi};this.status!==i1?this.Oi(a):this.ai()}}}}}Ri(){if(!this._instance.config.capture_pageview&&s){var e=this.di(s.location.href);this.Pi!==e&&(this.ii("$url_changed",{href:e}),this.Pi=e)}}Ii(){if(this.Ht.length){var e=[...this.Ht];this.Ht=[],e.forEach(e=>{Date.now()-e.enqueuedAt<=2e3&&this.$i(e)})}}di(e){var t=this._instance.config.session_recording;if(t.maskNetworkRequestFn){var i,s={url:e};return null==(i=s=t.maskNetworkRequestFn(s))?void 0:i.url}return e}ai(){return this.M={size:0,data:[],sessionId:this.kt,windowId:this.oi},this.M}Zt(){this.Fi&&(clearTimeout(this.Fi),this.Fi=void 0);var e=this.Nt,t=this.Mt,i=D(t)&&t>=0,s=D(e)&&i&&t<e;return this.status===i5||this.status===i6||this.status===i1||s?(this.Fi=setTimeout(()=>{this.Zt()},2e3),this.M):(this.M.data.length>0&&(function e(t,i){if(void 0===i&&(i=6606028.8),t.size>=i&&t.data.length>1){var s=Math.floor(t.data.length/2),r=t.data.slice(0,s),n=t.data.slice(s);return[e({size:is(r),data:r,sessionId:t.sessionId,windowId:t.windowId}),e({size:is(n),data:n,sessionId:t.sessionId,windowId:t.windowId})].flatMap(e=>e)}return[t]})(this.M).forEach(e=>{this.Ai({$snapshot_bytes:e.size,$snapshot_data:e.data,$session_id:e.sessionId,$window_id:e.windowId,$lib:"web",$lib_version:v.LIB_VERSION})}),this.ai())}Oi(e){var t,i=2+((null==(t=this.M)?void 0:t.data.length)||0);!this.Wt&&(this.M.size+e.$snapshot_bytes+i>943718.4||this.M.sessionId!==this.kt)&&(this.M=this.Zt()),this.M.size+=e.$snapshot_bytes,this.M.data.push(e.$snapshot_data),this.Fi||this.Wt||(this.Fi=setTimeout(()=>{this.Zt()},2e3))}Ai(e){this._instance.capture("$snapshot",e,{_url:this._instance.requestRouter.endpointFor("api",this.si),_noTruncate:!0,_batchKey:"recordings",skip_client_rate_limiting:!0})}Ci(e){var t;this.Rt.triggerStatus(this.sessionId)===i7&&(null==(t=this._instance)||null==(t=t.persistence)||t.register({["url"===e?eI:e$]:this.kt}),this.Zt(),this.pi(e+"_trigger_matched"))}Ti(){this.Ut.urlBlocked||(this.Ut.urlBlocked=!0,clearInterval(this.vi),sh.info("recording paused due to URL blocker"),this.ii("recording paused",{reason:"url blocker"}))}Mi(){this.Ut.urlBlocked&&(this.Ut.urlBlocked=!1,this.ki(),this.Si(),this.ii("recording resumed",{reason:"left blocked url"}),sh.info("recording resumed"))}hi(){0!==this.qt.St.length&&L(this.Xt)&&(this.Xt=this._instance.on("eventCaptured",e=>{try{this.qt.St.includes(e.event)&&this.Ci("event")}catch(e){sh.error("Could not activate event trigger",e)}}))}overrideLinkedFlag(){this.Bt.linkedFlagSeen=!0,this.ki(),this.pi("linked_flag_overridden")}overrideSampling(){var e;null==(e=this._instance.persistence)||e.register({[ex]:!0}),this.ki(),this.pi("sampling_overridden")}overrideTrigger(e){this.Ci(e)}pi(e,t){this._instance.register_for_session({$session_recording_start_reason:e}),sh.info(e.replace("_"," "),t),w(["recording_initialized","session_id_changed"],e)||this.ii(e,t)}get sdkDebugProperties(){var{sessionStartTimestamp:e}=this.It.checkAndGetSessionAndWindowId(!0);return{$recording_status:this.status,$sdk_debug_replay_internal_buffer_length:this.M.data.length,$sdk_debug_replay_internal_buffer_size:this.M.size,$sdk_debug_current_session_duration:this.Mt,$sdk_debug_session_start:e}}}var sm=U("[SegmentIntegration]"),sy="posthog-js";function sb(e,t){var{organization:i,projectId:s,prefix:r,severityAllowList:n=["error"]}=void 0===t?{}:t;return t=>{if(!("*"===n||n.includes(t.level))||!e.__loaded)return t;t.tags||(t.tags={});var o,a,l,c,u,h=e.requestRouter.endpointFor("ui","/project/"+e.config.token+"/person/"+e.get_distinct_id());t.tags["PostHog Person URL"]=h,e.sessionRecordingStarted()&&(t.tags["PostHog Recording URL"]=e.get_session_replay_url({withTimestamp:!0}));var d=(null==(o=t.exception)?void 0:o.values)||[],p=d.map(e=>G({},e,{stacktrace:e.stacktrace?G({},e.stacktrace,{type:"raw",frames:(e.stacktrace.frames||[]).map(e=>G({},e,{platform:"web:javascript"}))}):void 0})),g={$exception_message:(null==(a=d[0])?void 0:a.value)||t.message,$exception_type:null==(l=d[0])?void 0:l.type,$exception_personURL:h,$exception_level:t.level,$exception_list:p,$sentry_event_id:t.event_id,$sentry_exception:t.exception,$sentry_exception_message:(null==(c=d[0])?void 0:c.value)||t.message,$sentry_exception_type:null==(u=d[0])?void 0:u.type,$sentry_tags:t.tags};return i&&s&&(g.$sentry_url=(r||"https://sentry.io/organizations/")+i+"/issues/?project="+s+"&query="+t.event_id),e.exceptions.sendExceptionEvent(g),t}}class sw{constructor(e,t,i,s,r){this.name=sy,this.setupOnce=function(n){n(sb(e,{organization:t,projectId:i,prefix:s,severityAllowList:r}))}}}var sE=null!=s&&s.location?td(s.location.hash,"__posthog")||td(location.hash,"state"):null,sk="_postHogToolbarParams",sS=U("[Toolbar]"),sx=function(e){return e[e.UNINITIALIZED=0]="UNINITIALIZED",e[e.LOADING=1]="LOADING",e[e.LOADED=2]="LOADED",e}(sx||{});class sI{constructor(e){this.instance=e}Di(e){_.ph_toolbar_state=e}Li(){var e;return null!==(e=_.ph_toolbar_state)&&void 0!==e?e:sx.UNINITIALIZED}maybeLoadToolbar(e,t,i){if(void 0===e&&(e=void 0),void 0===t&&(t=void 0),void 0===i&&(i=void 0),!s||!c)return!1;e=null!=e?e:s.location,i=null!=i?i:s.history;try{if(!t){try{s.localStorage.setItem("test","test"),s.localStorage.removeItem("test")}catch(e){return!1}t=null==s?void 0:s.localStorage}var r,n=sE||td(e.hash,"__posthog")||td(e.hash,"state"),o=n?ee(()=>JSON.parse(atob(decodeURIComponent(n))))||ee(()=>JSON.parse(decodeURIComponent(n))):null;return o&&"ph_authorize"===o.action?((r=o).source="url",r&&Object.keys(r).length>0&&(o.desiredHash?e.hash=o.desiredHash:i?i.replaceState(i.state,"",e.pathname+e.search):e.hash="")):((r=JSON.parse(t.getItem(sk)||"{}")).source="localstorage",delete r.userIntent),!(!r.token||this.instance.config.token!==r.token)&&(this.loadToolbar(r),!0)}catch(e){return!1}}Ni(e){var t=_.ph_load_toolbar||_.ph_load_editor;!L(t)&&R(t)?t(e,this.instance):sS.warn("No toolbar load function found")}loadToolbar(e){var t,i=!(null==c||!c.getElementById(eU));if(!s||i)return!1;var r="custom"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,n=G({token:this.instance.config.token},e,{apiURL:this.instance.requestRouter.endpointFor("ui")},r?{instrument:!1}:{});return(s.localStorage.setItem(sk,JSON.stringify(G({},n,{source:void 0}))),this.Li()===sx.LOADED)?this.Ni(n):this.Li()===sx.UNINITIALIZED&&(this.Di(sx.LOADING),null==(t=_.__PosthogExtensions__)||null==t.loadExternalDependency||t.loadExternalDependency(this.instance,"toolbar",e=>{if(e)return sS.error("[Toolbar] Failed to load",e),void this.Di(sx.UNINITIALIZED);this.Di(sx.LOADED),this.Ni(n)}),en(s,"turbolinks:load",()=>{this.Di(sx.UNINITIALIZED),this.loadToolbar(n)})),!0}ji(e){return this.loadToolbar(e)}maybeLoadEditor(e,t,i){return void 0===e&&(e=void 0),void 0===t&&(t=void 0),void 0===i&&(i=void 0),this.maybeLoadToolbar(e,t,i)}}var s$=U("[TracingHeaders]");class sF{constructor(e){this.zi=void 0,this.Ui=void 0,this.V=()=>{var e,t;T(this.zi)&&(null==(e=_.__PosthogExtensions__)||null==(e=e.tracingHeadersPatchFns)||e._patchXHR(this._instance.sessionManager)),T(this.Ui)&&(null==(t=_.__PosthogExtensions__)||null==(t=t.tracingHeadersPatchFns)||t._patchFetch(this._instance.sessionManager))},this._instance=e}G(e){var t,i;null!=(t=_.__PosthogExtensions__)&&t.tracingHeadersPatchFns&&e(),null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"tracing-headers",t=>{if(t)return s$.error("failed to load script",t);e()})}startIfEnabledOrStop(){var e,t;this._instance.config.__add_tracing_headers?this.G(this.V):(null==(e=this.zi)||e.call(this),null==(t=this.Ui)||t.call(this),this.zi=void 0,this.Ui=void 0)}}var sR=U("[Web Vitals]");class sP{constructor(e){var t;this.qi=!1,this.i=!1,this.M={url:void 0,metrics:[],firstMetricTimestamp:void 0},this.Bi=()=>{clearTimeout(this.Hi),0!==this.M.metrics.length&&(this._instance.capture("$web_vitals",this.M.metrics.reduce((e,t)=>G({},e,{["$web_vitals_"+t.name+"_event"]:G({},t),["$web_vitals_"+t.name+"_value"]:t.value}),{})),this.M={url:void 0,metrics:[],firstMetricTimestamp:void 0})},this.Wi=e=>{var t,i=null==(t=this._instance.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0);if(T(i))sR.error("Could not read session ID. Dropping metrics!");else{this.M=this.M||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var s=this.Gi();T(s)||(L(null==e?void 0:e.name)||L(null==e?void 0:e.value)?sR.error("Invalid metric received",e):this.Ji&&e.value>=this.Ji?sR.error("Ignoring metric with value >= "+this.Ji,e):(this.M.url!==s&&(this.Bi(),this.Hi=setTimeout(this.Bi,this.flushToCaptureTimeoutMs)),T(this.M.url)&&(this.M.url=s),this.M.firstMetricTimestamp=T(this.M.firstMetricTimestamp)?Date.now():this.M.firstMetricTimestamp,e.attribution&&e.attribution.interactionTargetElement&&(e.attribution.interactionTargetElement=void 0),this.M.metrics.push(G({},e,{$current_url:s,$session_id:i.sessionId,$window_id:i.windowId,timestamp:Date.now()})),this.M.metrics.length===this.allowedMetrics.length&&this.Bi()))}},this.V=()=>{var e,t,i,s,r=_.__PosthogExtensions__;T(r)||T(r.postHogWebVitalsCallbacks)||({onLCP:e,onCLS:t,onFCP:i,onINP:s}=r.postHogWebVitalsCallbacks),e&&t&&i&&s?(this.allowedMetrics.indexOf("LCP")>-1&&e(this.Wi.bind(this)),this.allowedMetrics.indexOf("CLS")>-1&&t(this.Wi.bind(this)),this.allowedMetrics.indexOf("FCP")>-1&&i(this.Wi.bind(this)),this.allowedMetrics.indexOf("INP")>-1&&s(this.Wi.bind(this)),this.i=!0):sR.error("web vitals callbacks not loaded - not starting")},this._instance=e,this.qi=!(null==(t=this._instance.persistence)||!t.props[ep]),this.startIfEnabled()}get allowedMetrics(){var e,t,i=P(this._instance.config.capture_performance)?null==(e=this._instance.config.capture_performance)?void 0:e.web_vitals_allowed_metrics:void 0;return T(i)?(null==(t=this._instance.persistence)?void 0:t.props[e_])||["CLS","FCP","INP","LCP"]:i}get flushToCaptureTimeoutMs(){return(P(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get Ji(){var e=P(this._instance.config.capture_performance)&&D(this._instance.config.capture_performance.__web_vitals_max_value)?this._instance.config.capture_performance.__web_vitals_max_value:9e5;return 0<e&&e<=6e4?9e5:e}get isEnabled(){var e=null==u?void 0:u.protocol;if("http:"!==e&&"https:"!==e)return sR.info("Web Vitals are disabled on non-http/https protocols"),!1;var t=P(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals:N(this._instance.config.capture_performance)?this._instance.config.capture_performance:void 0;return N(t)?t:this.qi}startIfEnabled(){this.isEnabled&&!this.i&&(sR.info("enabled, starting..."),this.G(this.V))}onRemoteConfig(e){var t=P(e.capturePerformance)&&!!e.capturePerformance.web_vitals,i=P(e.capturePerformance)?e.capturePerformance.web_vitals_allowed_metrics:void 0;this._instance.persistence&&(this._instance.persistence.register({[ep]:t}),this._instance.persistence.register({[e_]:i})),this.qi=t,this.startIfEnabled()}G(e){var t,i;null!=(t=_.__PosthogExtensions__)&&t.postHogWebVitalsCallbacks&&e(),null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"web-vitals",t=>{t?sR.error("failed to load script",t):e()})}Gi(){var e=s?s.location.href:void 0;return e||sR.error("Could not determine current URL"),e}}var sM=U("[Heatmaps]");function sT(e){return P(e)&&"clientX"in e&&"clientY"in e&&D(e.clientX)&&D(e.clientY)}class sC{constructor(e){var t;this.rageclicks=new to,this.qi=!1,this.i=!1,this.Vi=null,this.instance=e,this.qi=!(null==(t=this.instance.persistence)||!t.props[eu])}get flushIntervalMilliseconds(){var e=5e3;return P(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(e=this.instance.config.capture_heatmaps.flush_interval_milliseconds),e}get isEnabled(){return T(this.instance.config.capture_heatmaps)?T(this.instance.config.enable_heatmaps)?this.qi:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}startIfEnabled(){if(this.isEnabled)this.i||(sM.info("starting..."),this.Ki(),this.Vi=setInterval(this.Yi.bind(this),this.flushIntervalMilliseconds));else{var e,t;clearInterval(null!==(e=this.Vi)&&void 0!==e?e:void 0),null==(t=this.Xi)||t.stop(),this.getAndClearBuffer()}}onRemoteConfig(e){var t=!!e.heatmaps;this.instance.persistence&&this.instance.persistence.register({[eu]:t}),this.qi=t,this.startIfEnabled()}getAndClearBuffer(){var e=this.M;return this.M=void 0,e}Qi(e){this.Zi(e.originalEvent,"deadclick")}Ki(){s&&c&&(en(s,"beforeunload",this.Yi.bind(this)),en(c,"click",e=>this.Zi(e||(null==s?void 0:s.event)),{capture:!0}),en(c,"mousemove",e=>this.te(e||(null==s?void 0:s.event)),{capture:!0}),this.Xi=new tB(this.instance,tN,this.Qi.bind(this)),this.Xi.startIfEnabled(),this.i=!0)}ie(e,t){var i=this.instance.scrollManager.scrollY(),r=this.instance.scrollManager.scrollX(),n=this.instance.scrollManager.scrollElement(),o=function(e,t,i){for(var r=e;r&&eV(r)&&!eY(r,"body")&&r!==i;){if(w(t,null==s?void 0:s.getComputedStyle(r).position))return!0;r=e5(r)}return!1}(e2(e),["fixed","sticky"],n);return{x:e.clientX+(o?0:r),y:e.clientY+(o?0:i),target_fixed:o,type:t}}Zi(e,t){var i;if(void 0===t&&(t="click"),!eG(e.target)&&sT(e)){var s=this.ie(e,t);null!=(i=this.rageclicks)&&i.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this.ee(G({},s,{type:"rageclick"})),this.ee(s)}}te(e){!eG(e.target)&&sT(e)&&(clearTimeout(this.re),this.re=setTimeout(()=>{this.ee(this.ie(e,"mousemove"))},500))}ee(e){if(s){var t=s.location.href;this.M=this.M||{},this.M[t]||(this.M[t]=[]),this.M[t].push(e)}}Yi(){this.M&&!M(this.M)&&this.instance.capture("$$heatmap",{$heatmap_data:this.getAndClearBuffer()})}}class sO{constructor(e){this._instance=e}doPageView(e,t){var i,r=this.se(e,t);return this.ne={pathname:null!==(i=null==s?void 0:s.location.pathname)&&void 0!==i?i:"",pageViewId:t,timestamp:e},this._instance.scrollManager.resetContext(),r}doPageLeave(e){var t;return this.se(e,null==(t=this.ne)?void 0:t.pageViewId)}doEvent(){var e;return{$pageview_id:null==(e=this.ne)?void 0:e.pageViewId}}se(e,t){var i=this.ne;if(!i)return{$pageview_id:t};var s={$pageview_id:t,$prev_pageview_id:i.pageViewId},r=this._instance.scrollManager.getContext();if(r&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:n,lastScrollY:o,maxScrollY:a,maxContentHeight:l,lastContentY:c,maxContentY:u}=r;if(!(T(n)||T(o)||T(a)||T(l)||T(c)||T(u))){n=Math.ceil(n),o=Math.ceil(o),a=Math.ceil(a),l=Math.ceil(l),c=Math.ceil(c),u=Math.ceil(u);var h=n<=1?1:iv(o/n,0,1),d=n<=1?1:iv(a/n,0,1),p=l<=1?1:iv(c/l,0,1),g=l<=1?1:iv(u/l,0,1);s=Z(s,{$prev_pageview_last_scroll:o,$prev_pageview_last_scroll_percentage:h,$prev_pageview_max_scroll:a,$prev_pageview_max_scroll_percentage:d,$prev_pageview_last_content:c,$prev_pageview_last_content_percentage:p,$prev_pageview_max_content:u,$prev_pageview_max_content_percentage:g})}}return i.pathname&&(s.$prev_pageview_pathname=i.pathname),i.timestamp&&(s.$prev_pageview_duration=(e.getTime()-i.timestamp.getTime())/1e3),s}}var sA=function(e){var t,i,s,r,n="";for(t=i=0,s=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,r=0;r<s;r++){var o=e.charCodeAt(r),a=null;o<128?i++:a=o>127&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),A(a)||(i>t&&(n+=e.substring(t,i)),n+=a,t=i=r+1)}return i>t&&(n+=e.substring(t,e.length)),n},sL=!!d||!!h,sD="text/plain",sN=(e,t)=>{var[i,s]=e.split("?"),r=G({},t);null==s||s.split("&").forEach(e=>{var[t]=e.split("=");delete r[t]});var n=tc(r);return i+"?"+(n=n?(s?s+"&":"")+n:s)},sq=(e,t)=>JSON.stringify(e,(e,t)=>"bigint"==typeof t?t.toString():t,t),sB=e=>{var{data:t,compression:i}=e;if(t){if(i===y.GZipJS){var s=new Blob([iQ(i0(sq(t)),{mtime:0})],{type:sD});return{contentType:sD,body:s,estimatedSize:s.size}}if(i===y.Base64){var r=(e=>"data="+encodeURIComponent("string"==typeof e?e:sq(e)))(function(e){var t,i,s,r,n,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a=0,l=0,c="",u=[];if(!e)return e;e=sA(e);do t=(n=e.charCodeAt(a++)<<16|e.charCodeAt(a++)<<8|e.charCodeAt(a++))>>18&63,i=n>>12&63,s=n>>6&63,r=63&n,u[l++]=o.charAt(t)+o.charAt(i)+o.charAt(s)+o.charAt(r);while(a<e.length);switch(c=u.join(""),e.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c}(sq(t)));return{contentType:"application/x-www-form-urlencoded",body:r,estimatedSize:new Blob([r]).size}}var n=sq(t);return{contentType:"application/json",body:n,estimatedSize:new Blob([n]).size}}},sH=[];h&&sH.push({transport:"fetch",method:e=>{var t,i,{contentType:s,body:r,estimatedSize:n}=null!==(t=sB(e))&&void 0!==t?t:{},o=new Headers;J(e.headers,function(e,t){o.append(t,e)}),s&&o.append("Content-Type",s);var a=e.url,l=null;if(p){var c=new p;l={signal:c.signal,timeout:setTimeout(()=>c.abort(),e.timeout)}}h(a,G({method:(null==e?void 0:e.method)||"GET",headers:o,keepalive:"POST"===e.method&&52428.8>(n||0),body:r,signal:null==(i=l)?void 0:i.signal},e.fetchOptions)).then(t=>t.text().then(i=>{var s={statusCode:t.status,text:i};if(200===t.status)try{s.json=JSON.parse(i)}catch(e){j.error(e)}null==e.callback||e.callback(s)})).catch(t=>{j.error(t),null==e.callback||e.callback({statusCode:0,text:t})}).finally(()=>l?clearTimeout(l.timeout):null)}}),d&&sH.push({transport:"XHR",method:e=>{var t,i=new d;i.open(e.method||"GET",e.url,!0);var{contentType:s,body:r}=null!==(t=sB(e))&&void 0!==t?t:{};J(e.headers,function(e,t){i.setRequestHeader(t,e)}),s&&i.setRequestHeader("Content-Type",s),e.timeout&&(i.timeout=e.timeout),i.withCredentials=!0,i.onreadystatechange=()=>{if(4===i.readyState){var t={statusCode:i.status,text:i.responseText};if(200===i.status)try{t.json=JSON.parse(i.responseText)}catch(e){}null==e.callback||e.callback(t)}},i.send(r)}}),null!=l&&l.sendBeacon&&sH.push({transport:"sendBeacon",method:e=>{var t=sN(e.url,{beacon:"1"});try{var i,{contentType:s,body:r}=null!==(i=sB(e))&&void 0!==i?i:{},n="string"==typeof r?new Blob([r],{type:s}):r;l.sendBeacon(t,n)}catch(e){}}});var sj=function(e,t){if(!function(e){try{new RegExp(e)}catch(e){return!1}return!0}(t))return!1;try{return new RegExp(t).test(e)}catch(e){return!1}};function sU(e,t,i){return sq({distinct_id:e,userPropertiesToSet:t,userPropertiesToSetOnce:i})}var sz={exact:(e,t)=>t.some(t=>e.some(e=>t===e)),is_not:(e,t)=>t.every(t=>e.every(e=>t!==e)),regex:(e,t)=>t.some(t=>e.some(e=>sj(t,e))),not_regex:(e,t)=>t.every(t=>e.every(e=>!sj(t,e))),icontains:(e,t)=>t.map(sW).some(t=>e.map(sW).some(e=>t.includes(e))),not_icontains:(e,t)=>t.map(sW).every(t=>e.map(sW).every(e=>!t.includes(e)))},sW=e=>e.toLowerCase(),sG=U("[Error tracking]");class sV{constructor(e){var t,i;this.oe=[],this._instance=e,this.oe=null!==(t=null==(i=this._instance.persistence)?void 0:i.get_property(ed))&&void 0!==t?t:[]}onRemoteConfig(e){var t,i,s=null!==(t=null==(i=e.errorTracking)?void 0:i.suppressionRules)&&void 0!==t?t:[];this.oe=s,this._instance.persistence&&this._instance.persistence.register({[ed]:this.oe})}sendExceptionEvent(e){this.ae(e)?sG.info("Skipping exception capture because a suppression rule matched"):this._instance.capture("$exception",e,{_noTruncate:!0,_batchKey:"exceptionEvent"})}ae(e){var t=e.$exception_list;if(!t||!F(t)||0===t.length)return!1;var i=t.reduce((e,t)=>{var{type:i,value:s}=t;return C(i)&&i.length>0&&e.$exception_types.push(i),C(s)&&s.length>0&&e.$exception_messages.push(s),e},{$exception_types:[],$exception_messages:[]});return this.oe.some(e=>{var t=e.values.map(e=>{var t=sz[e.operator],s=F(e.value)?e.value:[e.value],r=i[e.key];return s.length>0&&t(s,r)});return"OR"===e.type?t.some(Boolean):t.every(Boolean)})}}var sY="Mobile",sX="Android",sJ="Tablet",sZ=sX+" "+sJ,sK="iPad",sQ="Apple",s0=sQ+" Watch",s1="Safari",s2="BlackBerry",s3="Samsung",s5=s3+"Browser",s6=s3+" Internet",s4="Chrome",s8=s4+" OS",s7=s4+" iOS",s9="Internet Explorer",re=s9+" "+sY,rt="Opera",ri=rt+" Mini",rs="Edge",rr="Microsoft "+rs,rn="Firefox",ro=rn+" iOS",ra="Nintendo",rl="PlayStation",rc="Xbox",ru=sX+" "+sY,rh=sY+" "+s1,rd="Windows",rp=rd+" Phone",rg="Nokia",r_="Ouya",rv="Generic",rf=rv+" "+sY.toLowerCase(),rm=rv+" "+sJ.toLowerCase(),ry="Konqueror",rb="(\\d+(\\.\\d+)?)",rw=RegExp("Version/"+rb),rE=RegExp(rc,"i"),rk=RegExp(rl+" \\w+","i"),rS=RegExp(ra+" \\w+","i"),rx=RegExp(s2+"|PlayBook|BB10","i"),rI={"NT3.51":"NT 3.11","NT4.0":"NT 4.0","5.0":"2000",5.1:"XP",5.2:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1",6.4:"10","10.0":"10"},r$=(e,t)=>t&&w(t,sQ)||function(e){return w(e,s1)&&!w(e,s4)&&!w(e,sX)}(e),rF=function(e,t){return t=t||"",w(e," OPR/")&&w(e,"Mini")?ri:w(e," OPR/")?rt:rx.test(e)?s2:w(e,"IE"+sY)||w(e,"WPDesktop")?re:w(e,s5)?s6:w(e,rs)||w(e,"Edg/")?rr:w(e,"FBIOS")?"Facebook "+sY:w(e,"UCWEB")||w(e,"UCBrowser")?"UC Browser":w(e,"CriOS")?s7:w(e,"CrMo")||w(e,s4)?s4:w(e,sX)&&w(e,s1)?ru:w(e,"FxiOS")?ro:w(e.toLowerCase(),ry.toLowerCase())?ry:r$(e,t)?w(e,sY)?rh:s1:w(e,rn)?rn:w(e,"MSIE")||w(e,"Trident/")?s9:w(e,"Gecko")?rn:""},rR={[re]:[RegExp("rv:"+rb)],[rr]:[RegExp(rs+"?\\/"+rb)],[s4]:[RegExp("("+s4+"|CrMo)\\/"+rb)],[s7]:[RegExp("CriOS\\/"+rb)],"UC Browser":[RegExp("(UCBrowser|UCWEB)\\/"+rb)],[s1]:[rw],[rh]:[rw],[rt]:[RegExp("(Opera|OPR)\\/"+rb)],[rn]:[RegExp(rn+"\\/"+rb)],[ro]:[RegExp("FxiOS\\/"+rb)],[ry]:[RegExp("Konqueror[:/]?"+rb,"i")],[s2]:[RegExp(s2+" "+rb),rw],[ru]:[RegExp("android\\s"+rb,"i")],[s6]:[RegExp(s5+"\\/"+rb)],[s9]:[RegExp("(rv:|MSIE )"+rb)],Mozilla:[RegExp("rv:"+rb)]},rP=function(e,t){var i=rR[rF(e,t)];if(T(i))return null;for(var s=0;s<i.length;s++){var r=i[s],n=e.match(r);if(n)return parseFloat(n[n.length-2])}return null},rM=[[RegExp(rc+"; "+rc+" (.*?)[);]","i"),e=>[rc,e&&e[1]||""]],[RegExp(ra,"i"),[ra,""]],[RegExp(rl,"i"),[rl,""]],[rx,[s2,""]],[RegExp(rd,"i"),(e,t)=>{if(/Phone/.test(t)||/WPDesktop/.test(t))return[rp,""];if(new RegExp(sY).test(t)&&!/IEMobile\b/.test(t))return[rd+" "+sY,""];var i=/Windows NT ([0-9.]+)/i.exec(t);if(i&&i[1]){var s=rI[i[1]]||"";return/arm/i.test(t)&&(s="RT"),[rd,s]}return[rd,""]}],[/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/,e=>e&&e[3]?["iOS",[e[3],e[4],e[5]||"0"].join(".")]:["iOS",""]],[/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i,e=>{var t="";return e&&e.length>=3&&(t=T(e[2])?e[3]:e[2]),["watchOS",t]}],[RegExp("("+sX+" (\\d+)\\.(\\d+)\\.?(\\d+)?|"+sX+")","i"),e=>e&&e[2]?[sX,[e[2],e[3],e[4]||"0"].join(".")]:[sX,""]],[/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i,e=>{var t=["Mac OS X",""];if(e&&e[1]){var i=[e[1],e[2],e[3]||"0"];t[1]=i.join(".")}return t}],[/Mac/i,["Mac OS X",""]],[/CrOS/,[s8,""]],[/Linux|debian/i,["Linux",""]]],rT=function(e){return rS.test(e)?ra:rk.test(e)?rl:rE.test(e)?rc:RegExp(r_,"i").test(e)?r_:RegExp("("+rp+"|WPDesktop)","i").test(e)?rp:/iPad/.test(e)?sK:/iPod/.test(e)?"iPod Touch":/iPhone/.test(e)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(e)?s0:rx.test(e)?s2:/(kobo)\s(ereader|touch)/i.test(e)?"Kobo":RegExp(rg,"i").test(e)?rg:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(e)||/(kf[a-z]+)( bui|\)).+silk\//i.test(e)?"Kindle Fire":/(Android|ZTE)/i.test(e)?!new RegExp(sY).test(e)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(e)?/pixel[\daxl ]{1,6}/i.test(e)&&!/pixel c/i.test(e)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(e)||/lmy47v/i.test(e)&&!/QTAQZ3/i.test(e)?sX:sZ:sX:RegExp("(pda|"+sY+")","i").test(e)?rf:RegExp(sJ,"i").test(e)&&!RegExp(sJ+" pc","i").test(e)?rm:""},rC="https?://(.*)",rO=["gclid","gclsrc","dclid","gbraid","wbraid","fbclid","msclkid","twclid","li_fat_id","igshid","ttclid","rdt_cid","epik","qclid","sccid","irclid","_kx"],rA=K(["utm_source","utm_medium","utm_campaign","utm_content","utm_term","gad_source","mc_cid"],rO),rL="<masked>";function rD(e,t,i){if(!c)return{};var s=t?K([],rO,i||[]):[];return rN(th(c.URL,s,rL),e)}function rN(e,t){var i=rA.concat(t||[]),s={};return J(i,function(t){var i=tu(e,t);s[t]=i||null}),s}function rq(e){var t=e?0===e.search(rC+"google.([^/?]*)")?"google":0===e.search(rC+"bing.com")?"bing":0===e.search(rC+"yahoo.com")?"yahoo":0===e.search(rC+"duckduckgo.com")?"duckduckgo":null:null,i={};if(!A(t)){i.$search_engine=t;var s=c?tu(c.referrer,"yahoo"!=t?"q":"p"):"";s.length&&(i.ph_keyword=s)}return i}function rB(){return navigator.language||navigator.userLanguage}function rH(){return(null==c?void 0:c.referrer)||"$direct"}function rj(e,t){var i=e?K([],rO,t||[]):[],s=null==u?void 0:u.href.substring(0,1e3);return{r:rH().substring(0,1e3),u:s?th(s,i,rL):void 0}}function rU(e){var t,{r:i,u:s}=e,r={$referrer:i,$referring_domain:null==i?void 0:"$direct"==i?"$direct":null==(t=tl(i))?void 0:t.host};if(s){r.$current_url=s;var n=tl(s);r.$host=null==n?void 0:n.host,r.$pathname=null==n?void 0:n.pathname,Z(r,rN(s))}return i&&Z(r,rq(i)),r}function rz(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){return}}var rW=U("[FeatureFlags]"),rG="$active_feature_flags",rV="$override_feature_flags",rY="$feature_flag_payloads",rX="$override_feature_flag_payloads",rJ="$feature_flag_request_id",rZ=e=>{var t={};for(var[i,s]of Q(e||{}))s&&(t[i]=s);return t},rK=e=>{var t=e.flags;return t?(e.featureFlags=Object.fromEntries(Object.keys(t).map(e=>{var i;return[e,null!==(i=t[e].variant)&&void 0!==i?i:t[e].enabled]})),e.featureFlagPayloads=Object.fromEntries(Object.keys(t).filter(e=>t[e].enabled).filter(e=>{var i;return null==(i=t[e].metadata)?void 0:i.payload}).map(e=>{var i;return[e,null==(i=t[e].metadata)?void 0:i.payload]}))):rW.warn("Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version"),e},rQ=function(e){return e.FeatureFlags="feature_flags",e.Recordings="recordings",e}({});class r0{constructor(e){this.le=!1,this.ue=!1,this.he=!1,this.de=!1,this.ve=!1,this.ce=!1,this.fe=!1,this._instance=e,this.featureFlagEventHandlers=[]}decide(){if(this._instance.config.__preview_remote_config)this.ce=!0;else{var e=!this.pe&&(this._instance.config.advanced_disable_feature_flags||this._instance.config.advanced_disable_feature_flags_on_first_load);this._e({disableFlags:e})}}get hasLoadedFlags(){return this.ue}getFlags(){return Object.keys(this.getFlagVariants())}getFlagsWithDetails(){var e=this._instance.get_property(eP),t=this._instance.get_property(rV),i=this._instance.get_property(rX);if(!i&&!t)return e||{};var s=Z({},e||{});for(var r of[...new Set([...Object.keys(i||{}),...Object.keys(t||{})])]){var n,o,a=s[r],l=null==t?void 0:t[r],c=T(l)?null!==(n=null==a?void 0:a.enabled)&&void 0!==n&&n:!!l,u=T(l)?a.variant:"string"==typeof l?l:void 0,h=null==i?void 0:i[r],d=G({},a,{enabled:c,variant:c?null!=u?u:null==a?void 0:a.variant:void 0});c!==(null==a?void 0:a.enabled)&&(d.original_enabled=null==a?void 0:a.enabled),u!==(null==a?void 0:a.variant)&&(d.original_variant=null==a?void 0:a.variant),h&&(d.metadata=G({},null==a?void 0:a.metadata,{payload:h,original_payload:null==a||null==(o=a.metadata)?void 0:o.payload})),s[r]=d}return this.le||(rW.warn(" Overriding feature flag details!",{flagDetails:e,overriddenPayloads:i,finalDetails:s}),this.le=!0),s}getFlagVariants(){var e=this._instance.get_property(eF),t=this._instance.get_property(rV);if(!t)return e||{};for(var i=Z({},e),s=Object.keys(t),r=0;r<s.length;r++)i[s[r]]=t[s[r]];return this.le||(rW.warn(" Overriding feature flags!",{enabledFlags:e,overriddenFlags:t,finalFlags:i}),this.le=!0),i}getFlagPayloads(){var e=this._instance.get_property(rY),t=this._instance.get_property(rX);if(!t)return e||{};for(var i=Z({},e||{}),s=Object.keys(t),r=0;r<s.length;r++)i[s[r]]=t[s[r]];return this.le||(rW.warn(" Overriding feature flag payloads!",{flagPayloads:e,overriddenPayloads:t,finalPayloads:i}),this.le=!0),i}reloadFeatureFlags(){this.de||this._instance.config.advanced_disable_feature_flags||this.pe||(this.pe=setTimeout(()=>{this._e()},5))}ge(){clearTimeout(this.pe),this.pe=void 0}ensureFlagsLoaded(){this.ue||this.he||this.pe||this.reloadFeatureFlags()}setAnonymousDistinctId(e){this.$anon_distinct_id=e}setReloadingPaused(e){this.de=e}_e(e){var t;if(this.ge(),!this._instance.config.advanced_disable_decide)if(this.he)this.ve=!0;else{var i={token:this._instance.config.token,distinct_id:this._instance.get_distinct_id(),groups:this._instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:G({},(null==(t=this._instance.persistence)?void 0:t.get_initial_props())||{},this._instance.get_property(eM)||{}),group_properties:this._instance.get_property(eT)};(null!=e&&e.disableFlags||this._instance.config.advanced_disable_feature_flags)&&(i.disable_flags=!0);var s=this._instance.config.__preview_flags_v2&&this._instance.config.__preview_remote_config,r=this._instance.config.advanced_only_evaluate_survey_feature_flags?"&only_evaluate_survey_feature_flags=true":"",n=this._instance.requestRouter.endpointFor("api",(s?"/flags/?v=2":"/decide/?v=4")+r);s&&(i.timezone=rz()),this.he=!0,this._instance.me({method:"POST",url:n,data:i,compression:this._instance.config.disable_compression?void 0:y.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:e=>{var t,s,r,n=!0;200===e.statusCode&&(this.ve||(this.$anon_distinct_id=void 0),n=!1),this.he=!1,this.ce||(this.ce=!0,this._instance.be(null!==(r=e.json)&&void 0!==r?r:{})),(!i.disable_flags||this.ve)&&((this.fe=!n,e.json&&null!=(s=e.json.quotaLimited)&&s.includes(rQ.FeatureFlags))?rW.warn("You have hit your feature flags quota limit, and will not be able to load feature flags until the quota is reset.  Please visit https://posthog.com/docs/billing/limits-alerts to learn more."):(i.disable_flags||this.receivedFeatureFlags(null!==(t=e.json)&&void 0!==t?t:{},n),this.ve&&(this.ve=!1,this._e())))}})}}getFeatureFlag(e,t){if(void 0===t&&(t={}),this.ue||this.getFlags()&&this.getFlags().length>0){var i=this.getFlagVariants()[e],s=""+i,r=this._instance.get_property(rJ)||void 0,n=this._instance.get_property(eA)||{};if((t.send_event||!("send_event"in t))&&(!(e in n)||!n[e].includes(s))){F(n[e])?n[e].push(s):n[e]=[s],null==(l=this._instance.persistence)||l.register({[eA]:n});var o=this.getFeatureFlagDetails(e),a={$feature_flag:e,$feature_flag_response:i,$feature_flag_payload:this.getFeatureFlagPayload(e)||null,$feature_flag_request_id:r,$feature_flag_bootstrapped_response:(null==(c=this._instance.config.bootstrap)||null==(c=c.featureFlags)?void 0:c[e])||null,$feature_flag_bootstrapped_payload:(null==(u=this._instance.config.bootstrap)||null==(u=u.featureFlagPayloads)?void 0:u[e])||null,$used_bootstrap_value:!this.fe};T(null==o||null==(h=o.metadata)?void 0:h.version)||(a.$feature_flag_version=o.metadata.version);var l,c,u,h,d,p,g,_,v,f,m=null!==(d=null==o||null==(p=o.reason)?void 0:p.description)&&void 0!==d?d:null==o||null==(g=o.reason)?void 0:g.code;m&&(a.$feature_flag_reason=m),null!=o&&null!=(_=o.metadata)&&_.id&&(a.$feature_flag_id=o.metadata.id),T(null==o?void 0:o.original_variant)&&T(null==o?void 0:o.original_enabled)||(a.$feature_flag_original_response=T(o.original_variant)?o.original_enabled:o.original_variant),null!=o&&null!=(v=o.metadata)&&v.original_payload&&(a.$feature_flag_original_payload=null==o||null==(f=o.metadata)?void 0:f.original_payload),this._instance.capture("$feature_flag_called",a)}return i}rW.warn('getFeatureFlag for key "'+e+"\" failed. Feature flags didn't load in time.")}getFeatureFlagDetails(e){return this.getFlagsWithDetails()[e]}getFeatureFlagPayload(e){return this.getFlagPayloads()[e]}getRemoteConfigPayload(e,t){var i=this._instance.config.token;this._instance.me({method:"POST",url:this._instance.requestRouter.endpointFor("api","/decide/?v=4"),data:{distinct_id:this._instance.get_distinct_id(),token:i},compression:this._instance.config.disable_compression?void 0:y.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:i=>{var s,r=null==(s=i.json)?void 0:s.featureFlagPayloads;t((null==r?void 0:r[e])||void 0)}})}isFeatureEnabled(e,t){if(void 0===t&&(t={}),this.ue||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(e,t);rW.warn('isFeatureEnabled for key "'+e+"\" failed. Feature flags didn't load in time.")}addFeatureFlagsHandler(e){this.featureFlagEventHandlers.push(e)}removeFeatureFlagsHandler(e){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter(t=>t!==e)}receivedFeatureFlags(e,t){if(this._instance.persistence){this.ue=!0;var i=this.getFlagVariants(),s=this.getFlagPayloads(),r=this.getFlagsWithDetails();!function(e,t,i,s,r){void 0===i&&(i={}),void 0===s&&(s={}),void 0===r&&(r={});var n=rK(e),o=n.flags,a=n.featureFlags,l=n.featureFlagPayloads;if(a){var c=e.requestId;if(F(a)){rW.warn("v1 of the feature flags endpoint is deprecated. Please use the latest version.");var u={};if(a)for(var h=0;h<a.length;h++)u[a[h]]=!0;t&&t.register({[rG]:a,[eF]:u})}else{var d=a,p=l,g=o;e.errorsWhileComputingFlags&&(d=G({},i,d),p=G({},s,p),g=G({},r,g)),t&&t.register(G({[rG]:Object.keys(rZ(d)),[eF]:d||{},[rY]:p||{},[eP]:g||{}},c?{[rJ]:c}:{}))}}}(e,this._instance.persistence,i,s,r),this.ye(t)}}override(e,t){void 0===t&&(t=!1),rW.warn("override is deprecated. Please use overrideFeatureFlags instead."),this.overrideFeatureFlags({flags:e,suppressWarning:t})}overrideFeatureFlags(e){if(!this._instance.__loaded||!this._instance.persistence)return rW.uninitializedWarning("posthog.featureFlags.overrideFeatureFlags");if(!1===e)return this._instance.persistence.unregister(rV),this._instance.persistence.unregister(rX),void this.ye();if(e&&"object"==typeof e&&("flags"in e||"payloads"in e)){var t;if(this.le=!!(null!==(t=e.suppressWarning)&&void 0!==t&&t),"flags"in e){if(!1===e.flags)this._instance.persistence.unregister(rV);else if(e.flags)if(F(e.flags)){for(var i={},s=0;s<e.flags.length;s++)i[e.flags[s]]=!0;this._instance.persistence.register({[rV]:i})}else this._instance.persistence.register({[rV]:e.flags})}return"payloads"in e&&(!1===e.payloads?this._instance.persistence.unregister(rX):e.payloads&&this._instance.persistence.register({[rX]:e.payloads})),void this.ye()}this.ye()}onFeatureFlags(e){if(this.addFeatureFlagsHandler(e),this.ue){var{flags:t,flagVariants:i}=this.we();e(t,i)}return()=>this.removeFeatureFlagsHandler(e)}updateEarlyAccessFeatureEnrollment(e,t){var i,s=(this._instance.get_property(eR)||[]).find(t=>t.flagKey===e),r={["$feature_enrollment/"+e]:t},n={$feature_flag:e,$feature_enrollment:t,$set:r};s&&(n.$early_access_feature_name=s.name),this._instance.capture("$feature_enrollment_update",n),this.setPersonPropertiesForFlags(r,!1);var o=G({},this.getFlagVariants(),{[e]:t});null==(i=this._instance.persistence)||i.register({[rG]:Object.keys(rZ(o)),[eF]:o}),this.ye()}getEarlyAccessFeatures(e,t,i){void 0===t&&(t=!1);var s=this._instance.get_property(eR),r=i?"&"+i.map(e=>"stage="+e).join("&"):"";if(s&&!t)return e(s);this._instance.me({url:this._instance.requestRouter.endpointFor("api","/api/early_access_features/?token="+this._instance.config.token+r),method:"GET",callback:t=>{var i;if(t.json){var s=t.json.earlyAccessFeatures;return null==(i=this._instance.persistence)||i.register({[eR]:s}),e(s)}}})}we(){var e=this.getFlags(),t=this.getFlagVariants();return{flags:e.filter(e=>t[e]),flagVariants:Object.keys(t).filter(e=>t[e]).reduce((e,i)=>(e[i]=t[i],e),{})}}ye(e){var{flags:t,flagVariants:i}=this.we();this.featureFlagEventHandlers.forEach(s=>s(t,i,{errorsLoading:e}))}setPersonPropertiesForFlags(e,t){void 0===t&&(t=!0);var i=this._instance.get_property(eM)||{};this._instance.register({[eM]:G({},i,e)}),t&&this._instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this._instance.unregister(eM)}setGroupPropertiesForFlags(e,t){void 0===t&&(t=!0);var i=this._instance.get_property(eT)||{};0!==Object.keys(i).length&&Object.keys(i).forEach(t=>{i[t]=G({},i[t],e[t]),delete e[t]}),this._instance.register({[eT]:G({},i,e)}),t&&this._instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(e){if(e){var t=this._instance.get_property(eT)||{};this._instance.register({[eT]:G({},t,{[e]:{}})})}else this._instance.unregister(eT)}}var r1=["cookie","localstorage","localstorage+cookie","sessionstorage","memory"];class r2{constructor(e){this.S=e,this.props={},this.Se=!1,this.$e=(e=>{var t="";return e.token&&(t=e.token.replace(/\+/g,"PL").replace(/\//g,"SL").replace(/=/g,"EQ")),e.persistence_name?"ph_"+e.persistence_name:"ph_"+t+"_posthog"})(e),this.q=this.ke(e),this.load(),e.debug&&j.info("Persistence loaded",e.persistence,G({},this.props)),this.update_config(e,e),this.save()}ke(e){-1===r1.indexOf(e.persistence.toLowerCase())&&(j.critical("Unknown persistence type "+e.persistence+"; falling back to localStorage+cookie"),e.persistence="localStorage+cookie");var t=e.persistence.toLowerCase();return"localstorage"===t&&tF.O()?tF:"localstorage+cookie"===t&&tP.O()?tP:"sessionstorage"===t&&tO.O()?tO:"memory"===t?tT:"cookie"===t?tI:tP.O()?tP:tI}properties(){var e={};return J(this.props,function(t,i){if(i===eF&&P(t))for(var s,r=Object.keys(t),n=0;n<r.length;n++)e["$feature/"+r[n]]=t[r[n]];else s=!1,(A(eW)?s:a&&eW.indexOf===a?-1!=eW.indexOf(i):(J(eW,function(e){if(s||(s=e===i))return Y}),s))||(e[i]=t)}),e}load(){if(!this.xe){var e=this.q.D(this.$e);e&&(this.props=Z({},e))}}save(){this.xe||this.q.L(this.$e,this.props,this.Ee,this.Ie,this.Pe,this.S.debug)}remove(){this.q.N(this.$e,!1),this.q.N(this.$e,!0)}clear(){this.remove(),this.props={}}register_once(e,t,i){if(P(e)){T(t)&&(t="None"),this.Ee=T(i)?this.Re:i;var s=!1;if(J(e,(e,i)=>{this.props.hasOwnProperty(i)&&this.props[i]!==t||(this.props[i]=e,s=!0)}),s)return this.save(),!0}return!1}register(e,t){if(P(e)){this.Ee=T(t)?this.Re:t;var i=!1;if(J(e,(t,s)=>{e.hasOwnProperty(s)&&this.props[s]!==t&&(this.props[s]=t,i=!0)}),i)return this.save(),!0}return!1}unregister(e){e in this.props&&(delete this.props[e],this.save())}update_campaign_params(){if(!this.Se){var e=rD(this.S.custom_campaign_params,this.S.mask_personal_data_properties,this.S.custom_personal_data_properties);M(ei(e))||this.register(e),this.Se=!0}}update_search_keyword(){var e;this.register((e=null==c?void 0:c.referrer)?rq(e):{})}update_referrer_info(){var e;this.register_once({$referrer:rH(),$referring_domain:null!=c&&c.referrer&&(null==(e=tl(c.referrer))?void 0:e.host)||"$direct"},void 0)}set_initial_person_info(){this.props[eq]||this.props[eB]||this.register_once({[eH]:rj(this.S.mask_personal_data_properties,this.S.custom_personal_data_properties)},void 0)}get_initial_props(){var e={};J([eB,eq],t=>{var i=this.props[t];i&&J(i,function(t,i){e["$initial_"+k(i)]=t})});var t,i,s=this.props[eH];return s&&Z(e,(t=rU(s),i={},J(t,function(e,t){i["$initial_"+k(t)]=e}),i)),e}safe_merge(e){return J(this.props,function(t,i){i in e||(e[i]=t)}),e}update_config(e,t){if(this.Re=this.Ee=e.cookie_expiration,this.set_disabled(e.disable_persistence),this.set_cross_subdomain(e.cross_subdomain_cookie),this.set_secure(e.secure_cookie),e.persistence!==t.persistence){var i=this.ke(e),s=this.props;this.clear(),this.q=i,this.props=s,this.save()}}set_disabled(e){this.xe=e,this.xe?this.remove():this.save()}set_cross_subdomain(e){e!==this.Ie&&(this.Ie=e,this.remove(),this.save())}set_secure(e){e!==this.Pe&&(this.Pe=e,this.remove(),this.save())}set_event_timer(e,t){var i=this.props[el]||{};i[e]=t,this.props[el]=i,this.save()}remove_event_timer(e){var t=(this.props[el]||{})[e];return T(t)||(delete this.props[el][e],this.save()),t}get_property(e){return this.props[e]}set_property(e,t){this.props[e]=t,this.save()}}class r3{constructor(){this.Te={},this.Te={}}on(e,t){return this.Te[e]||(this.Te[e]=[]),this.Te[e].push(t),()=>{this.Te[e]=this.Te[e].filter(e=>e!==t)}}emit(e,t){for(var i of this.Te[e]||[])i(t);for(var s of this.Te["*"]||[])s(e,t)}}class r5{constructor(e){this.Me=new r3,this.Ce=(e,t)=>this.Oe(e,t)&&this.Fe(e,t)&&this.Ae(e,t),this.Oe=(e,t)=>null==t||!t.event||(null==e?void 0:e.event)===(null==t?void 0:t.event),this._instance=e,this.De=new Set,this.Le=new Set}init(){var e,t;T(null==(e=this._instance)?void 0:e.Ne)||null==(t=this._instance)||t.Ne((e,t)=>{this.on(e,t)})}register(e){var t,i;if(!T(null==(t=this._instance)?void 0:t.Ne)&&(e.forEach(e=>{var t,i;null==(t=this.Le)||t.add(e),null==(i=e.steps)||i.forEach(e=>{var t;null==(t=this.De)||t.add((null==e?void 0:e.event)||"")})}),null!=(i=this._instance)&&i.autocapture)){var s,r=new Set;e.forEach(e=>{var t;null==(t=e.steps)||t.forEach(e=>{null!=e&&e.selector&&r.add(null==e?void 0:e.selector)})}),null==(s=this._instance)||s.autocapture.setElementSelectors(r)}}on(e,t){var i;null!=t&&0!=e.length&&(this.De.has(e)||this.De.has(null==t?void 0:t.event))&&this.Le&&(null==(i=this.Le)?void 0:i.size)>0&&this.Le.forEach(e=>{this.je(t,e)&&this.Me.emit("actionCaptured",e.name)})}ze(e){this.onAction("actionCaptured",t=>e(t))}je(e,t){if(null==(null==t?void 0:t.steps))return!1;for(var i of t.steps)if(this.Ce(e,i))return!0;return!1}onAction(e,t){return this.Me.on(e,t)}Fe(e,t){if(null!=t&&t.url){var i,s=null==e||null==(i=e.properties)?void 0:i.$current_url;if(!s||"string"!=typeof s||!r5.Ue(s,null==t?void 0:t.url,(null==t?void 0:t.url_matching)||"contains"))return!1}return!0}static Ue(e,t,i){switch(i){case"regex":return!!s&&sj(e,t);case"exact":return t===e;case"contains":return sj(e,r5.qe(t).replace(/_/g,".").replace(/%/g,".*"));default:return!1}}static qe(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}Ae(e,t){if((null!=t&&t.href||null!=t&&t.tag_name||null!=t&&t.text)&&!this.Be(e).some(e=>!(null!=t&&t.href&&!r5.Ue(e.href||"",null==t?void 0:t.href,(null==t?void 0:t.href_matching)||"exact"))&&(null==t||!t.tag_name||e.tag_name===(null==t?void 0:t.tag_name))&&!(null!=t&&t.text&&!r5.Ue(e.text||"",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||"exact")&&!r5.Ue(e.$el_text||"",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||"exact"))))return!1;if(null!=t&&t.selector){var i,s=null==e||null==(i=e.properties)?void 0:i.$element_selectors;if(!s||!s.includes(null==t?void 0:t.selector))return!1}return!0}Be(e){return null==(null==e?void 0:e.properties.$elements)?[]:null==e?void 0:e.properties.$elements}}var r6=U("[Surveys]"),r4="seenSurvey_",r8=(e,t)=>{var i="$survey_"+t+"/"+e.id;return e.current_iteration&&e.current_iteration>0&&(i="$survey_"+t+"/"+e.id+"/"+e.current_iteration),i};class r7{constructor(e){this._instance=e,this.He=new Map,this.We=new Map}register(e){var t;T(null==(t=this._instance)?void 0:t.Ne)||(this.Ge(e),this.Je(e))}Je(e){var t=e.filter(e=>{var t,i;return(null==(t=e.conditions)?void 0:t.actions)&&(null==(i=e.conditions)||null==(i=i.actions)||null==(i=i.values)?void 0:i.length)>0});0!==t.length&&(null==this.Ve&&(this.Ve=new r5(this._instance),this.Ve.init(),this.Ve.ze(e=>{this.onAction(e)})),t.forEach(e=>{var t,i,s,r,n;e.conditions&&null!=(t=e.conditions)&&t.actions&&null!=(i=e.conditions)&&null!=(i=i.actions)&&i.values&&(null==(s=e.conditions)||null==(s=s.actions)||null==(s=s.values)?void 0:s.length)>0&&(null==(r=this.Ve)||r.register(e.conditions.actions.values),null==(n=e.conditions)||null==(n=n.actions)||null==(n=n.values)||n.forEach(t=>{if(t&&t.name){var i=this.We.get(t.name);i&&i.push(e.id),this.We.set(t.name,i||[e.id])}}))}))}Ge(e){var t;0!==e.filter(e=>{var t,i;return(null==(t=e.conditions)?void 0:t.events)&&(null==(i=e.conditions)||null==(i=i.events)||null==(i=i.values)?void 0:i.length)>0}).length&&(null==(t=this._instance)||t.Ne((e,t)=>{this.onEvent(e,t)}),e.forEach(e=>{var t;null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)||t.forEach(t=>{if(t&&t.name){var i=this.He.get(t.name);i&&i.push(e.id),this.He.set(t.name,i||[e.id])}})}))}onEvent(e,t){var i,s=(null==(i=this._instance)||null==(i=i.persistence)?void 0:i.props[eO])||[];if("survey shown"===e&&t&&s.length>0){r6.info("survey event matched, removing survey from activated surveys",{event:e,eventPayload:t,existingActivatedSurveys:s});var r,n=null==t||null==(r=t.properties)?void 0:r.$survey_id;if(n){var o=s.indexOf(n);o>=0&&(s.splice(o,1),this.Ke(s))}}else this.He.has(e)&&(r6.info("survey event matched, updating activated surveys",{event:e,surveys:this.He.get(e)}),this.Ke(s.concat(this.He.get(e)||[])))}onAction(e){var t,i=(null==(t=this._instance)||null==(t=t.persistence)?void 0:t.props[eO])||[];this.We.has(e)&&this.Ke(i.concat(this.We.get(e)||[]))}Ke(e){var t;null==(t=this._instance)||null==(t=t.persistence)||t.register({[eO]:[...new Set(e)]})}getSurveys(){var e;return(null==(e=this._instance)||null==(e=e.persistence)?void 0:e.props[eO])||[]}getEventToSurveys(){return this.He}Ye(){return this.Ve}}class r9{constructor(e){this.Xe=null,this.Qe=!1,this.Ze=!1,this.tr=[],this._instance=e,this._surveyEventReceiver=null}onRemoteConfig(e){var t=e.surveys;if(L(t))return r6.warn("Decide not loaded yet. Not loading surveys.");var i=F(t);this.ir=i?t.length>0:t,r6.info("decide response received, hasSurveys: "+this.ir),this.ir&&this.loadIfEnabled()}reset(){localStorage.removeItem("lastSeenSurveyDate");for(var e=[],t=0;t<localStorage.length;t++){var i=localStorage.key(t);(null!=i&&i.startsWith(r4)||null!=i&&i.startsWith("inProgressSurvey_"))&&e.push(i)}e.forEach(e=>localStorage.removeItem(e))}loadIfEnabled(){if(!this.Xe)if(this.Ze)r6.info("Already initializing surveys, skipping...");else if(this._instance.config.disable_surveys)r6.info("Disabled. Not loading surveys.");else if(this.ir){var e=null==_?void 0:_.__PosthogExtensions__;if(e){this.Ze=!0;try{var t=e.generateSurveys;if(t)return void this.er(t);var i=e.loadExternalDependency;if(!i)return void this.rr("PostHog loadExternalDependency extension not found.");i(this._instance,"surveys",t=>{t||!e.generateSurveys?this.rr("Could not load surveys script",t):this.er(e.generateSurveys)})}catch(e){throw this.rr("Error initializing surveys",e),e}finally{this.Ze=!1}}else r6.error("PostHog Extensions not found.")}else r6.info("No surveys to load.")}er(e){this.Xe=e(this._instance),this._surveyEventReceiver=new r7(this._instance),r6.info("Surveys loaded successfully"),this.sr({isLoaded:!0})}rr(e,t){r6.error(e,t),this.sr({isLoaded:!1,error:e})}onSurveysLoaded(e){return this.tr.push(e),this.Xe&&this.sr({isLoaded:!0}),()=>{this.tr=this.tr.filter(t=>t!==e)}}getSurveys(e,t){if(void 0===t&&(t=!1),this._instance.config.disable_surveys)return r6.info("Disabled. Not loading surveys."),e([]);var i=this._instance.get_property(eC);if(i&&!t)return e(i,{isLoaded:!0});if(this.Qe)return e([],{isLoaded:!1,error:"Surveys are already being loaded"});try{this.Qe=!0,this._instance.me({url:this._instance.requestRouter.endpointFor("api","/api/surveys/?token="+this._instance.config.token),method:"GET",timeout:this._instance.config.surveys_request_timeout_ms,callback:t=>{this.Qe=!1;var i=t.statusCode;if(200!==i||!t.json){var s="Surveys API could not be loaded, status: "+i;return r6.error(s),e([],{isLoaded:!1,error:s})}var r,n,o=t.json.surveys||[],a=o.filter(e=>{var t,i;return!(!e.start_date||e.end_date)&&(!(null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)||!t.length)||!(null==(i=e.conditions)||null==(i=i.actions)||null==(i=i.values)||!i.length))});return a.length>0&&(null==(n=this._surveyEventReceiver)||n.register(a)),null==(r=this._instance.persistence)||r.register({[eC]:o}),e(o,{isLoaded:!0})}})}catch(e){throw this.Qe=!1,e}}sr(e){for(var t of this.tr)try{e.isLoaded?this.getSurveys(t):t([],e)}catch(e){r6.error("Error in survey callback",e)}}getActiveMatchingSurveys(e,t){if(void 0===t&&(t=!1),!L(this.Xe))return this.Xe.getActiveMatchingSurveys(e,t);r6.warn("init was not called")}nr(e){var t=null;return this.getSurveys(i=>{var s;t=null!==(s=i.find(t=>t.id===e))&&void 0!==s?s:null}),t}ar(e){if(L(this.Xe))return{eligible:!1,reason:"SDK is not enabled or survey functionality is not yet loaded"};var t="string"==typeof e?this.nr(e):e;return t?this.Xe.checkSurveyEligibility(t):{eligible:!1,reason:"Survey not found"}}canRenderSurvey(e){if(L(this.Xe))return r6.warn("init was not called"),{visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"};var t=this.ar(e);return{visible:t.eligible,disabledReason:t.reason}}canRenderSurveyAsync(e,t){return L(this.Xe)?(r6.warn("init was not called"),Promise.resolve({visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"})):new Promise(i=>{this.getSurveys(t=>{var s,r=null!==(s=t.find(t=>t.id===e))&&void 0!==s?s:null;if(r){var n=this.ar(r);i({visible:n.eligible,disabledReason:n.reason})}else i({visible:!1,disabledReason:"Survey not found"})},t)})}renderSurvey(e,t){if(L(this.Xe))r6.warn("init was not called");else{var i=this.nr(e),s=null==c?void 0:c.querySelector(t);i?s?this.Xe.renderSurvey(i,s):r6.warn("Survey element not found"):r6.warn("Survey not found")}}}(function(e){e.Button="button",e.Tab="tab",e.Selector="selector"})({}),function(e){e.TopLeft="top_left",e.TopRight="top_right",e.TopCenter="top_center",e.MiddleLeft="middle_left",e.MiddleRight="middle_right",e.MiddleCenter="middle_center",e.Left="left",e.Center="center",e.Right="right",e.NextToTrigger="next_to_trigger"}({}),function(e){e.Popover="popover",e.API="api",e.Widget="widget"}({}),function(e){e.Open="open",e.MultipleChoice="multiple_choice",e.SingleChoice="single_choice",e.Rating="rating",e.Link="link"}({}),function(e){e.NextQuestion="next_question",e.End="end",e.ResponseBased="response_based",e.SpecificQuestion="specific_question"}({}),function(e){e.Once="once",e.Recurring="recurring",e.Always="always"}({});var ne=function(e){return e.SHOWN="survey shown",e.DISMISSED="survey dismissed",e.SENT="survey sent",e}({}),nt=function(e){return e.SURVEY_ID="$survey_id",e.SURVEY_NAME="$survey_name",e.SURVEY_RESPONSE="$survey_response",e.SURVEY_ITERATION="$survey_iteration",e.SURVEY_ITERATION_START_DATE="$survey_iteration_start_date",e.SURVEY_PARTIALLY_COMPLETED="$survey_partially_completed",e.SURVEY_SUBMISSION_ID="$survey_submission_id",e.SURVEY_QUESTIONS="$survey_questions",e.SURVEY_COMPLETED="$survey_completed",e}({}),ni=U("[RateLimiter]");class ns{constructor(e){var t,i;this.serverLimits={},this.lastEventRateLimited=!1,this.checkForLimiting=e=>{var t=e.text;if(t&&t.length)try{(JSON.parse(t).quota_limited||[]).forEach(e=>{ni.info((e||"events")+" is quota limited."),this.serverLimits[e]=(new Date).getTime()+6e4})}catch(e){return void ni.warn('could not rate limit - continuing. Error: "'+(null==e?void 0:e.message)+'"',{text:t})}},this.instance=e,this.captureEventsPerSecond=(null==(t=e.config.rate_limiting)?void 0:t.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null==(i=e.config.rate_limiting)?void 0:i.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(e){void 0===e&&(e=!1);var t,i,s,r=(new Date).getTime(),n=null!==(t=null==(i=this.instance.persistence)?void 0:i.get_property(eN))&&void 0!==t?t:{tokens:this.captureEventsBurstLimit,last:r};n.tokens+=(r-n.last)/1e3*this.captureEventsPerSecond,n.last=r,n.tokens>this.captureEventsBurstLimit&&(n.tokens=this.captureEventsBurstLimit);var o=n.tokens<1;return o||e||(n.tokens=Math.max(0,n.tokens-1)),!o||this.lastEventRateLimited||e||this.instance.capture("$$client_ingestion_warning",{$$client_ingestion_warning_message:"posthog-js client rate limited. Config is set to "+this.captureEventsPerSecond+" events per second and "+this.captureEventsBurstLimit+" events burst limit."},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=o,null==(s=this.instance.persistence)||s.set_property(eN,n),{isRateLimited:o,remainingTokens:n.tokens}}isServerRateLimited(e){var t=this.serverLimits[e||"events"]||!1;return!1!==t&&(new Date).getTime()<t}}var nr=U("[RemoteConfig]");class nn{constructor(e){this._instance=e}get remoteConfig(){var e;return null==(e=_._POSTHOG_REMOTE_CONFIG)||null==(e=e[this._instance.config.token])?void 0:e.config}lr(e){var t,i;null!=(t=_.__PosthogExtensions__)&&t.loadExternalDependency?null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"remote-config",()=>e(this.remoteConfig)):(nr.error("PostHog Extensions not found. Cannot load remote config."),e())}ur(e){this._instance.me({method:"GET",url:this._instance.requestRouter.endpointFor("assets","/array/"+this._instance.config.token+"/config"),callback:t=>{e(t.json)}})}load(){try{if(this.remoteConfig)return nr.info("Using preloaded remote config",this.remoteConfig),void this.be(this.remoteConfig);if(this._instance.config.advanced_disable_decide)return void nr.warn("Remote config is disabled. Falling back to local config.");this.lr(e=>{if(!e)return nr.info("No config found after loading remote JS config. Falling back to JSON."),void this.ur(e=>{this.be(e)});this.be(e)})}catch(e){nr.error("Error loading remote config",e)}}be(e){e?this._instance.config.__preview_remote_config?(this._instance.be(e),!1!==e.hasFeatureFlags&&this._instance.featureFlags.ensureFlagsLoaded()):nr.info("__preview_remote_config is disabled. Logging config instead",e):nr.error("Failed to fetch remote config from PostHog.")}}class no{constructor(e,t){this.hr=!0,this.dr=[],this.vr=iv((null==t?void 0:t.flush_interval_ms)||3e3,250,5e3,"flush interval",3e3),this.cr=e}enqueue(e){this.dr.push(e),this.pr||this._r()}unload(){this.gr();var e=Object.values(this.dr.length>0?this.mr():{});[...e.filter(e=>0===e.url.indexOf("/e")),...e.filter(e=>0!==e.url.indexOf("/e"))].map(e=>{this.cr(G({},e,{transport:"sendBeacon"}))})}enable(){this.hr=!1,this._r()}_r(){var e=this;this.hr||(this.pr=setTimeout(()=>{if(this.gr(),this.dr.length>0){var t=this.mr();for(var i in t)!function(){var s=t[i],r=(new Date).getTime();s.data&&F(s.data)&&J(s.data,e=>{e.offset=Math.abs(e.timestamp-r),delete e.timestamp}),e.cr(s)}()}},this.vr))}gr(){clearTimeout(this.pr),this.pr=void 0}mr(){var e={};return J(this.dr,t=>{var i,s=(t?t.batchKey:null)||t.url;T(e[s])&&(e[s]=G({},t,{data:[]})),null==(i=e[s].data)||i.push(t.data)}),this.dr=[],e}}var na=["retriesPerformedSoFar"];class nl{constructor(e){this.br=!1,this.yr=3e3,this.dr=[],this._instance=e,this.dr=[],this.wr=!0,!T(s)&&"onLine"in s.navigator&&(this.wr=s.navigator.onLine,en(s,"online",()=>{this.wr=!0,this.Yi()}),en(s,"offline",()=>{this.wr=!1}))}get length(){return this.dr.length}retriableRequest(e){var{retriesPerformedSoFar:t}=e,i=V(e,na);D(t)&&t>0&&(i.url=sN(i.url,{retry_count:t})),this._instance.me(G({},i,{callback:e=>{200!==e.statusCode&&(e.statusCode<400||e.statusCode>=500)&&(null!=t?t:0)<10?this.Sr(G({retriesPerformedSoFar:t},i)):null==i.callback||i.callback(e)}}))}Sr(e){var t,i,s,r=e.retriesPerformedSoFar||0;e.retriesPerformedSoFar=r+1;var n=(s=(Math.random()-.5)*((i=Math.min(18e5,t=3e3*Math.pow(2,r)))-t/2),Math.ceil(i+s)),o=Date.now()+n;this.dr.push({retryAt:o,requestOptions:e});var a="Enqueued failed request for retry in "+n;navigator.onLine||(a+=" (Browser is offline)"),j.warn(a),this.br||(this.br=!0,this.$r())}$r(){this.kr&&clearTimeout(this.kr),this.kr=setTimeout(()=>{this.wr&&this.dr.length>0&&this.Yi(),this.$r()},this.yr)}Yi(){var e=Date.now(),t=[],i=this.dr.filter(i=>i.retryAt<e||(t.push(i),!1));if(this.dr=t,i.length>0)for(var{requestOptions:s}of i)this.retriableRequest(s)}unload(){for(var{requestOptions:e}of(this.kr&&(clearTimeout(this.kr),this.kr=void 0),this.dr))try{this._instance.me(G({},e,{transport:"sendBeacon"}))}catch(e){j.error(e)}this.dr=[]}}class nc{constructor(e){this.Er=()=>{this.Ir||(this.Ir={});var e,t,i,s,r=this.scrollElement(),n=this.scrollY(),o=r?Math.max(0,r.scrollHeight-r.clientHeight):0,a=n+((null==r?void 0:r.clientHeight)||0),l=(null==r?void 0:r.scrollHeight)||0;this.Ir.lastScrollY=Math.ceil(n),this.Ir.maxScrollY=Math.max(n,null!==(e=this.Ir.maxScrollY)&&void 0!==e?e:0),this.Ir.maxScrollHeight=Math.max(o,null!==(t=this.Ir.maxScrollHeight)&&void 0!==t?t:0),this.Ir.lastContentY=a,this.Ir.maxContentY=Math.max(a,null!==(i=this.Ir.maxContentY)&&void 0!==i?i:0),this.Ir.maxContentHeight=Math.max(l,null!==(s=this.Ir.maxContentHeight)&&void 0!==s?s:0)},this._instance=e}getContext(){return this.Ir}resetContext(){var e=this.Ir;return setTimeout(this.Er,0),e}startMeasuringScrollPosition(){en(s,"scroll",this.Er,{capture:!0}),en(s,"scrollend",this.Er,{capture:!0}),en(s,"resize",this.Er)}scrollElement(){if(!this._instance.config.scroll_root_selector)return null==s?void 0:s.document.documentElement;for(var e of F(this._instance.config.scroll_root_selector)?this._instance.config.scroll_root_selector:[this._instance.config.scroll_root_selector]){var t=null==s?void 0:s.document.querySelector(e);if(t)return t}}scrollY(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollTop||0}return s&&(s.scrollY||s.pageYOffset||s.document.documentElement.scrollTop)||0}scrollX(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollLeft||0}return s&&(s.scrollX||s.pageXOffset||s.document.documentElement.scrollLeft)||0}}var nu=e=>rj(null==e?void 0:e.config.mask_personal_data_properties,null==e?void 0:e.config.custom_personal_data_properties);class nh{constructor(e,t,i,s){this.Pr=e=>{var t=this.Rr();if(!t||t.sessionId!==e){var i={sessionId:e,props:this.Tr(this._instance)};this.Mr.register({[eD]:i})}},this._instance=e,this.Cr=t,this.Mr=i,this.Tr=s||nu,this.Cr.onSessionId(this.Pr)}Rr(){return this.Mr.props[eD]}getSetOnceProps(){var e,t=null==(e=this.Rr())?void 0:e.props;return t?"r"in t?rU(t):{$referring_domain:t.referringDomain,$pathname:t.initialPathName,utm_source:t.utm_source,utm_campaign:t.utm_campaign,utm_medium:t.utm_medium,utm_content:t.utm_content,utm_term:t.utm_term}:{}}getSessionProps(){var e={};return J(ei(this.getSetOnceProps()),(t,i)=>{"$current_url"===i&&(i="url"),e["$session_entry_"+k(i)]=t}),e}}var nd=U("[SessionId]");class np{constructor(e,t,i){if(this.Or=[],!e.persistence)throw Error("SessionIdManager requires a PostHogPersistence instance");if(e.config.__preview_experimental_cookieless_mode)throw Error("SessionIdManager cannot be used with __preview_experimental_cookieless_mode");this.S=e.config,this.Mr=e.persistence,this.oi=void 0,this.kt=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this.Fr=t||tE,this.Ar=i||tE;var s,r=this.S.persistence_name||this.S.token,n=this.S.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*iv(n,60,36e3,"session_idle_timeout_seconds",1800),e.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.Dr(),this.Lr="ph_"+r+"_window_id",this.Nr="ph_"+r+"_primary_window_exists",this.jr()){var o=tO.D(this.Lr),a=tO.D(this.Nr);o&&!a?this.oi=o:tO.N(this.Lr),tO.L(this.Nr,!0)}if(null!=(s=this.S.bootstrap)&&s.sessionID)try{var l=(e=>{var t=e.replace(/-/g,"");if(32!==t.length)throw Error("Not a valid UUID");if("7"!==t[12])throw Error("Not a UUIDv7");return parseInt(t.substring(0,12),16)})(this.S.bootstrap.sessionID);this.zr(this.S.bootstrap.sessionID,(new Date).getTime(),l)}catch(e){nd.error("Invalid sessionID in bootstrap",e)}this.Ur()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(e){return T(this.Or)&&(this.Or=[]),this.Or.push(e),this.kt&&e(this.kt,this.oi),()=>{this.Or=this.Or.filter(t=>t!==e)}}jr(){return"memory"!==this.S.persistence&&!this.Mr.xe&&tO.O()}qr(e){e!==this.oi&&(this.oi=e,this.jr()&&tO.L(this.Lr,e))}Br(){return this.oi?this.oi:this.jr()?tO.D(this.Lr):null}zr(e,t,i){e===this.kt&&t===this._sessionActivityTimestamp&&i===this._sessionStartTimestamp||(this._sessionStartTimestamp=i,this._sessionActivityTimestamp=t,this.kt=e,this.Mr.register({[eS]:[t,e,i]}))}Hr(){if(this.kt&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this.kt,this._sessionStartTimestamp];var e=this.Mr.props[eS];return F(e)&&2===e.length&&e.push(e[0]),e||[0,null,0]}resetSessionId(){this.zr(null,null,null)}Ur(){en(s,"beforeunload",()=>{this.jr()&&tO.N(this.Nr)},{capture:!1})}checkAndGetSessionAndWindowId(e,t){if(void 0===e&&(e=!1),void 0===t&&(t=null),this.S.__preview_experimental_cookieless_mode)throw Error("checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode");var i=t||(new Date).getTime(),[s,r,n]=this.Hr(),o=this.Br(),a=D(n)&&n>0&&Math.abs(i-n)>864e5,l=!1,c=!r,u=!e&&Math.abs(i-s)>this.sessionTimeoutMs;c||u||a?(r=this.Fr(),o=this.Ar(),nd.info("new session ID generated",{sessionId:r,windowId:o,changeReason:{noSessionId:c,activityTimeout:u,sessionPastMaximumLength:a}}),n=i,l=!0):o||(o=this.Ar(),l=!0);var h=0===s||!e||a?i:s,d=0===n?(new Date).getTime():n;return this.qr(o),this.zr(r,h,d),e||this.Dr(),l&&this.Or.forEach(e=>e(r,o,l?{noSessionId:c,activityTimeout:u,sessionPastMaximumLength:a}:void 0)),{sessionId:r,windowId:o,sessionStartTimestamp:d,changeReason:l?{noSessionId:c,activityTimeout:u,sessionPastMaximumLength:a}:void 0,lastActivityTimestamp:s}}Dr(){clearTimeout(this.Wr),this.Wr=setTimeout(()=>{this.resetSessionId()},1.1*this.sessionTimeoutMs)}}var ng=["$set_once","$set"],n_=U("[SiteApps]");class nv{constructor(e){this._instance=e,this.Gr=[],this.apps={}}get isEnabled(){return!!this._instance.config.opt_in_site_apps}Jr(e,t){if(t){var i=this.globalsForEvent(t);this.Gr.push(i),this.Gr.length>1e3&&(this.Gr=this.Gr.slice(10))}}get siteAppLoaders(){var e;return null==(e=_._POSTHOG_REMOTE_CONFIG)||null==(e=e[this._instance.config.token])?void 0:e.siteApps}init(){if(this.isEnabled){var e=this._instance.Ne(this.Jr.bind(this));this.Vr=()=>{e(),this.Gr=[],this.Vr=void 0}}}globalsForEvent(e){if(!e)throw Error("Event payload is required");var t,i,s,r,n,o,a,l={},c=this._instance.get_property("$groups")||[];for(var[u,h]of Object.entries(this._instance.get_property("$stored_group_properties")||{}))l[u]={id:c[u],type:u,properties:h};var{$set_once:d,$set:p}=e;return{event:G({},V(e,ng),{properties:G({},e.properties,p?{$set:G({},null!==(t=null==(i=e.properties)?void 0:i.$set)&&void 0!==t?t:{},p)}:{},d?{$set_once:G({},null!==(s=null==(r=e.properties)?void 0:r.$set_once)&&void 0!==s?s:{},d)}:{}),elements_chain:null!==(n=null==(o=e.properties)?void 0:o.$elements_chain)&&void 0!==n?n:"",distinct_id:null==(a=e.properties)?void 0:a.distinct_id}),person:{properties:this._instance.get_property("$stored_person_properties")},groups:l}}setupSiteApp(e){var t=this.apps[e.id],i=()=>{var i;!t.errored&&this.Gr.length&&(n_.info("Processing "+this.Gr.length+" events for site app with id "+e.id),this.Gr.forEach(e=>null==t.processEvent?void 0:t.processEvent(e)),t.processedBuffer=!0),Object.values(this.apps).every(e=>e.processedBuffer||e.errored)&&(null==(i=this.Vr)||i.call(this))},s=!1,r=r=>{t.errored=!r,t.loaded=!0,n_.info("Site app with id "+e.id+" "+(r?"loaded":"errored")),s&&i()};try{var{processEvent:n}=e.init({posthog:this._instance,callback:e=>{r(e)}});n&&(t.processEvent=n),s=!0}catch(t){n_.error("Error while initializing PostHog app with config id "+e.id,t),r(!1)}if(s&&t.loaded)try{i()}catch(i){n_.error("Error while processing buffered events PostHog app with config id "+e.id,i),t.errored=!0}}Kr(){var e=this.siteAppLoaders||[];for(var t of e)this.apps[t.id]={id:t.id,loaded:!1,errored:!1,processedBuffer:!1};for(var i of e)this.setupSiteApp(i)}Yr(e){if(0!==Object.keys(this.apps).length){var t=this.globalsForEvent(e);for(var i of Object.values(this.apps))try{null==i.processEvent||i.processEvent(t)}catch(t){n_.error("Error while processing event "+e.event+" for site app "+i.id,t)}}}onRemoteConfig(e){var t,i,s,r=this;if(null!=(t=this.siteAppLoaders)&&t.length)return this.isEnabled?(this.Kr(),void this._instance.on("eventCaptured",e=>this.Yr(e))):void n_.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.');if(null==(i=this.Vr)||i.call(this),null!=(s=e.siteApps)&&s.length)if(this.isEnabled){var n=function(e){var t;_["__$$ph_site_app_"+e]=r._instance,null==(t=_.__PosthogExtensions__)||null==t.loadSiteApp||t.loadSiteApp(r._instance,a,t=>{if(t)return n_.error("Error while initializing PostHog app with config id "+e,t)})};for(var{id:o,url:a}of e.siteApps)n(o)}else n_.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')}}var nf=["amazonbot","amazonproductbot","app.hypefactors.com","applebot","archive.org_bot","awariobot","backlinksextendedbot","baiduspider","bingbot","bingpreview","chrome-lighthouse","dataforseobot","deepscan","duckduckbot","facebookexternal","facebookcatalog","http://yandex.com/bots","hubspot","ia_archiver","leikibot","linkedinbot","meta-externalagent","mj12bot","msnbot","nessus","petalbot","pinterest","prerender","rogerbot","screaming frog","sebot-wa","sitebulb","slackbot","slurp","trendictionbot","turnitin","twitterbot","vercelbot","yahoo! slurp","yandexbot","zoombot","bot.htm","bot.php","(bot;","bot/","crawler","ahrefsbot","ahrefssiteaudit","semrushbot","siteauditbot","splitsignalbot","gptbot","oai-searchbot","chatgpt-user","perplexitybot","better uptime bot","sentryuptimebot","uptimerobot","headlesschrome","cypress","google-hoteladsverifier","adsbot-google","apis-google","duplexweb-google","feedfetcher-google","google favicon","google web preview","google-read-aloud","googlebot","googleother","google-cloudvertexbot","googleweblight","mediapartners-google","storebot-google","google-inspectiontool","bytespider"],nm=function(e,t){if(!e)return!1;var i=e.toLowerCase();return nf.concat(t||[]).some(e=>{var t=e.toLowerCase();return -1!==i.indexOf(t)})},ny=function(e,t){if(!e)return!1;var i=e.userAgent;if(i&&nm(i,t))return!0;try{var s=null==e?void 0:e.userAgentData;if(null!=s&&s.brands&&s.brands.some(e=>nm(null==e?void 0:e.brand,t)))return!0}catch(e){}return!!e.webdriver},nb=function(e){return e.US="us",e.EU="eu",e.CUSTOM="custom",e}({}),nw="i.posthog.com";class nE{constructor(e){this.Xr={},this.instance=e}get apiHost(){var e=this.instance.config.api_host.trim().replace(/\/$/,"");return"https://app.posthog.com"===e?"https://us.i.posthog.com":e}get uiHost(){var e,t=null==(e=this.instance.config.ui_host)?void 0:e.replace(/\/$/,"");return t||(t=this.apiHost.replace("."+nw,".posthog.com")),"https://app.posthog.com"===t?"https://us.posthog.com":t}get region(){return this.Xr[this.apiHost]||(/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this.Xr[this.apiHost]=nb.US:/https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this.Xr[this.apiHost]=nb.EU:this.Xr[this.apiHost]=nb.CUSTOM),this.Xr[this.apiHost]}endpointFor(e,t){if(void 0===t&&(t=""),t&&(t="/"===t[0]?t:"/"+t),"ui"===e)return this.uiHost+t;if(this.region===nb.CUSTOM)return this.apiHost+t;var i=nw+t;switch(e){case"assets":return"https://"+this.region+"-assets."+i;case"api":return"https://"+this.region+"."+i}}}var nk={icontains:(e,t)=>!!s&&t.href.toLowerCase().indexOf(e.toLowerCase())>-1,not_icontains:(e,t)=>!!s&&-1===t.href.toLowerCase().indexOf(e.toLowerCase()),regex:(e,t)=>!!s&&sj(t.href,e),not_regex:(e,t)=>!!s&&!sj(t.href,e),exact:(e,t)=>t.href===e,is_not:(e,t)=>t.href!==e};class nS{constructor(e){var t=this;this.getWebExperimentsAndEvaluateDisplayLogic=function(e){void 0===e&&(e=!1),t.getWebExperiments(e=>{nS.Qr("retrieved web experiments from the server"),t.Zr=new Map,e.forEach(e=>{if(e.feature_flag_key){t.Zr&&(nS.Qr("setting flag key ",e.feature_flag_key," to web experiment ",e),null==(i=t.Zr)||i.set(e.feature_flag_key,e));var i,s=t._instance.getFeatureFlag(e.feature_flag_key);C(s)&&e.variants[s]&&t.ts(e.name,s,e.variants[s].transforms)}else if(e.variants)for(var r in e.variants){var n=e.variants[r];nS.es(n)&&t.ts(e.name,r,n.transforms)}})},e)},this._instance=e,this._instance.onFeatureFlags(e=>{this.onFeatureFlags(e)})}onFeatureFlags(e){if(this._is_bot())nS.Qr("Refusing to render web experiment since the viewer is a likely bot");else if(!this._instance.config.disable_web_experiments){if(L(this.Zr))return this.Zr=new Map,this.loadIfEnabled(),void this.previewWebExperiment();nS.Qr("applying feature flags",e),e.forEach(e=>{var t;if(this.Zr&&null!=(t=this.Zr)&&t.has(e)){var i,s=this._instance.getFeatureFlag(e),r=null==(i=this.Zr)?void 0:i.get(e);s&&null!=r&&r.variants[s]&&this.ts(r.name,s,r.variants[s].transforms)}})}}previewWebExperiment(){var e=nS.getWindowLocation();if(null!=e&&e.search){var t=tu(null==e?void 0:e.search,"__experiment_id"),i=tu(null==e?void 0:e.search,"__experiment_variant");t&&i&&(nS.Qr("previewing web experiments "+t+" && "+i),this.getWebExperiments(e=>{this.rs(parseInt(t),i,e)},!1,!0))}}loadIfEnabled(){this._instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(e,t,i){if(this._instance.config.disable_web_experiments&&!i)return e([]);var s=this._instance.get_property("$web_experiments");if(s&&!t)return e(s);this._instance.me({url:this._instance.requestRouter.endpointFor("api","/api/web_experiments/?token="+this._instance.config.token),method:"GET",callback:t=>200===t.statusCode&&t.json?e(t.json.experiments||[]):e([])})}rs(e,t,i){var s=i.filter(t=>t.id===e);s&&s.length>0&&(nS.Qr("Previewing web experiment ["+s[0].name+"] with variant ["+t+"]"),this.ts(s[0].name,t,s[0].variants[t].transforms))}static es(e){return!L(e.conditions)&&nS.ss(e)&&nS.ns(e)}static ss(e){if(L(e.conditions)||L(null==(t=e.conditions)?void 0:t.url))return!0;var t,i,s,r,n=nS.getWindowLocation();return!!n&&(null==(i=e.conditions)||!i.url||nk[null!==(s=null==(r=e.conditions)?void 0:r.urlMatchType)&&void 0!==s?s:"icontains"](e.conditions.url,n))}static getWindowLocation(){return null==s?void 0:s.location}static ns(e){if(L(e.conditions)||L(null==(i=e.conditions)?void 0:i.utm))return!0;var t=rD();if(t.utm_source){var i,s,r,n,o,a,l,c,u,h=null==(s=e.conditions)||null==(s=s.utm)||!s.utm_campaign||(null==(r=e.conditions)||null==(r=r.utm)?void 0:r.utm_campaign)==t.utm_campaign,d=null==(n=e.conditions)||null==(n=n.utm)||!n.utm_source||(null==(o=e.conditions)||null==(o=o.utm)?void 0:o.utm_source)==t.utm_source,p=null==(a=e.conditions)||null==(a=a.utm)||!a.utm_medium||(null==(l=e.conditions)||null==(l=l.utm)?void 0:l.utm_medium)==t.utm_medium,g=null==(c=e.conditions)||null==(c=c.utm)||!c.utm_term||(null==(u=e.conditions)||null==(u=u.utm)?void 0:u.utm_term)==t.utm_term;return h&&p&&g&&d}return!1}static Qr(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];j.info("[WebExperiments] "+e,i)}ts(e,t,i){this._is_bot()?nS.Qr("Refusing to render web experiment since the viewer is a likely bot"):"control"!==t?i.forEach(i=>{if(i.selector){nS.Qr("applying transform of variant "+t+" for experiment "+e+" ",i);var s,r=null==(s=document)?void 0:s.querySelectorAll(i.selector);null==r||r.forEach(e=>{i.html&&(e.innerHTML=i.html),i.css&&e.setAttribute("style",i.css)})}}):nS.Qr("Control variants leave the page unmodified.")}_is_bot(){return l&&this._instance?ny(l,this._instance.config.custom_blocked_useragents):void 0}}var nx={},nI=()=>{},n$="posthog",nF=!sL&&-1===(null==g?void 0:g.indexOf("MSIE"))&&-1===(null==g?void 0:g.indexOf("Mozilla")),nR=e=>{var t;return{api_host:"https://us.i.posthog.com",ui_host:null,token:"",autocapture:!0,rageclick:!0,cross_subdomain_cookie:function(e){var t=null==e?void 0:e.hostname;if(!C(t))return!1;var i=t.split(".").slice(-2).join(".");for(var s of es)if(i===s)return!1;return!0}(null==c?void 0:c.location),persistence:"localStorage+cookie",persistence_name:"",loaded:nI,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:"2025-05-24"!==e||"history_change",capture_pageleave:"if_capture_pageview",defaults:null!=e?e:"unset",debug:u&&C(null==u?void 0:u.search)&&-1!==u.search.indexOf("__posthog_debug=true")||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:"https:"===(null==s||null==(t=s.location)?void 0:t.protocol),ip:!0,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:"localStorage",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_only_evaluate_survey_feature_flags:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,surveys_request_timeout_ms:1e4,on_request_error:e=>{var t="Bad HTTP status: "+e.statusCode+" "+e.text;j.error(t)},get_device_id:e=>e,capture_performance:void 0,name:"posthog",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:"identified_only",before_send:void 0,request_queue_config:{flush_interval_ms:3e3},_onCapture:nI}},nP=e=>{var t={};T(e.process_person)||(t.person_profiles=e.process_person),T(e.xhr_headers)||(t.request_headers=e.xhr_headers),T(e.cookie_name)||(t.persistence_name=e.cookie_name),T(e.disable_cookie)||(t.disable_persistence=e.disable_cookie),T(e.store_google)||(t.save_campaign_params=e.store_google),T(e.verbose)||(t.debug=e.verbose);var i=Z({},t,e);return F(e.property_blacklist)&&(T(e.property_denylist)?i.property_denylist=e.property_blacklist:F(e.property_denylist)?i.property_denylist=[...e.property_blacklist,...e.property_denylist]:j.error("Invalid value for property_denylist config: "+e.property_denylist)),i};class nM{constructor(){this.__forceAllowLocalhost=!1}get os(){return this.__forceAllowLocalhost}set os(e){j.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"),this.__forceAllowLocalhost=e}}class nT{get decideEndpointWasHit(){var e,t;return null!==(e=null==(t=this.featureFlags)?void 0:t.hasLoadedFlags)&&void 0!==e&&e}constructor(){this.webPerformance=new nM,this.ls=!1,this.version=v.LIB_VERSION,this.us=new r3,this._calculate_event_properties=this.calculateEventProperties.bind(this),this.config=nR(),this.SentryIntegration=sw,this.sentryIntegration=e=>(function(e,t){var i=sb(e,t);return{name:sy,processEvent:e=>i(e)}})(this,e),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint="/e/",this.hs=!1,this.ds=null,this.vs=null,this.cs=null,this.featureFlags=new r0(this),this.toolbar=new sI(this),this.scrollManager=new nc(this),this.pageViewManager=new sO(this),this.surveys=new r9(this),this.experiments=new nS(this),this.exceptions=new sV(this),this.rateLimiter=new ns(this),this.requestRouter=new nE(this),this.consent=new tL(this),this.people={set:(e,t,i)=>{var s=C(e)?{[e]:t}:e;this.setPersonProperties(s),null==i||i({})},set_once:(e,t,i)=>{var s=C(e)?{[e]:t}:e;this.setPersonProperties(void 0,s),null==i||i({})}},this.on("eventCaptured",e=>j.info('send "'+(null==e?void 0:e.event)+'"',e))}init(e,t,i){if(i&&i!==n$){var s,r=null!==(s=nx[i])&&void 0!==s?s:new nT;return r._init(e,t,i),nx[i]=r,nx[n$][i]=r,r}return this._init(e,t,i)}_init(e,t,i){if(void 0===t&&(t={}),T(e)||O(e))return j.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"),this;if(this.__loaded)return j.warn("You have already initialized PostHog! Re-initializing is a no-op"),this;this.__loaded=!0,this.config={},this.fs=[],t.person_profiles&&(this.vs=t.person_profiles),this.set_config(Z({},nR(t.defaults),nP(t),{name:i,token:e})),this.config.on_xhr_error&&j.error("on_xhr_error is deprecated. Use on_request_error instead"),this.compression=t.disable_compression?void 0:y.GZipJS,this.persistence=new r2(this.config),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new r2(G({},this.config,{persistence:"sessionStorage"}));var r=G({},this.persistence.props),n=G({},this.sessionPersistence.props);if(this.ps=new no(e=>this._s(e),this.config.request_queue_config),this.gs=new nl(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new np(this),this.sessionPropsManager=new nh(this,this.sessionManager,this.persistence)),new sF(this).startIfEnabledOrStop(),this.siteApps=new nv(this),null==(o=this.siteApps)||o.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new sf(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new t_(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new sC(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new sP(this),this.exceptionObserver=new tj(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new tB(this,tq),this.deadClicksAutocapture.startIfEnabled(),this.historyAutocapture=new ii(this),this.historyAutocapture.startIfEnabled(),v.DEBUG=v.DEBUG||this.config.debug,v.DEBUG&&j.info("Starting in debug mode",{this:this,config:t,thisC:G({},this.config),p:r,s:n}),this.bs(),void 0!==(null==(a=t.bootstrap)?void 0:a.distinctID)){var o,a,l,c,u=this.config.get_device_id(tE()),h=null!=(l=t.bootstrap)&&l.isIdentifiedID?u:t.bootstrap.distinctID;this.persistence.set_property(eL,null!=(c=t.bootstrap)&&c.isIdentifiedID?"identified":"anonymous"),this.register({distinct_id:t.bootstrap.distinctID,$device_id:h})}if(this.ys()){var d,p,g=Object.keys((null==(d=t.bootstrap)?void 0:d.featureFlags)||{}).filter(e=>{var i;return!(null==(i=t.bootstrap)||null==(i=i.featureFlags)||!i[e])}).reduce((e,i)=>{var s;return e[i]=(null==(s=t.bootstrap)||null==(s=s.featureFlags)?void 0:s[i])||!1,e},{}),_=Object.keys((null==(p=t.bootstrap)?void 0:p.featureFlagPayloads)||{}).filter(e=>g[e]).reduce((e,i)=>{var s,r;return null!=(s=t.bootstrap)&&null!=(s=s.featureFlagPayloads)&&s[i]&&(e[i]=null==(r=t.bootstrap)||null==(r=r.featureFlagPayloads)?void 0:r[i]),e},{});this.featureFlags.receivedFeatureFlags({featureFlags:g,featureFlagPayloads:_})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:ez,$device_id:null},"");else if(!this.get_distinct_id()){var f=this.config.get_device_id(tE());this.register_once({distinct_id:f,$device_id:f},""),this.persistence.set_property(eL,"anonymous")}return en(s,"onpagehide"in self?"pagehide":"unload",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),t.segment?function(e,t){var i=e.config.segment;if(!i)return t();!function(e,t){var i=e.config.segment;if(!i)return t();var s=i=>{var s=()=>i.anonymousId()||tE();e.config.get_device_id=s,i.id()&&(e.register({distinct_id:i.id(),$device_id:s()}),e.persistence.set_property(eL,"identified")),t()},r=i.user();"then"in r&&R(r.then)?r.then(e=>s(e)):s(r)}(e,()=>{var s;i.register((Promise&&Promise.resolve||sm.warn("This browser does not have Promise support, and can not use the segment integration"),s=(t,i)=>{if(!i)return t;t.event.userId||t.event.anonymousId===e.get_distinct_id()||(sm.info("No userId set, resetting PostHog"),e.reset()),t.event.userId&&t.event.userId!==e.get_distinct_id()&&(sm.info("UserId set, identifying with PostHog"),e.identify(t.event.userId));var s=e.calculateEventProperties(i,t.event.properties);return t.event.properties=Object.assign({},s,t.event.properties),t},{name:"PostHog JS",type:"enrichment",version:"1.0.0",isLoaded:()=>!0,load:()=>Promise.resolve(),track:e=>s(e,e.event.event),page:e=>s(e,"$pageview"),identify:e=>s(e,"$identify"),screen:e=>s(e,"$screen")})).then(()=>{t()})})}(this,()=>this.ws()):this.ws(),R(this.config._onCapture)&&this.config._onCapture!==nI&&(j.warn("onCapture is deprecated. Please use `before_send` instead"),this.on("eventCaptured",e=>this.config._onCapture(e.event,e))),this}be(e){var t,i,s,r,n,o,a,l;if(!c||!c.body)return j.info("document not ready yet, trying again in 500 milliseconds..."),void setTimeout(()=>{this.be(e)},500);this.compression=void 0,e.supportedCompression&&!this.config.disable_compression&&(this.compression=w(e.supportedCompression,y.GZipJS)?y.GZipJS:w(e.supportedCompression,y.Base64)?y.Base64:void 0),null!=(t=e.analytics)&&t.endpoint&&(this.analyticsDefaultEndpoint=e.analytics.endpoint),this.set_config({person_profiles:this.vs?this.vs:"identified_only"}),null==(i=this.siteApps)||i.onRemoteConfig(e),null==(s=this.sessionRecording)||s.onRemoteConfig(e),null==(r=this.autocapture)||r.onRemoteConfig(e),null==(n=this.heatmaps)||n.onRemoteConfig(e),this.surveys.onRemoteConfig(e),null==(o=this.webVitalsAutocapture)||o.onRemoteConfig(e),null==(a=this.exceptionObserver)||a.onRemoteConfig(e),this.exceptions.onRemoteConfig(e),null==(l=this.deadClicksAutocapture)||l.onRemoteConfig(e)}ws(){try{this.config.loaded(this)}catch(e){j.critical("`loaded` function failed",e)}this.Ss(),this.config.capture_pageview&&setTimeout(()=>{this.consent.isOptedIn()&&this.$s()},1),new nn(this).load(),this.featureFlags.decide()}Ss(){var e;this.has_opted_out_capturing()||this.config.request_batching&&(null==(e=this.ps)||e.enable())}_dom_loaded(){this.has_opted_out_capturing()||X(this.__request_queue,e=>this._s(e)),this.__request_queue=[],this.Ss()}_handle_unload(){var e,t;this.config.request_batching?(this.ks()&&this.capture("$pageleave"),null==(e=this.ps)||e.unload(),null==(t=this.gs)||t.unload()):this.ks()&&this.capture("$pageleave",null,{transport:"sendBeacon"})}me(e){this.__loaded&&(nF?this.__request_queue.push(e):this.rateLimiter.isServerRateLimited(e.batchKey)||(e.transport=e.transport||this.config.api_transport,e.url=sN(e.url,{ip:+!!this.config.ip}),e.headers=G({},this.config.request_headers),e.compression="best-available"===e.compression?this.compression:e.compression,e.fetchOptions=e.fetchOptions||this.config.fetch_options,(e=>{var t,i,s,r=G({},e);r.timeout=r.timeout||6e4,r.url=sN(r.url,{_:(new Date).getTime().toString(),ver:v.LIB_VERSION,compression:r.compression});var n=null!==(t=r.transport)&&void 0!==t?t:"fetch",o=null!==(i=null==(s=er(sH,e=>e.transport===n))?void 0:s.method)&&void 0!==i?i:sH[0].method;if(!o)throw Error("No available transport method");o(r)})(G({},e,{callback:t=>{var i,s;this.rateLimiter.checkForLimiting(t),t.statusCode>=400&&(null==(i=(s=this.config).on_request_error)||i.call(s,t)),null==e.callback||e.callback(t)}}))))}_s(e){this.gs?this.gs.retriableRequest(e):this.me(e)}_execute_array(e){var t,i=[],s=[],r=[];X(e,e=>{e&&(F(t=e[0])?r.push(e):R(e)?e.call(this):F(e)&&"alias"===t?i.push(e):F(e)&&-1!==t.indexOf("capture")&&R(this[t])?r.push(e):s.push(e))});var n=function(e,t){X(e,function(e){if(F(e[0])){var i=t;J(e,function(e){i=i[e[0]].apply(i,e.slice(1))})}else this[e[0]].apply(this,e.slice(1))},t)};n(i,this),n(s,this),n(r,this)}ys(){var e,t;return(null==(e=this.config.bootstrap)?void 0:e.featureFlags)&&Object.keys(null==(t=this.config.bootstrap)?void 0:t.featureFlags).length>0||!1}push(e){this._execute_array([e])}capture(e,t,i){var s;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this.ps){if(!this.consent.isOptedOut())if(!T(e)&&C(e)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var r=null!=i&&i.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==r||!r.isRateLimited){null!=t&&t.$current_url&&!C(null==t?void 0:t.$current_url)&&(j.error("Invalid `$current_url` property provided to `posthog.capture`. Input must be a string. Ignoring provided value."),null==t||delete t.$current_url),this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var n=new Date,o=(null==i?void 0:i.timestamp)||n,a=tE(),l={uuid:a,event:e,properties:this.calculateEventProperties(e,t||{},o,a)};r&&(l.properties.$lib_rate_limit_remaining_tokens=r.remainingTokens),(null==i?void 0:i.$set)&&(l.$set=null==i?void 0:i.$set);var c=this.xs(null==i?void 0:i.$set_once);c&&(l.$set_once=c),(u=l,h=null!=i&&i._noTruncate?null:this.config.properties_string_max_length,d=e=>C(e)&&!A(h)?e.slice(0,h):e,p=new Set,l=function e(t,i){var s;return t!==Object(t)?d?d(t,i):t:p.has(t)?void 0:(p.add(t),F(t)?(s=[],X(t,t=>{s.push(e(t))})):(s={},J(t,(t,i)=>{p.has(t)||(s[i]=e(t,i))})),s)}(u)).timestamp=o,T(null==i?void 0:i.timestamp)||(l.properties.$event_time_override_provided=!0,l.properties.$event_time_override_system_time=n);var u,h,d,p,g,_,v=G({},l.properties.$set,l.$set);if(M(v)||this.setPersonPropertiesForFlags(v),e===ne.DISMISSED||e===ne.SENT){var f=null==t?void 0:t[nt.SURVEY_ID],m=null==t?void 0:t[nt.SURVEY_ITERATION];localStorage.setItem((_=""+r4+(g={id:f,current_iteration:m}).id,g.current_iteration&&g.current_iteration>0&&(_=""+r4+g.id+"_"+g.current_iteration),_),"true"),l.$set=G({},l.$set,{[r8({id:f,current_iteration:m},e===ne.SENT?"responded":"dismissed")]:!0})}if(!L(this.config.before_send)){var y=this.Es(l);if(!y)return;l=y}this.us.emit("eventCaptured",l);var b={method:"POST",url:null!==(s=null==i?void 0:i._url)&&void 0!==s?s:this.requestRouter.endpointFor("api",this.analyticsDefaultEndpoint),data:l,compression:"best-available",batchKey:null==i?void 0:i._batchKey};return!this.config.request_batching||i&&(null==i||!i._batchKey)||null!=i&&i.send_instantly?this._s(b):this.ps.enqueue(b),l}j.critical("This capture call is ignored due to client rate limiting.")}}else j.error("No event name provided to posthog.capture")}else j.uninitializedWarning("posthog.capture")}Ne(e){return this.on("eventCaptured",t=>e(t.event,t))}calculateEventProperties(e,t,i,r,n){if(i=i||new Date,!this.persistence||!this.sessionPersistence)return t;var o,a=n?void 0:this.persistence.remove_event_timer(e),l=G({},t);if(l.token=this.config.token,l.$config_defaults=this.config.defaults,this.config.__preview_experimental_cookieless_mode&&(l.$cookieless_mode=!0),"$snapshot"===e){var h=G({},this.persistence.properties(),this.sessionPersistence.properties());return l.distinct_id=h.distinct_id,(!C(l.distinct_id)&&!D(l.distinct_id)||O(l.distinct_id))&&j.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"),l}var d,p=function(e,t){if(!g)return{};var i,r,n=e?K([],rO,t||[]):[],[o,a]=function(e){for(var t=0;t<rM.length;t++){var[i,s]=rM[t],r=i.exec(e),n=r&&(R(s)?s(r,e):s);if(n)return n}return["",""]}(g);return Z(ei({$os:o,$os_version:a,$browser:rF(g,navigator.vendor),$device:rT(g),$device_type:(r=rT(g))===sK||r===sZ||"Kobo"===r||"Kindle Fire"===r||r===rm?sJ:r===ra||r===rc||r===rl||r===r_?"Console":r===s0?"Wearable":r?sY:"Desktop",$timezone:rz(),$timezone_offset:function(){try{return(new Date).getTimezoneOffset()}catch(e){return}}()}),{$current_url:th(null==u?void 0:u.href,n,rL),$host:null==u?void 0:u.host,$pathname:null==u?void 0:u.pathname,$raw_user_agent:g.length>1e3?g.substring(0,997)+"...":g,$browser_version:rP(g,navigator.vendor),$browser_language:rB(),$browser_language_prefix:"string"==typeof(i=rB())?i.split("-")[0]:void 0,$screen_height:null==s?void 0:s.screen.height,$screen_width:null==s?void 0:s.screen.width,$viewport_height:null==s?void 0:s.innerHeight,$viewport_width:null==s?void 0:s.innerWidth,$lib:"web",$lib_version:v.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})}(this.config.mask_personal_data_properties,this.config.custom_personal_data_properties);if(this.sessionManager){var{sessionId:_,windowId:f}=this.sessionManager.checkAndGetSessionAndWindowId(n,i.getTime());l.$session_id=_,l.$window_id=f}this.sessionPropsManager&&Z(l,this.sessionPropsManager.getSessionProps());try{this.sessionRecording&&Z(l,this.sessionRecording.sdkDebugProperties),l.$sdk_debug_retry_queue_size=null==(o=this.gs)?void 0:o.length}catch(e){l.$sdk_debug_error_capturing_properties=String(e)}if(this.requestRouter.region===nb.CUSTOM&&(l.$lib_custom_api_host=this.config.api_host),d="$pageview"!==e||n?"$pageleave"!==e||n?this.pageViewManager.doEvent():this.pageViewManager.doPageLeave(i):this.pageViewManager.doPageView(i,r),l=Z(l,d),"$pageview"===e&&c&&(l.title=c.title),!T(a)){var m=i.getTime()-a;l.$duration=parseFloat((m/1e3).toFixed(3))}g&&this.config.opt_out_useragent_filter&&(l.$browser_type=this._is_bot()?"bot":"browser"),(l=Z({},p,this.persistence.properties(),this.sessionPersistence.properties(),l)).$is_identified=this._isIdentified(),F(this.config.property_denylist)?J(this.config.property_denylist,function(e){delete l[e]}):j.error("Invalid value for property_denylist config: "+this.config.property_denylist+" or property_blacklist config: "+this.config.property_blacklist);var y=this.config.sanitize_properties;y&&(j.error("sanitize_properties is deprecated. Use before_send instead"),l=y(l,e));var b=this.Is();return l.$process_person_profile=b,b&&!n&&this.Ps("_calculate_event_properties"),l}xs(e){if(!this.persistence||!this.Is()||this.ls)return e;var t,i=Z({},this.persistence.get_initial_props(),(null==(t=this.sessionPropsManager)?void 0:t.getSetOnceProps())||{},e||{}),s=this.config.sanitize_properties;return s&&(j.error("sanitize_properties is deprecated. Use before_send instead"),i=s(i,"$set_once")),this.ls=!0,M(i)?void 0:i}register(e,t){var i;null==(i=this.persistence)||i.register(e,t)}register_once(e,t,i){var s;null==(s=this.persistence)||s.register_once(e,t,i)}register_for_session(e){var t;null==(t=this.sessionPersistence)||t.register(e)}unregister(e){var t;null==(t=this.persistence)||t.unregister(e)}unregister_for_session(e){var t;null==(t=this.sessionPersistence)||t.unregister(e)}Rs(e,t){this.register({[e]:t})}getFeatureFlag(e,t){return this.featureFlags.getFeatureFlag(e,t)}getFeatureFlagPayload(e){var t=this.featureFlags.getFeatureFlagPayload(e);try{return JSON.parse(t)}catch(e){return t}}isFeatureEnabled(e,t){return this.featureFlags.isFeatureEnabled(e,t)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(e,t){this.featureFlags.updateEarlyAccessFeatureEnrollment(e,t)}getEarlyAccessFeatures(e,t,i){return void 0===t&&(t=!1),this.featureFlags.getEarlyAccessFeatures(e,t,i)}on(e,t){return this.us.on(e,t)}onFeatureFlags(e){return this.featureFlags.onFeatureFlags(e)}onSurveysLoaded(e){return this.surveys.onSurveysLoaded(e)}onSessionId(e){var t,i;return null!==(t=null==(i=this.sessionManager)?void 0:i.onSessionId(e))&&void 0!==t?t:()=>{}}getSurveys(e,t){void 0===t&&(t=!1),this.surveys.getSurveys(e,t)}getActiveMatchingSurveys(e,t){void 0===t&&(t=!1),this.surveys.getActiveMatchingSurveys(e,t)}renderSurvey(e,t){this.surveys.renderSurvey(e,t)}canRenderSurvey(e){return this.surveys.canRenderSurvey(e)}canRenderSurveyAsync(e,t){return void 0===t&&(t=!1),this.surveys.canRenderSurveyAsync(e,t)}identify(e,t,i){if(!this.__loaded||!this.persistence)return j.uninitializedWarning("posthog.identify");if(D(e)&&(e=e.toString(),j.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")),e){if(["distinct_id","distinctid"].includes(e.toLowerCase()))j.critical('The string "'+e+'" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.');else if(this.Ps("posthog.identify")){var s=this.get_distinct_id();this.register({$user_id:e}),this.get_property("$device_id")||this.register_once({$had_persisted_distinct_id:!0,$device_id:s},""),e!==s&&e!==this.get_property(ea)&&(this.unregister(ea),this.register({distinct_id:e}));var r="anonymous"===(this.persistence.get_property(eL)||"anonymous");e!==s&&r?(this.persistence.set_property(eL,"identified"),this.setPersonPropertiesForFlags(G({},i||{},t||{}),!1),this.capture("$identify",{distinct_id:e,$anon_distinct_id:s},{$set:t||{},$set_once:i||{}}),this.cs=sU(e,t,i),this.featureFlags.setAnonymousDistinctId(s)):(t||i)&&this.setPersonProperties(t,i),e!==s&&(this.reloadFeatureFlags(),this.unregister(eA))}}else j.error("Unique user id has not been set in posthog.identify")}setPersonProperties(e,t){if((e||t)&&this.Ps("posthog.setPersonProperties")){var i=sU(this.get_distinct_id(),e,t);this.cs!==i?(this.setPersonPropertiesForFlags(G({},t||{},e||{})),this.capture("$set",{$set:e||{},$set_once:t||{}}),this.cs=i):j.info("A duplicate setPersonProperties call was made with the same properties. It has been ignored.")}}group(e,t,i){if(e&&t){if(this.Ps("posthog.group")){var s=this.getGroups();s[e]!==t&&this.resetGroupPropertiesForFlags(e),this.register({$groups:G({},s,{[e]:t})}),i&&(this.capture("$groupidentify",{$group_type:e,$group_key:t,$group_set:i}),this.setGroupPropertiesForFlags({[e]:i})),s[e]===t||i||this.reloadFeatureFlags()}}else j.error("posthog.group requires a group type and group key")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(e,t){void 0===t&&(t=!0),this.featureFlags.setPersonPropertiesForFlags(e,t)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(e,t){void 0===t&&(t=!0),this.Ps("posthog.setGroupPropertiesForFlags")&&this.featureFlags.setGroupPropertiesForFlags(e,t)}resetGroupPropertiesForFlags(e){this.featureFlags.resetGroupPropertiesForFlags(e)}reset(e){if(j.info("reset"),!this.__loaded)return j.uninitializedWarning("posthog.reset");var t,i,s,r,n=this.get_property("$device_id");if(this.consent.reset(),null==(t=this.persistence)||t.clear(),null==(i=this.sessionPersistence)||i.clear(),this.surveys.reset(),null==(s=this.persistence)||s.set_property(eL,"anonymous"),null==(r=this.sessionManager)||r.resetSessionId(),this.cs=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:ez,$device_id:null},"");else{var o=this.config.get_device_id(tE());this.register_once({distinct_id:o,$device_id:e?o:n},"")}this.register({$last_posthog_reset:(new Date).toISOString()},1)}get_distinct_id(){return this.get_property("distinct_id")}getGroups(){return this.get_property("$groups")||{}}get_session_id(){var e,t;return null!==(e=null==(t=this.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0).sessionId)&&void 0!==e?e:""}get_session_replay_url(e){if(!this.sessionManager)return"";var{sessionId:t,sessionStartTimestamp:i}=this.sessionManager.checkAndGetSessionAndWindowId(!0),s=this.requestRouter.endpointFor("ui","/project/"+this.config.token+"/replay/"+t);if(null!=e&&e.withTimestamp&&i){var r,n=null!==(r=e.timestampLookBack)&&void 0!==r?r:10;if(!i)return s;s+="?t="+Math.max(Math.floor(((new Date).getTime()-i)/1e3)-n,0)}return s}alias(e,t){return e===this.get_property(eo)?(j.critical("Attempting to create alias for existing People user - aborting."),-2):this.Ps("posthog.alias")?(T(t)&&(t=this.get_distinct_id()),e!==t?(this.Rs(ea,e),this.capture("$create_alias",{alias:e,distinct_id:t})):(j.warn("alias matches current distinct_id - skipping api call."),this.identify(e),-1)):void 0}set_config(e){var t,i,s,r,n=G({},this.config);P(e)&&(Z(this.config,nP(e)),null==(t=this.persistence)||t.update_config(this.config,n),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new r2(G({},this.config,{persistence:"sessionStorage"})),tF.O()&&"true"===tF.A("ph_debug")&&(this.config.debug=!0),this.config.debug&&(v.DEBUG=!0,j.info("set_config",JSON.stringify({config:e,oldConfig:n,newConfig:G({},this.config)},null,2))),null==(i=this.sessionRecording)||i.startIfEnabledOrStop(),null==(s=this.autocapture)||s.startIfEnabled(),null==(r=this.heatmaps)||r.startIfEnabled(),this.surveys.loadIfEnabled(),this.bs())}startSessionRecording(e){var t,i,s,r,n,o=!0===e,a={sampling:o||!(null==e||!e.sampling),linked_flag:o||!(null==e||!e.linked_flag),url_trigger:o||!(null==e||!e.url_trigger),event_trigger:o||!(null==e||!e.event_trigger)};Object.values(a).some(Boolean)&&(null==(t=this.sessionManager)||t.checkAndGetSessionAndWindowId(),a.sampling&&(null==(i=this.sessionRecording)||i.overrideSampling()),a.linked_flag&&(null==(s=this.sessionRecording)||s.overrideLinkedFlag()),a.url_trigger&&(null==(r=this.sessionRecording)||r.overrideTrigger("url")),a.event_trigger&&(null==(n=this.sessionRecording)||n.overrideTrigger("event"))),this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var e;return!(null==(e=this.sessionRecording)||!e.started)}captureException(e,t){var i=Error("PostHog syntheticException");this.exceptions.sendExceptionEvent(G({},function(e,t){var{error:i,event:s}=e,r={$exception_list:[]},n=i||s;if(tV(n)||tG(n,"DOMException")){if("stack"in n)r=t9(n,t);else{var o=n.name||(tV(n)?"DOMError":"DOMException"),a=n.message?o+": "+n.message:o;r=ie(a,G({},t,{overrideExceptionType:tV(n)?"DOMError":"DOMException",defaultExceptionMessage:a}))}return"code"in n&&(r.$exception_DOMException_code=""+n.code),r}if(tG(n,"ErrorEvent")&&n.error)return t9(n.error,t);if(tW(n))return t9(n,t);if(tG(n,"Object")||tU(n))return function(e,t){var i,s,r,n=null===(s=null==t?void 0:t.handled)||void 0===s||s,o=null===(r=null==t?void 0:t.synthetic)||void 0===r||r,a={type:null!=t&&t.overrideExceptionType?t.overrideExceptionType:tU(e)?e.constructor.name:"Error",value:"Non-Error 'exception' captured with keys: "+function(e,t){void 0===t&&(t=40);var i=Object.keys(e);if(i.sort(),!i.length)return"[object has no keys]";for(var s=i.length;s>0;s--){var r=i.slice(0,s).join(", ");if(!(r.length>t))return s===i.length||r.length<=t?r:r.slice(0,t)+"..."}return""}(e),mechanism:{handled:n,synthetic:o}};if(null!=t&&t.syntheticException){var l=t8(null==t?void 0:t.syntheticException,1);l.length&&(a.stacktrace={frames:l,type:"raw"})}return{$exception_list:[a],$exception_level:C(i=e.level)&&!O(i)&&b.indexOf(i)>=0?e.level:"error"}}(n,t);if(T(i)&&C(s)){var l="Error",c=s,u=s.match(t4);return u&&(l=u[1],c=u[2]),ie(c,G({},t,{overrideExceptionType:l,defaultExceptionMessage:c}))}return ie(n,t)}(e instanceof Error?{error:e,event:e.message}:{event:e},{syntheticException:i}),t))}loadToolbar(e){return this.toolbar.loadToolbar(e)}get_property(e){var t;return null==(t=this.persistence)?void 0:t.props[e]}getSessionProperty(e){var t;return null==(t=this.sessionPersistence)?void 0:t.props[e]}toString(){var e,t=null!==(e=this.config.name)&&void 0!==e?e:n$;return t!==n$&&(t=n$+"."+t),t}_isIdentified(){var e,t;return"identified"===(null==(e=this.persistence)?void 0:e.get_property(eL))||"identified"===(null==(t=this.sessionPersistence)?void 0:t.get_property(eL))}Is(){var e,t;return!("never"===this.config.person_profiles||"identified_only"===this.config.person_profiles&&!this._isIdentified()&&M(this.getGroups())&&(null==(e=this.persistence)||null==(e=e.props)||!e[ea])&&(null==(t=this.persistence)||null==(t=t.props)||!t[ej]))}ks(){return!0===this.config.capture_pageleave||"if_capture_pageview"===this.config.capture_pageleave&&(!0===this.config.capture_pageview||"history_change"===this.config.capture_pageview)}createPersonProfile(){this.Is()||this.Ps("posthog.createPersonProfile")&&this.setPersonProperties({},{})}Ps(e){return"never"===this.config.person_profiles?(j.error(e+' was called, but process_person is set to "never". This call will be ignored.'),!1):(this.Rs(ej,!0),!0)}bs(){var e,t,i,s,r=this.consent.isOptedOut(),n=this.config.opt_out_persistence_by_default,o=this.config.disable_persistence||r&&!!n;(null==(e=this.persistence)?void 0:e.xe)!==o&&(null==(i=this.persistence)||i.set_disabled(o)),(null==(t=this.sessionPersistence)?void 0:t.xe)!==o&&(null==(s=this.sessionPersistence)||s.set_disabled(o))}opt_in_capturing(e){var t;this.consent.optInOut(!0),this.bs(),(T(null==e?void 0:e.captureEventName)||null!=e&&e.captureEventName)&&this.capture(null!==(t=null==e?void 0:e.captureEventName)&&void 0!==t?t:"$opt_in",null==e?void 0:e.captureProperties,{send_instantly:!0}),this.config.capture_pageview&&this.$s()}opt_out_capturing(){this.consent.optInOut(!1),this.bs()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this.bs()}_is_bot(){return l?ny(l,this.config.custom_blocked_useragents):void 0}$s(){c&&("visible"===c.visibilityState?this.hs||(this.hs=!0,this.capture("$pageview",{title:c.title},{send_instantly:!0}),this.ds&&(c.removeEventListener("visibilitychange",this.ds),this.ds=null)):this.ds||(this.ds=this.$s.bind(this),en(c,"visibilitychange",this.ds)))}debug(e){!1===e?(null==s||s.console.log("You've disabled debug mode."),localStorage&&localStorage.removeItem("ph_debug"),this.set_config({debug:!1})):(null==s||s.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."),localStorage&&localStorage.setItem("ph_debug","true"),this.set_config({debug:!0}))}Es(e){if(L(this.config.before_send))return e;var t=F(this.config.before_send)?this.config.before_send:[this.config.before_send],i=e;for(var s of t){if(L(i=s(i))){var r="Event '"+e.event+"' was rejected in beforeSend function";return B(e.event)?j.warn(r+". This can cause unexpected behavior."):j.info(r),null}i.properties&&!M(i.properties)||j.warn("Event '"+e.event+"' has no properties after beforeSend function, this is likely an error.")}return i}getPageViewId(){var e;return null==(e=this.pageViewManager.ne)?void 0:e.pageViewId}captureTraceFeedback(e,t){this.capture("$ai_feedback",{$ai_trace_id:String(e),$ai_feedback_text:t})}captureTraceMetric(e,t,i){this.capture("$ai_metric",{$ai_trace_id:String(e),$ai_metric_name:t,$ai_metric_value:String(i)})}}!function(e,t){for(var i=0;i<t.length;i++)e.prototype[t[i]]=et(e.prototype[t[i]])}(nT,["identify"]);var nC,nO=(nC=nx[n$]=new nT,function(){function e(){e.done||(e.done=!0,nF=!1,J(nx,function(e){e._dom_loaded()}))}null!=c&&c.addEventListener?"complete"===c.readyState?e():en(c,"DOMContentLoaded",e,{capture:!1}):s&&j.error("Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized")}(),nC)}}]);