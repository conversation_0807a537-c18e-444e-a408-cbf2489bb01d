"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{7615:(e,t,o)=>{o.r(t),o.d(t,{FAQSection:()=>d});var a=o(47093),s=o(34545),n=o(38163),r=o(23558),i=o(18322);let l=[{question:"What exactly is Roo Code?",answer:"Roo Code is an open-source, AI-powered coding assistant that runs in VS Code. It goes beyond simple autocompletion by reading and writing across multiple files, executing commands, and adapting to your workflow—like having a whole dev team right inside your editor."},{question:"How does Roo Code differ from Copilot, Cursor, or Windsurf?",answer:"Open & Customizable: Roo Code is open-source and allows you to integrate any AI model (OpenAI, Anthropic, local LLMs, etc.). Multi-File Edits: It can read, refactor, and update multiple files at once for more holistic changes. Agentic Abilities: Roo Code can run tests, open a browser, or do deeper tasks than a typical AI autocomplete. Permission-Based: You control and approve any file changes or command executions."},{question:"Is Roo Code really free?",answer:"Yes! Roo Code is completely free and open-source. You'll only pay for the AI model usage if you use a paid API (like OpenAI). If you choose free or self-hosted models, there's no cost at all."},{question:"Will my code stay private?",answer:"Yes. Because Roo Code is an extension in your local VS Code, your code never leaves your machine unless you connect to an external AI API. Even then, you control exactly what is sent to the AI model. You can use tools like .rooignore to exclude sensitive files, and you can also run Roo Code with offline/local models for full privacy."},{question:"Which AI models does Roo Code support?",answer:"Roo Code is model-agnostic. It works with: OpenAI models (GPT-3.5, GPT-4, etc.), Anthropic Claude, Local LLMs (through APIs or special plugins), Any other API that follows Roo Code's Model Context Protocol (MCP)."},{question:"Does Roo Code support my programming language?",answer:"Likely yes! Roo Code supports a wide range of languages—Python, Java, C#, JavaScript/TypeScript, Go, Rust, etc. Since it leverages the AI model's understanding, new or lesser-known languages may also work, depending on model support."},{question:"How do I install and get started?",answer:"Install Roo Code from the VS Code Marketplace (or GitHub). Add your AI keys (OpenAI, Anthropic, or other) in the extension settings. Open the Roo panel (the rocket icon) in VS Code, and start typing commands in plain English!"},{question:"Can it handle large, enterprise-scale projects?",answer:"Absolutely. Roo Code uses efficient strategies (like partial-file analysis, summarization, or user-specified context) to handle large codebases. Enterprises especially appreciate the on-prem or self-hosted model option for compliance and security needs."},{question:"Is it safe for enterprise use?",answer:"Yes. Roo Code was designed with enterprise in mind: Self-host AI models or choose your own provider. Permission gating on file writes and commands. Auditable: The entire code is open-source, so you know exactly how it operates."},{question:"Can Roo Code run commands and tests automatically?",answer:"Yes! One of Roo Code's superpowers is command execution (optional and fully permission-based). It can: Run npm install or any terminal command you grant permission for. Execute your test suites. Open a web browser for integration tests."},{question:"What if I just want a casual coding 'vibe'?",answer:'Roo Code shines for both serious enterprise development and casual "vibe coding." You can ask it to quickly prototype ideas, refactor on the fly, or provide design suggestions—without a rigid, step-by-step process.'},{question:"Can I contribute to Roo Code?",answer:"Yes, please do! Roo Code is open-source on GitHub. Submit issues, suggest features, or open a pull request. There's also an active community on Discord and Reddit if you want to share feedback or help others."},{question:"Where can I learn more or get help?",answer:"Check out: Official Documentation for setup and advanced guides. Discord & Reddit channels for community support. YouTube tutorials and blog posts from fellow developers showcasing real-world usage."}];function d(){let[e,t]=(0,s.useState)(null),o=o=>{t(e===o?null:o)};return(0,a.jsx)("section",{id:"faq-section",className:"border-t border-border py-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"mx-auto mb-24 max-w-3xl text-center",children:(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,ease:[.21,.45,.27,.9]},children:[(0,a.jsx)("h2",{className:"bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl",children:"Frequently Asked Questions"}),(0,a.jsx)("p",{className:"mt-6 text-lg text-muted-foreground",children:"Everything you need to know about Roo Code and how it can transform your development workflow."})]})}),(0,a.jsx)("div",{className:"mx-auto max-w-3xl",children:(0,a.jsx)("div",{className:"space-y-4",children:l.map((t,s)=>(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.5,delay:.1*s,ease:[.21,.45,.27,.9]},children:(0,a.jsxs)("div",{className:"group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border",children:[(0,a.jsxs)("button",{onClick:()=>o(s),className:"flex w-full items-center justify-between p-6 text-left","aria-expanded":e===s,children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-foreground/90",children:t.question}),(0,a.jsx)(r.A,{className:(0,i.cn)("h-5 w-5 text-muted-foreground transition-transform duration-200",e===s?"rotate-180":"")})]}),(0,a.jsx)("div",{className:(0,i.cn)("overflow-hidden transition-all duration-300 ease-in-out",e===s?"max-h-96 pb-6":"max-h-0"),children:(0,a.jsx)("div",{className:"px-6 text-muted-foreground",children:(0,a.jsx)("p",{children:t.answer})})})]})},s))})})]})})}},21470:(e,t,o)=>{o.r(t),o.d(t,{WhatsNewButton:()=>g});var a=o(47093),s=o(34545),n=o(39250),r=o(38163),i=o(63774),l=o(7676),d=o(59981),c=o(75173),u=o(83932),m=o(4214),h=o.n(m);function p(e){let{icon:t,color:o,title:s,description:n}=e;return(0,a.jsxs)("div",{className:"space-y-1.5 sm:space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 space-x-2",children:[(0,a.jsx)("div",{className:"rounded-full ".concat({blue:"bg-blue-500/20",purple:"bg-purple-500/20",green:"bg-green-500/20"}[o]," p-3 ").concat({blue:"text-blue-400",purple:"text-purple-400",green:"text-green-400"}[o]),children:(0,a.jsx)(t,{className:"h-6 w-6"})}),(0,a.jsx)("h3",{className:"text-base font-semibold sm:text-lg",children:s})]}),(0,a.jsx)("p",{className:"text-sm text-gray-400 sm:text-base",children:n})]})}let x="v3.8.0";function g(){let[e,t]=(0,s.useState)(!1),o=(0,s.useRef)(null),m=(0,s.useRef)(null);return(0,s.useEffect)(()=>{let e,t=m.current,a=o.current;if(!t||!a)return;let s=t.getContext("2d");if(!s)return;let n=()=>{let e=a.getBoundingClientRect();t.width=e.width+8,t.height=e.height+8,t.style.width="".concat(t.width,"px"),t.style.height="".concat(t.height,"px")};n(),window.addEventListener("resize",n);let r=0,i=()=>{if(!s||!t)return;s.clearRect(0,0,t.width,t.height);let o=t.width-4,a=t.height-4,n=a/2;s.beginPath(),s.moveTo(2+n,2),s.lineTo(2+o-n,2),s.arcTo(2+o,2,2+o,2+n,n),s.lineTo(2+o,2+a-n),s.arcTo(2+o,2+a,2+o-n,2+a,n),s.lineTo(2+n,2+a),s.arcTo(2,2+a,2,2+a-n,n),s.lineTo(2,2+n),s.arcTo(2,2,2+n,2,n),s.closePath(),r=(r+.016)%(2*Math.PI);let l=t.width/2,d=t.height/2,c="70, 130, 255",u=s.createConicGradient(r,l,d);u.addColorStop(0,"rgba(".concat(c,", 0)")),u.addColorStop(.2,"rgba(".concat(c,", 0.8)")),u.addColorStop(.4,"rgba(".concat(c,", 0)")),u.addColorStop(1,"rgba(".concat(c,", 0)")),s.strokeStyle=u,s.lineWidth=1.5,s.stroke(),s.shadowColor="rgba(".concat(c,", 0.6)"),s.shadowBlur=5,s.strokeStyle="rgba(".concat(c,", 0.3)"),s.lineWidth=.5,s.stroke(),e=requestAnimationFrame(i)};return i(),()=>{window.removeEventListener("resize",n),e&&cancelAnimationFrame(e)}},[]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"relative inline-flex",ref:o,children:[(0,a.jsx)("canvas",{ref:m,className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2",style:{pointerEvents:"none"}}),(0,a.jsxs)(h(),{href:"#",onClick:e=>{e.preventDefault(),t(!0)},className:"relative z-10 flex items-center space-x-2 rounded-full bg-black px-4 py-2 text-sm font-medium text-white transition-all hover:bg-gray-900",children:[(0,a.jsxs)("span",{children:["See what's new in ",x]}),(0,a.jsx)(i.A,{className:"h-3.5 w-3.5"})]})]}),(0,a.jsx)(n.N,{children:e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.P.div,{className:"fixed inset-0 z-40 bg-black/80 backdrop-blur-sm",initial:{opacity:0,backdropFilter:"blur(0px)"},animate:{opacity:1,backdropFilter:"blur(8px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},transition:{duration:.2}}),(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",onClick:()=>t(!1),children:(0,a.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,a.jsxs)(r.P.div,{className:"relative w-full max-w-2xl rounded-lg border border-gray-800 bg-black p-6 sm:p-8",initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:20,scale:.95},transition:{type:"spring",damping:20,stiffness:400,mass:.6,duration:.25},onClick:e=>{e.stopPropagation()},children:[(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold sm:text-2xl",children:["What's New in Roo Code ",x]}),(0,a.jsx)("button",{onClick:()=>t(!1),className:"flex-shrink-0 rounded-full p-1.5 text-gray-400 hover:bg-gray-800 hover:text-white",children:(0,a.jsx)(l.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"mt-4 space-y-4 sm:mt-6 sm:space-y-6",children:[(0,a.jsx)(p,{icon:d.A,color:"blue",title:"AI-Powered Code Generation",description:"Generate high-quality code snippets and entire components with our new AI assistant. Trained on millions of code repositories to understand your project context."}),(0,a.jsx)(p,{icon:c.A,color:"purple",title:"Real-time Collaboration",description:"Work together with your team in real-time with our new collaborative editing features. See changes as they happen and resolve conflicts automatically."}),(0,a.jsx)(p,{icon:u.A,color:"green",title:"Performance Optimizations",description:"We've completely rewritten our core engine for blazing fast performance. Experience up to 10x faster build times and smoother development workflow."})]})]})})})]})})]})}},21925:(e,t,o)=>{o.d(t,{AnimatedText:()=>n});var a=o(47093),s=o(38163);function n(e){let{children:t,className:o}=e;return(0,a.jsx)(s.P.span,{className:o,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,ease:[.2,.65,.3,.9]},children:t})}},33001:(e,t,o)=>{o.r(t),o.d(t,{AnimatedBackground:()=>n});var a=o(47093),s=o(34545);function n(){let e=(0,s.useRef)(null);return(0,s.useEffect)(()=>{let t,o=e.current;if(!o)return;let a=o.getContext("2d");if(!a)return;let s=[{x:.2*o.width,y:.3*o.height,radius:.4*o.width,color:"rgba(0, 100, 255, 0.15)"},{x:.8*o.width,y:.7*o.height,radius:.5*o.width,color:"rgba(100, 0, 255, 0.1)"}],n=[],r=Math.min(50,Math.floor(window.innerWidth/40)),i=()=>{o.width=window.innerWidth,o.height=window.innerHeight,s=[{x:.2*o.width,y:.3*o.height,radius:.4*o.width,color:"rgba(0, 100, 255, 0.15)"},{x:.8*o.width,y:.7*o.height,radius:.5*o.width,color:"rgba(100, 0, 255, 0.1)"}],l()};function l(){if(!a)throw Error("Context is null (not initialized?)");if(!o)throw Error("Canvas is null (not initialized?)");a.clearRect(0,0,o.width,o.height),s.forEach(e=>{let t=a.createRadialGradient(e.x,e.y,0,e.x,e.y,e.radius);t.addColorStop(0,e.color),t.addColorStop(1,"rgba(0, 0, 0, 0)"),a.fillStyle=t,a.fillRect(0,0,o.width,o.height)}),a.strokeStyle="rgba(50, 50, 70, ".concat(.15,")"),a.lineWidth=.5;let e=.7*o.height,t=.5*o.width;for(let s=0;s<=o.width;s+=50){let n=s/o.width-.5;a.beginPath(),a.moveTo(s,0);let r=e-50*Math.abs(n);a.quadraticCurveTo(s+(t-s)*.3,r,t+(s-t)*.2,e),a.stroke()}for(let o=0;o<=e;o+=50){let s=50*(1+o/e*5);a.beginPath(),a.moveTo(t-s,o),a.lineTo(t+s,o),a.stroke()}n.forEach(e=>{e.update(),e.draw()}),function(){if(!a)throw Error("Context is null (not initialized?)");for(let e=0;e<n.length;e++)for(let t=e;t<n.length;t++){let o=n[e].x-n[t].x,s=n[e].y-n[t].y,r=Math.sqrt(o*o+s*s);if(r<150){let o=(1-r/150)*.5;a.strokeStyle="rgba(100, 150, 255, ".concat(o,")"),a.lineWidth=.5,a.beginPath(),a.moveTo(n[e].x,n[e].y),a.lineTo(n[t].x,n[t].y),a.stroke()}}}()}i(),window.addEventListener("resize",i);class d{update(){if(!o)throw Error("Canvas is null (not initialized?)");this.x+=this.speedX,this.y+=this.speedY,this.x>o.width?this.x=0:this.x<0&&(this.x=o.width),this.y>.7*o.height?this.y=0:this.y<0&&(this.y=.7*o.height),this.opacity+=.01*Math.sin(.001*Date.now()),this.opacity=Math.max(.1,Math.min(.7,this.opacity))}draw(){if(!a)throw Error("Context is null (not initialized?)");a.fillStyle="".concat(this.color).concat(this.opacity,")"),a.beginPath(),a.arc(this.x,this.y,this.size,0,2*Math.PI),a.fill()}constructor(){if(!o)throw Error("Canvas is null (not initialized?)");this.x=Math.random()*o.width,this.y=Math.random()*(.7*o.height),this.size=2*Math.random()+1,this.speedX=(Math.random()-.5)*.8,this.speedY=(Math.random()-.5)*.8,this.color="rgba(100, 150, 255, ",this.opacity=.5*Math.random()+.2}}for(let e=0;e<r;e++)n.push(new d);let c=.2*o.width,u=.3*o.height,m=e=>{c=e.clientX,u=e.clientY};return function e(){if(t=requestAnimationFrame(e),!o)throw Error("Canvas is null (not initialized?)");let a=c-s[0].x,n=u-s[0].y;s[0].x+=.05*a,s[0].y+=.05*n;let r=Math.sqrt(a*a+n*n);s[0].radius=Math.max(.2*o.width,Math.min(.4*o.width,.3*o.width+.1*r)),l()}(),window.addEventListener("mousemove",m),()=>{window.removeEventListener("resize",i),window.removeEventListener("mousemove",m),cancelAnimationFrame(t)}},[]),(0,a.jsx)("canvas",{ref:e,className:"absolute inset-0 h-full w-full",style:{zIndex:0}})}},48503:(e,t,o)=>{o.r(t),o.d(t,{CompanyLogos:()=>n});var a=o(47093),s=o(38163);function n(){return(0,a.jsx)("div",{className:"mt-10",children:(0,a.jsx)("div",{className:"mx-auto grid max-w-5xl grid-cols-2 gap-8 py-8 md:grid-cols-3 lg:grid-cols-6",children:[{name:"Company 1",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 2",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 3",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 4",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 5",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 6",logo:"/placeholder.svg?height=40&width=120"}].map((e,t)=>(0,a.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*t,ease:"easeOut"},className:"flex items-center justify-center",children:(0,a.jsx)("img",{src:e.logo||"/placeholder.svg",alt:e.name,className:"h-10 w-auto opacity-70 grayscale transition-all duration-300 hover:opacity-100 hover:grayscale-0"})},t))})})}},52032:(e,t,o)=>{o.r(t),o.d(t,{Testimonials:()=>u,testimonials:()=>c});var a=o(47093),s=o(34545),n=o(38163),r=o(54554),i=o(75227),l=o(8087);function d(){let[e]=(0,i.A)({loop:!0},[(0,l.A)({playOnInit:!0,speed:1,stopOnInteraction:!0,stopOnMouseEnter:!0})]);return(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("div",{className:"overflow-hidden px-4",ref:e,children:(0,a.jsx)("div",{className:"flex",children:c.map(e=>(0,a.jsx)("div",{className:"min-w-0 flex-[0_0_100%] px-4",children:(0,a.jsxs)("div",{className:"relative py-8",children:[(0,a.jsx)("svg",{className:"absolute left-0 top-0 h-8 w-8 text-blue-500/30",fill:"currentColor",viewBox:"0 0 32 32",children:(0,a.jsx)("path",{d:"M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z"})}),(0,a.jsxs)("blockquote",{className:"mt-8",children:[(0,a.jsxs)("p",{className:"text-lg font-light italic leading-relaxed text-muted-foreground",children:['"',e.quote,'"']}),(0,a.jsxs)("footer",{className:"mt-6",children:[(0,a.jsx)("div",{className:"h-px w-12 bg-gradient-to-r from-blue-500/30 to-transparent"}),(0,a.jsx)("p",{className:"mt-4 font-medium text-foreground/90",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.role," at ",e.company]})]})]})]})},e.id))})})})}let c=[{id:1,name:"Luca",role:"Reviewer",company:"VS Code Marketplace",quote:"Roo Code is an absolute game-changer! \uD83D\uDE80 It makes coding faster, easier, and more intuitive with its smart AI-powered suggestions, real-time debugging, and automation features. The seamless integration with VS Code is a huge plus, and the constant updates ensure it keeps getting better"},{id:2,name:"Taro Woollett-Chiba",role:"AI Product Lead",company:"Vendidit",quote:"Easily the best AI code editor. Roo Code has the best features and capabilities, along with the best development team. I swear, they're the fastest to support new models and implement useful functionality whenever users mention it... simply amazing."},{id:3,name:"Can Nuri",role:"Reviewer",company:"VS Code Marketplace",quote:"Roo Code is one of the most inspiring projects I have seen for a long time. It shapes the way I think and deal with software development."},{id:4,name:"Michael",role:"Reviewer",company:"VS Code Marketplace",quote:"I switched from Windsurf to Roo Code in January and honestly, it's been a huge upgrade. Windsurf kept making mistakes and being dumb when I ask it for things. Roo just gets it. Projects that used to take a full day now wrap up before lunch. "}];function u(){let e=(0,s.useRef)(null),t={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:[.21,.45,.27,.9]}}};return(0,a.jsxs)("section",{ref:e,className:"relative overflow-hidden border-t border-border py-32",children:[(0,a.jsx)(n.P.div,{className:"absolute inset-0",initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1.2,ease:"easeOut"}}},children:(0,a.jsx)("div",{className:"absolute inset-y-0 left-1/2 h-full w-full max-w-[1200px] -translate-x-1/2",children:(0,a.jsx)("div",{className:"absolute left-1/2 top-1/2 h-[800px] w-full -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"})})}),(0,a.jsxs)("div",{className:"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"mx-auto mb-24 max-w-3xl text-center",children:(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,ease:[.21,.45,.27,.9]},children:[(0,a.jsx)("h2",{className:"bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl",children:"Empowering developers worldwide."}),(0,a.jsx)("p",{className:"mt-6 text-lg text-muted-foreground",children:"Join thousands of developers who are revolutionizing their workflow with AI-powered assistance."})]})}),(0,a.jsx)(d,{}),(0,a.jsx)(n.P.div,{className:"relative mx-auto hidden max-w-[1200px] md:block",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.3}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:(0,a.jsx)("div",{className:"relative grid grid-cols-1 gap-12 md:grid-cols-2",children:c.map((e,o)=>(0,a.jsxs)(n.P.div,{variants:t,className:"group relative ".concat(o%2==0?"md:translate-y-4":"md:translate-y-12"),children:[(0,a.jsx)("div",{className:"absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100"}),(0,a.jsxs)("div",{className:"relative h-full rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-border group-hover:bg-background/40",children:[e.image&&(0,a.jsx)("div",{className:"absolute -right-3 -top-3 h-16 w-16 overflow-hidden rounded-xl border border-border/50 bg-background/50 p-1.5 backdrop-blur-xl transition-all duration-500 ease-out group-hover:scale-105",children:(0,a.jsx)("div",{className:"relative h-full w-full overflow-hidden rounded-lg",children:(0,a.jsx)(r.default,{src:e.image||"/placeholder_pfp.png",alt:e.name,fill:!0,className:"object-cover"})})}),(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("svg",{className:"h-8 w-8 text-blue-500/20",fill:"currentColor",viewBox:"0 0 32 32",children:(0,a.jsx)("path",{d:"M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z"})})}),(0,a.jsx)("p",{className:"relative mb-6 text-lg leading-relaxed text-muted-foreground",children:e.quote}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent"}),(0,a.jsx)("h3",{className:"font-medium text-foreground/90",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.role," at ",e.company]})]})]})]})]},e.id))})})]})]})}},64518:(e,t,o)=>{o.r(t),o.d(t,{Features:()=>l,features:()=>i});var a=o(47093),s=o(38163),n=o(63091),r=o(69157);let i=[{icon:(0,a.jsx)(n.y8Q,{className:"h-6 w-6"}),title:"Your AI Dev Team in VS Code",description:"Roo Code puts a team of agentic AI assistants directly in your editor, with the power to plan, write, and fix code across multiple files.",size:"large"},{icon:(0,a.jsx)(n.FSj,{className:"h-6 w-6"}),title:"Multiple Specialized Modes",description:"From coding to debugging to architecture, Roo Code has a mode for every dev scenario—just switch on the fly.",size:"small"},{icon:(0,a.jsx)(n.KuA,{className:"h-6 w-6"}),title:"Deep Project-wide Context",description:"Roo Code reads your entire codebase, preserving valid code through diff-based edits for seamless multi-file refactors.",size:"small"},{icon:(0,a.jsx)(n.xdT,{className:"h-6 w-6"}),title:"Open-Source and Model-Agnostic",description:"Bring your own model or use local AI—no vendor lock-in. Roo Code is free, open, and adaptable to your needs.",size:"large"},{icon:(0,a.jsx)(n.iuJ,{className:"h-6 w-6"}),title:"Guarded Command Execution",description:"Approve or deny commands as needed. Roo Code automates your dev workflow while keeping oversight firmly in your hands.",size:"small"},{icon:(0,a.jsx)(n.yRn,{className:"h-6 w-6"}),title:"Fully Customizable",description:"Create or tweak modes, define usage rules, and shape Roo Code’s behavior precisely—your code, your way.",size:"small"},{icon:(0,a.jsx)(n.f35,{className:"h-6 w-6"}),title:"Automated Browser Actions",description:"Seamlessly test and verify your web app directly from VS Code—Roo Code can open a browser, run checks, and more.",size:"small"}];function l(){let e={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:[.21,.45,.27,.9]}}};return(0,a.jsxs)("section",{className:"relative overflow-hidden border-t border-border py-32",children:[(0,a.jsx)(s.P.div,{className:"absolute inset-0",initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1.2,ease:"easeOut"}}},children:(0,a.jsx)("div",{className:"absolute inset-y-0 left-1/2 h-full w-full max-w-[1200px] -translate-x-1/2",children:(0,a.jsx)("div",{className:"absolute left-1/2 top-1/2 h-[800px] w-full -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"})})}),(0,a.jsxs)("div",{className:"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"mx-auto mb-24 max-w-3xl text-center",children:(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,ease:[.21,.45,.27,.9]},children:[(0,a.jsx)("h2",{className:"bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl",children:"Powerful features for modern developers."}),(0,a.jsx)("p",{className:"mt-6 text-lg text-muted-foreground",children:"Everything you need to build faster and write better code."})]})}),(0,a.jsx)(r.FeaturesMobile,{}),(0,a.jsx)(s.P.div,{className:"relative mx-auto hidden max-w-[1200px] md:block",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.3}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:(0,a.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-8",children:i.map((t,o)=>(0,a.jsxs)(s.P.div,{variants:e,className:"group relative ".concat("large"===t.size?"lg:col-span-2":""," ").concat(o%2==0?"lg:translate-y-12":""),children:[(0,a.jsx)("div",{className:"absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"}),(0,a.jsxs)("div",{className:"relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-colors duration-300 hover:border-border",children:[(0,a.jsx)("div",{className:"mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5",children:(0,a.jsx)("div",{className:"rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5",children:(0,a.jsx)("div",{className:"text-foreground/90",children:t.icon})})}),(0,a.jsx)("h3",{className:"mb-3 text-xl font-medium text-foreground/90",children:t.title}),(0,a.jsx)("p",{className:"leading-relaxed text-muted-foreground",children:t.description})]})]},o))})})]})]})}},69157:(e,t,o)=>{o.r(t),o.d(t,{FeaturesMobile:()=>u});var a=o(47093),s=o(34545),n=o(75227),r=o(73093),i=o(29301),l=o(40959),d=o(23392),c=o(64518);function u(){let e=(0,r.A)({delay:5e3,stopOnInteraction:!0,stopOnMouseEnter:!0,rootNode:e=>e}),[t,o]=(0,n.A)({loop:!0,containScroll:"trimSnaps"},[e]),[u,m]=(0,s.useState)(0),[h,p]=(0,s.useState)([]),x=(0,s.useCallback)(e=>o&&o.scrollTo(e),[o]),g=(0,s.useCallback)(e=>{p(e.scrollSnapList())},[]),f=(0,s.useCallback)(e=>{m(e.selectedScrollSnap())},[]);return(0,s.useEffect)(()=>{if(o)return g(o),f(o),o.on("reInit",g),o.on("select",f),()=>{o.off("reInit",g),o.off("select",f)}},[o,g,f]),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)("div",{className:"relative px-4",children:[(0,a.jsx)("div",{className:"overflow-hidden",ref:t,children:(0,a.jsx)("div",{className:"flex",children:c.features.map((e,t)=>(0,a.jsx)("div",{className:"flex min-w-0 flex-[0_0_100%] px-4",children:(0,a.jsxs)("div",{className:"relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-colors duration-300 hover:border-border hover:bg-gray-900/20",children:[(0,a.jsx)("div",{className:"mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5",children:(0,a.jsx)("div",{className:"rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5",children:(0,a.jsx)("div",{className:"text-foreground/90",children:e.icon})})}),(0,a.jsx)("h3",{className:"mb-3 text-xl font-medium text-foreground/90",children:e.title}),(0,a.jsx)("p",{className:"leading-relaxed text-muted-foreground",children:e.description})]})},t))})}),(0,a.jsxs)("div",{className:"mt-6 flex items-center justify-between px-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-full border-border/50 bg-background/80 hover:bg-background",onClick:()=>null==o?void 0:o.scrollPrev(),children:[(0,a.jsx)(l.A,{className:"h-4 w-4 text-foreground/80"}),(0,a.jsx)("span",{className:"sr-only",children:"Previous slide"})]}),(0,a.jsxs)(i.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-full border-border/50 bg-background/80 hover:bg-background",onClick:()=>null==o?void 0:o.scrollNext(),children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-foreground/80"}),(0,a.jsx)("span",{className:"sr-only",children:"Next slide"})]})]}),(0,a.jsx)("div",{className:"flex gap-2",children:h.map((e,t)=>(0,a.jsx)("button",{type:"button",className:"h-3 w-3 rounded-full border border-border p-0 ".concat(t===u?"bg-foreground":"bg-background"),onClick:()=>x(t),"aria-label":"Go to slide ".concat(t+1)},t))})]})]})})}},70678:(e,t,o)=>{o.r(t),o.d(t,{InstallSection:()=>l});var a=o(47093),s=o(4784),n=o(4214),r=o.n(n),i=o(38163);function l(e){let{downloads:t}=e;return(0,a.jsxs)("section",{className:"relative overflow-hidden border-t border-border py-16 sm:py-24 lg:py-32",children:[(0,a.jsx)(i.P.div,{className:"absolute inset-x-0 top-1/2 -translate-y-1/2",initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1.2,ease:"easeOut"}}},children:(0,a.jsx)("div",{className:"relative mx-auto max-w-[1200px]",children:(0,a.jsx)("div",{className:"absolute left-1/2 top-1/2 h-[500px] w-[700px] -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"})})}),(0,a.jsx)("div",{className:"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"mx-auto max-w-3xl text-center",children:[(0,a.jsx)("h2",{className:"text-center text-xl font-semibold uppercase tracking-wider text-muted-foreground sm:text-2xl",children:"Install Roo Code — Open & Flexible"}),(0,a.jsx)("p",{className:"mt-4 text-center text-base text-muted-foreground sm:mt-6 sm:text-lg",children:"Roo Code is open-source, model-agnostic, and developer-focused. Install from the VS Code Marketplace or the CLI in minutes, then bring your own AI model."}),(0,a.jsxs)("div",{className:"mt-10 flex flex-col items-center justify-center gap-6",children:[(0,a.jsxs)(r(),{href:"https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline",target:"_blank",className:"group relative inline-flex w-full items-center justify-center gap-2 rounded-xl border border-border/50 bg-background/30 px-4 py-3 text-base backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/40 sm:w-auto sm:gap-3 sm:px-6 sm:py-4 sm:text-lg md:text-2xl",children:[(0,a.jsx)("div",{className:"absolute -inset-px rounded-xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"}),(0,a.jsxs)("div",{className:"relative flex items-center gap-2 sm:gap-3",children:[(0,a.jsx)(s.$$9,{className:"h-5 w-5 text-blue-400 sm:h-6 sm:w-6 md:h-8 md:w-8"}),(0,a.jsxs)("span",{className:"flex flex-wrap items-center gap-1 sm:gap-2 md:gap-3",children:[(0,a.jsx)("span",{className:"text-foreground/90",children:"VSCode Marketplace"}),null!==t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"hidden font-black text-muted-foreground sm:inline",children:"\xb7"}),(0,a.jsxs)("span",{className:"text-muted-foreground",children:[t," Downloads"]})]})]})]})]}),(0,a.jsxs)("div",{className:"group relative w-full max-w-xl",children:[(0,a.jsx)("div",{className:"absolute -inset-px rounded-xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"}),(0,a.jsxs)("div",{className:"relative overflow-hidden rounded-xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-border group-hover:bg-background/40",children:[(0,a.jsx)("div",{className:"border-b border-border/50 px-3 py-2 sm:px-4",children:(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Install via CLI"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsx)("pre",{className:"p-3 sm:p-4",children:(0,a.jsx)("code",{className:"whitespace-pre-wrap break-all text-sm text-foreground/90 sm:break-normal",children:"code --install-extension RooVeterinaryInc.roo-cline"})})})]})]})]})]})})]})}},99697:(e,t,o)=>{o.r(t),o.d(t,{CodeExample:()=>r});var a=o(47093),s=o(34545),n=o(38163);function r(){let[e,t]=(0,s.useState)("code"),[o,r]=(0,s.useState)(!1),[l,d]=(0,s.useState)(""),[c,u]=(0,s.useState)(0),m=(0,s.useRef)(null);(0,s.useEffect)(()=>{if(o&&c<i[e].code.length){let t=setTimeout(()=>{d(t=>t+i[e].code[c]),u(c+1),m.current&&(m.current.scrollTop=m.current.scrollHeight)},15);return()=>clearTimeout(t)}if(c>=i[e].code.length){r(!1);let t=setTimeout(()=>{h("code"===e?"architect":"architect"===e?"debug":"code")},1e3);return()=>clearTimeout(t)}},[o,c,e]);let h=e=>{t(e),d(""),u(0),r(!0),m.current&&(m.current.scrollTop=0)};return(0,s.useEffect)(()=>{r(!0)},[]),(0,a.jsx)("div",{className:"relative z-10 w-full max-w-[90vw] rounded-lg border border-border bg-background/50 p-2 shadow-2xl backdrop-blur-sm sm:max-w-[500px]",children:(0,a.jsxs)("div",{className:"rounded-md bg-muted p-1.5 dark:bg-gray-900 sm:p-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between border-b border-border px-2 py-1.5 sm:px-3 sm:py-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,a.jsx)("div",{className:"h-2.5 w-2.5 rounded-full bg-red-500 sm:h-3 sm:w-3"}),(0,a.jsx)("div",{className:"h-2.5 w-2.5 rounded-full bg-yellow-500 sm:h-3 sm:w-3"}),(0,a.jsx)("div",{className:"h-2.5 w-2.5 rounded-full bg-green-500 sm:h-3 sm:w-3"})]}),(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("button",{onClick:()=>h("code"),className:"rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm ".concat("code"===e?"bg-blue-500/20 text-blue-400":"text-gray-400 hover:bg-gray-800"),children:"Code"}),(0,a.jsx)("button",{onClick:()=>h("architect"),className:"rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm ".concat("architect"===e?"bg-purple-500/20 text-purple-400":"text-gray-400 hover:bg-gray-800"),children:"Architect"}),(0,a.jsx)("button",{onClick:()=>h("debug"),className:"rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm ".concat("debug"===e?"bg-green-500/20 text-green-400":"text-gray-400 hover:bg-gray-800"),children:"Debug"})]})]}),(0,a.jsx)("div",{className:"p-2 sm:p-4",children:(0,a.jsx)("pre",{ref:m,className:"scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent overflow-y-hidden text-xs text-muted-foreground max-lg:h-[25vh] sm:text-sm lg:max-h-[50vh]",children:(0,a.jsxs)("code",{className:"block whitespace-pre font-mono",children:[l,o&&(0,a.jsx)(n.P.span,{animate:{opacity:[1,0]},transition:{repeat:Number.POSITIVE_INFINITY,duration:.8},className:"inline-block h-4 w-2 bg-blue-500"})]})})})]})})}let i={code:{code:'// Code Mode\nYou: Generate a React component for a user profile card\n\nRoo: Here\'s a React component for a user profile card:\n\nimport React from \'react\';\n\ninterface UserProfileProps {\n  name: string;\n  role: string;\n  avatar: string;\n  bio: string;\n  stats: {\n    projects: number;\n    followers: number;\n    following: number;\n  };\n}\n\nexport const UserProfile: React.FC<UserProfileProps> = ({\n  name,\n  role,\n  avatar,\n  bio,\n  stats\n}) => {\n  return (\n    <div className="bg-white rounded-lg shadow-md p-6 max-w-sm">\n      <div className="flex items-center space-x-4">\n        <img \n          src={avatar || "/placeholder.svg"} \n          alt={name} \n          className="w-16 h-16 rounded-full object-cover"\n        />\n        <div>\n          <h3 className="text-xl font-bold">{name}</h3>\n          <p className="text-gray-600">{role}</p>\n        </div>\n      </div>\n      <p className="mt-4 text-gray-700">{bio}</p>\n      <div className="mt-6 flex justify-between">\n        <div className="text-center">\n          <span className="block font-bold">{stats.projects}</span>\n          <span className="text-sm text-gray-500">Projects</span>\n        </div>\n        <div className="text-center">\n          <span className="block font-bold">{stats.followers}</span>\n          <span className="text-sm text-gray-500">Followers</span>\n        </div>\n        <div className="text-center">\n          <span className="block font-bold">{stats.following}</span>\n          <span className="text-sm text-gray-500">Following</span>\n        </div>\n      </div>\n    </div>\n  );\n};'},architect:{code:"// Architect Mode\n// Project: E-commerce Platform\n// Request: Design the authentication system\n\nRoo is analyzing your project...\n\n\uD83D\uDCC2 Recommended Architecture:\n\nauth/\n├─ services/\n│  ├─ authService.ts       // Core authentication logic\n│  ├─ tokenService.ts      // JWT handling and refresh\n│  └─ userService.ts       // User data operations\n├─ hooks/\n│  ├─ useAuth.ts           // React hook for auth state\n│  └─ useProtectedRoute.ts // Route protection\n├─ components/\n│  ├─ LoginForm.tsx        // Login UI\n│  ├─ SignupForm.tsx       // Registration UI\n│  ├─ PasswordReset.tsx    // Password recovery\n│  └─ TwoFactorAuth.tsx    // 2FA implementation\n└─ context/\n   └─ AuthContext.tsx      // Global auth state\n\n\uD83D\uDD10 Security Recommendations:\n- Implement PKCE flow for auth code exchange\n- Use HttpOnly cookies for refresh tokens\n- Rate limit authentication attempts\n- Add device fingerprinting for suspicious login detection\n\n⚡ Performance Considerations:\n- Prefetch user data on auth\n- Implement token refresh without UI disruption\n- Lazy load auth components\n\nWould you like me to generate any of these files?"},debug:{code:"// Debug Mode\n// Analyzing error: TypeError: Cannot read property 'map' of undefined\n\nRoo has analyzed your code and found 3 issues:\n\n\uD83D\uDC1B Issue #1: Null data reference\n  Line 42: const items = data.items.map(item => item.name);\n  \n  ✓ Root Cause: 'data' is undefined when component mounts\n  ✓ Context: API request in useEffect hasn't completed yet\n  \n  Recommended Fix:\n  const items = data?.items?.map(item => item.name) || [];\n\n\uD83D\uDC1B Issue #2: Missing dependency in useEffect\n  Line 37: useEffect(() => { fetchData() }, []);\n  \n  ✓ Root Cause: fetchData depends on 'userId' but isn't in deps array\n  ✓ Context: This causes stale data when userId changes\n  \n  Recommended Fix:\n  useEffect(() => { fetchData() }, [userId, fetchData]);\n\n\uD83D\uDC1B Issue #3: Memory leak from unfinished API call\n  Line 38: const response = await api.getItems(userId);\n  \n  ✓ Root Cause: No cleanup when component unmounts during API call\n  ✓ Context: This triggers React warning in development\n  \n  Recommended Fix:\n  Add AbortController to cancel pending requests on unmount\n\nApply these fixes automatically? [Yes/No]"}}}}]);