import { describe, it, expect, beforeEach, afterEach, vi } from "vitest"
import { LangfuseClient } from "../src/LangfuseClient"
import type { LangfuseConfig } from "../src/types"

// Mock the Langfuse SDK
vi.mock("langfuse", () => ({
	Langfuse: vi.fn().mockImplementation(() => ({
		trace: vi.fn().mockReturnValue({ id: "test-trace" }),
		generation: vi.fn().mockReturnValue({ id: "test-generation" }),
		span: vi.fn().mockReturnValue({ id: "test-span" }),
		flushAsync: vi.fn().mockResolvedValue(undefined),
		shutdownAsync: vi.fn().mockResolvedValue(undefined),
	})),
}))

describe("LangfuseClient", () => {
	let client: LangfuseClient
	let mockConfig: LangfuseConfig

	beforeEach(() => {
		mockConfig = {
			enabled: true,
			publicKey: "test-public-key",
			secretKey: "test-secret-key",
			baseUrl: "https://test.langfuse.com",
			traceLlmCalls: true,
			traceConversations: true,
			traceToolUsage: true,
			capturePrompts: true,
			captureResponses: true,
			maxContentLength: 1000,
			respectPrivacySettings: true,
			defaultTags: ["test"],
			sessionTimeoutMinutes: 60,
		}
		client = new LangfuseClient(mockConfig)
	})

	afterEach(() => {
		vi.clearAllMocks()
	})

	describe("initialization", () => {
		it("should initialize successfully with valid config", async () => {
			await client.initialize()
			expect(client.isReady()).toBe(true)
		})

		it("should throw error when disabled", async () => {
			const disabledConfig = { ...mockConfig, enabled: false }
			const disabledClient = new LangfuseClient(disabledConfig)
			
			await expect(disabledClient.initialize()).rejects.toThrow("Langfuse is disabled")
		})

		it("should throw error with incomplete config", async () => {
			const incompleteConfig = { ...mockConfig, publicKey: undefined }
			const incompleteClient = new LangfuseClient(incompleteConfig)
			
			await expect(incompleteClient.initialize()).rejects.toThrow("incomplete")
		})
	})

	describe("configuration management", () => {
		it("should update config and reinitialize", async () => {
			

			await client.updateConfig({ enabled: false })
			expect(client.getConfig().enabled).toBe(false)

			await client.updateConfig({ enabled: true })
			await client.initialize()
			expect(client.isReady()).toBe(true)
		})

		it("should return current config", () => {
			const config = client.getConfig()
			expect(config.enabled).toBe(true)
			expect(config.publicKey).toBe("test-public-key")
		})
	})

	describe("trace creation", () => {
		beforeEach(async () => {
			await client.initialize()
		})

		it("should create trace successfully", async () => {
			const trace = await client.createTrace({
				name: "test-trace",
				input: { test: "input" },
				metadata: { test: "metadata" },
			})

			expect(trace).toBeDefined()
		})

		it("should return null when not ready", async () => {
			await client.shutdown()
			
			const trace = await client.createTrace({
				name: "test-trace",
			})

			expect(trace).toBeNull()
		})

		it("should include default tags", async () => {
			const trace = await client.createTrace({
				name: "test-trace",
				tags: ["custom"],
			})

			expect(trace).toBeDefined()
		})
	})

	describe("generation creation", () => {
		beforeEach(async () => {
			await client.initialize()
		})

		it("should create generation successfully", async () => {
			const generation = await client.createGeneration({
				name: "test-generation",
				model: "gpt-4",
				input: { prompt: "test" },
				output: { response: "test response" },
				usage: {
					promptTokens: 10,
					completionTokens: 20,
					totalTokens: 30,
				},
			})

			expect(generation).toBeDefined()
		})

		it("should handle generation with metadata", async () => {
			const generation = await client.createGeneration({
				name: "test-generation",
				model: "gpt-4",
				metadata: { provider: "openai" },
			})

			expect(generation).toBeDefined()
		})
	})

	describe("span creation", () => {
		beforeEach(async () => {
			await client.initialize()
		})

		it("should create span successfully", async () => {
			const span = await client.createSpan({
				name: "test-span",
				input: { tool: "test" },
				output: { result: "success" },
			})

			expect(span).toBeDefined()
		})

		it("should handle span with error", async () => {
			const span = await client.createSpan({
				name: "test-span",
				level: "ERROR",
				statusMessage: "Test error",
			})

			expect(span).toBeDefined()
		})
	})

	describe("error handling", () => {
		it("should handle errors gracefully", async () => {
			// Mock Langfuse to throw error
			const { Langfuse } = await import("langfuse")
			vi.mocked(Langfuse).mockImplementation(() => {
				throw new Error("Test error")
			})

			await expect(client.initialize()).rejects.toThrow("Failed to initialize")
		})

		it("should not throw on trace creation errors", async () => {
			await client.initialize()
			
			// Mock trace method to throw
			const mockClient = client.getClient()
			vi.mocked(mockClient.trace).mockImplementation(() => {
				throw new Error("Trace error")
			})

			const trace = await client.createTrace({ name: "test" })
			expect(trace).toBeNull()
		})
	})

	describe("shutdown", () => {
		it("should shutdown gracefully", async () => {
			await client.initialize()
			expect(client.isReady()).toBe(true)

			await client.shutdown()
			expect(client.isReady()).toBe(false)
		})

		it("should handle shutdown errors", async () => {
			await client.initialize()
			
			// Mock shutdown to throw
			const mockClient = client.getClient()
			vi.mocked(mockClient.shutdownAsync).mockRejectedValue(new Error("Shutdown error"))

			await expect(client.shutdown()).resolves.not.toThrow()
			expect(client.isReady()).toBe(false)
		})
	})

	describe("flush", () => {
		it("should flush successfully", async () => {
			await client.initialize()
			await expect(client.flush()).resolves.not.toThrow()
		})

		it("should handle flush when not ready", async () => {
			await expect(client.flush()).resolves.not.toThrow()
		})
	})
})
