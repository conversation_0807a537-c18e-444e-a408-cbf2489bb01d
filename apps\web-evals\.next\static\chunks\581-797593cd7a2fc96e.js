"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[581],{2134:(e,t,r)=>{r.d(t,{X:()=>d,k:()=>u});var s=r(8691),a=r(7554),i=r(8587),n=r(8939),d=class extends n.k{#e;#t;#r;#s;#a;#i;#n;constructor(e){super(),this.#n=!1,this.#i=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#s=e.client,this.#r=this.#s.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#e=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,s=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#e,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#a?.promise}setOptions(e){this.options={...this.#i,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#r.remove(this)}setData(e,t){let r=(0,s.pl)(this.state.data,e,this.options);return this.#d({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#d({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#a?.promise;return this.#a?.cancel(e),t?t.then(s.lQ).catch(s.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#e)}isActive(){return this.observers.some(e=>!1!==(0,s.Eh)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===s.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,s.j3)(this.state.dataUpdatedAt,e)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#a?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#a?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#r.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#a&&(this.#n?this.#a.cancel({revert:!0}):this.#a.cancelRetry()),this.scheduleGc()),this.#r.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#a)return this.#a.continueRetry(),this.#a.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,a=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#n=!0,r.signal)})},n={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#s,state:this.state,fetchFn:()=>{let e=(0,s.ZM)(this.options,t),r={client:this.#s,queryKey:this.queryKey,meta:this.meta};return(a(r),this.#n=!1,this.options.persister)?this.options.persister(e,r,this):e(r)}};a(n),this.options.behavior?.onFetch(n,this),this.#t=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==n.fetchOptions?.meta)&&this.#d({type:"fetch",meta:n.fetchOptions?.meta});let d=e=>{(0,i.wm)(e)&&e.silent||this.#d({type:"error",error:e}),(0,i.wm)(e)||(this.#r.config.onError?.(e,this),this.#r.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#a=(0,i.II)({initialPromise:t?.initialPromise,fn:n.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e){d(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){d(e);return}this.#r.config.onSuccess?.(e,this),this.#r.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:d,onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#a.start()}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...u(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if((0,i.wm)(r)&&r.revert&&this.#t)return{...this.#t,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),a.jG.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#r.notify({query:this,type:"updated",action:e})})}};function u(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,i.v_)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},5745:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(436).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},8590:(e,t,r)=>{r.d(t,{I:()=>Z});var s=r(7569),a=r(7554),i=r(2134),n=r(667),d=r(6733),u=r(8691),o=class extends n.Q{constructor(e,t){super(),this.options=t,this.#s=e,this.#u=null,this.#o=(0,d.T)(),this.options.experimental_prefetchInRender||this.#o.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#s;#l=void 0;#c=void 0;#h=void 0;#p;#f;#o;#u;#m;#y;#v;#_;#g;#b;#k=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#l.addObserver(this),l(this.#l,this.options)?this.#x():this.updateResult(),this.#w())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return c(this.#l,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return c(this.#l,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#T(),this.#O(),this.#l.removeObserver(this)}setOptions(e){let t=this.options,r=this.#l;if(this.options=this.#s.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,u.Eh)(this.options.enabled,this.#l))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#R(),this.#l.setOptions(this.options),t._defaulted&&!(0,u.f8)(this.options,t)&&this.#s.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#l,observer:this});let s=this.hasListeners();s&&h(this.#l,r,this.options,t)&&this.#x(),this.updateResult(),s&&(this.#l!==r||(0,u.Eh)(this.options.enabled,this.#l)!==(0,u.Eh)(t.enabled,this.#l)||(0,u.d2)(this.options.staleTime,this.#l)!==(0,u.d2)(t.staleTime,this.#l))&&this.#Z();let a=this.#C();s&&(this.#l!==r||(0,u.Eh)(this.options.enabled,this.#l)!==(0,u.Eh)(t.enabled,this.#l)||a!==this.#b)&&this.#S(a)}getOptimisticResult(e){var t,r;let s=this.#s.getQueryCache().build(this.#s,e),a=this.createResult(s,e);return t=this,r=a,(0,u.f8)(t.getCurrentResult(),r)||(this.#h=a,this.#f=this.options,this.#p=this.#l.state),a}getCurrentResult(){return this.#h}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#k.add(e)}getCurrentQuery(){return this.#l}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#s.defaultQueryOptions(e),r=this.#s.getQueryCache().build(this.#s,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#x({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#h))}#x(e){this.#R();let t=this.#l.fetch(this.options,e);return e?.throwOnError||(t=t.catch(u.lQ)),t}#Z(){this.#T();let e=(0,u.d2)(this.options.staleTime,this.#l);if(u.S$||this.#h.isStale||!(0,u.gn)(e))return;let t=(0,u.j3)(this.#h.dataUpdatedAt,e);this.#_=setTimeout(()=>{this.#h.isStale||this.updateResult()},t+1)}#C(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#l):this.options.refetchInterval)??!1}#S(e){this.#O(),this.#b=e,!u.S$&&!1!==(0,u.Eh)(this.options.enabled,this.#l)&&(0,u.gn)(this.#b)&&0!==this.#b&&(this.#g=setInterval(()=>{(this.options.refetchIntervalInBackground||s.m.isFocused())&&this.#x()},this.#b))}#w(){this.#Z(),this.#S(this.#C())}#T(){this.#_&&(clearTimeout(this.#_),this.#_=void 0)}#O(){this.#g&&(clearInterval(this.#g),this.#g=void 0)}createResult(e,t){let r,s=this.#l,a=this.options,n=this.#h,o=this.#p,c=this.#f,f=e!==s?e.state:this.#c,{state:m}=e,y={...m},v=!1;if(t._optimisticResults){let r=this.hasListeners(),n=!r&&l(e,t),d=r&&h(e,s,t,a);(n||d)&&(y={...y,...(0,i.k)(m.data,e.options)}),"isRestoring"===t._optimisticResults&&(y.fetchStatus="idle")}let{error:_,errorUpdatedAt:g,status:b}=y;r=y.data;let k=!1;if(void 0!==t.placeholderData&&void 0===r&&"pending"===b){let e;n?.isPlaceholderData&&t.placeholderData===c?.placeholderData?(e=n.data,k=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#v?.state.data,this.#v):t.placeholderData,void 0!==e&&(b="success",r=(0,u.pl)(n?.data,e,t),v=!0)}if(t.select&&void 0!==r&&!k)if(n&&r===o?.data&&t.select===this.#m)r=this.#y;else try{this.#m=t.select,r=t.select(r),r=(0,u.pl)(n?.data,r,t),this.#y=r,this.#u=null}catch(e){this.#u=e}this.#u&&(_=this.#u,r=this.#y,g=Date.now(),b="error");let x="fetching"===y.fetchStatus,w="pending"===b,T="error"===b,O=w&&x,R=void 0!==r,Z={status:b,fetchStatus:y.fetchStatus,isPending:w,isSuccess:"success"===b,isError:T,isInitialLoading:O,isLoading:O,data:r,dataUpdatedAt:y.dataUpdatedAt,error:_,errorUpdatedAt:g,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>f.dataUpdateCount||y.errorUpdateCount>f.errorUpdateCount,isFetching:x,isRefetching:x&&!w,isLoadingError:T&&!R,isPaused:"paused"===y.fetchStatus,isPlaceholderData:v,isRefetchError:T&&R,isStale:p(e,t),refetch:this.refetch,promise:this.#o};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===Z.status?e.reject(Z.error):void 0!==Z.data&&e.resolve(Z.data)},r=()=>{t(this.#o=Z.promise=(0,d.T)())},a=this.#o;switch(a.status){case"pending":e.queryHash===s.queryHash&&t(a);break;case"fulfilled":("error"===Z.status||Z.data!==a.value)&&r();break;case"rejected":("error"!==Z.status||Z.error!==a.reason)&&r()}}return Z}updateResult(){let e=this.#h,t=this.createResult(this.#l,this.options);this.#p=this.#l.state,this.#f=this.options,void 0!==this.#p.data&&(this.#v=this.#l),!(0,u.f8)(t,e)&&(this.#h=t,this.#I({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#k.size)return!0;let s=new Set(r??this.#k);return this.options.throwOnError&&s.add("error"),Object.keys(this.#h).some(t=>this.#h[t]!==e[t]&&s.has(t))})()}))}#R(){let e=this.#s.getQueryCache().build(this.#s,this.options);if(e===this.#l)return;let t=this.#l;this.#l=e,this.#c=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#w()}#I(e){a.jG.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#h)}),this.#s.getQueryCache().notify({query:this.#l,type:"observerResultsUpdated"})})}};function l(e,t){return!1!==(0,u.Eh)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&c(e,t,t.refetchOnMount)}function c(e,t,r){if(!1!==(0,u.Eh)(t.enabled,e)){let s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&p(e,t)}return!1}function h(e,t,r,s){return(e!==t||!1===(0,u.Eh)(s.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,u.Eh)(t.enabled,e)&&e.isStaleByTime((0,u.d2)(t.staleTime,e))}var f=r(4545),m=r(6890);r(7093);var y=f.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),v=()=>f.useContext(y),_=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},g=e=>{f.useEffect(()=>{e.clearReset()},[e])},b=e=>{let{result:t,errorResetBoundary:r,throwOnError:s,query:a,suspense:i}=e;return t.isError&&!r.isReset()&&!t.isFetching&&a&&(i&&void 0===t.data||(0,u.GU)(s,[t.error,a]))},k=f.createContext(!1),x=()=>f.useContext(k);k.Provider;var w=e=>{let t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},T=(e,t)=>e.isLoading&&e.isFetching&&!t,O=(e,t)=>e?.suspense&&t.isPending,R=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function Z(e,t){return function(e,t,r){var s,i,n,d,o;let l=(0,m.jE)(r),c=x(),h=v(),p=l.defaultQueryOptions(e);null===(i=l.getDefaultOptions().queries)||void 0===i||null===(s=i._experimental_beforeQuery)||void 0===s||s.call(i,p),p._optimisticResults=c?"isRestoring":"optimistic",w(p),_(p,h),g(h);let y=!l.getQueryCache().get(p.queryHash),[k]=f.useState(()=>new t(l,p)),Z=k.getOptimisticResult(p),C=!c&&!1!==e.subscribed;if(f.useSyncExternalStore(f.useCallback(e=>{let t=C?k.subscribe(a.jG.batchCalls(e)):u.lQ;return k.updateResult(),t},[k,C]),()=>k.getCurrentResult(),()=>k.getCurrentResult()),f.useEffect(()=>{k.setOptions(p)},[p,k]),O(p,Z))throw R(p,k,h);if(b({result:Z,errorResetBoundary:h,throwOnError:p.throwOnError,query:l.getQueryCache().get(p.queryHash),suspense:p.suspense}))throw Z.error;if(null===(d=l.getDefaultOptions().queries)||void 0===d||null===(n=d._experimental_afterQuery)||void 0===n||n.call(d,p,Z),p.experimental_prefetchInRender&&!u.S$&&T(Z,c)){let e=y?R(p,k,h):null===(o=l.getQueryCache().get(p.queryHash))||void 0===o?void 0:o.promise;null==e||e.catch(u.lQ).finally(()=>{k.updateResult()})}return p.notifyOnChangeProps?Z:k.trackResult(Z)}(e,o,t)}},9860:(e,t,r)=>{let s;r.d(t,{z:()=>u});var a,i,n,d,u={};r.r(u),r.d(u,{BRAND:()=>eE,DIRTY:()=>w,EMPTY_PATH:()=>g,INVALID:()=>x,NEVER:()=>tf,OK:()=>T,ParseStatus:()=>k,Schema:()=>E,ZodAny:()=>ei,ZodArray:()=>eo,ZodBigInt:()=>X,ZodBoolean:()=>ee,ZodBranded:()=>eN,ZodCatch:()=>eI,ZodDate:()=>et,ZodDefault:()=>eS,ZodDiscriminatedUnion:()=>ep,ZodEffects:()=>eR,ZodEnum:()=>ew,ZodError:()=>p,ZodFirstPartyTypeKind:()=>d,ZodFunction:()=>eg,ZodIntersection:()=>ef,ZodIssueCode:()=>c,ZodLazy:()=>eb,ZodLiteral:()=>ek,ZodMap:()=>ev,ZodNaN:()=>eA,ZodNativeEnum:()=>eT,ZodNever:()=>ed,ZodNull:()=>ea,ZodNullable:()=>eC,ZodNumber:()=>Y,ZodObject:()=>el,ZodOptional:()=>eZ,ZodParsedType:()=>o,ZodPipeline:()=>ej,ZodPromise:()=>eO,ZodReadonly:()=>eP,ZodRecord:()=>ey,ZodSchema:()=>E,ZodSet:()=>e_,ZodString:()=>J,ZodSymbol:()=>er,ZodTransformer:()=>eR,ZodTuple:()=>em,ZodType:()=>E,ZodUndefined:()=>es,ZodUnion:()=>ec,ZodUnknown:()=>en,ZodVoid:()=>eu,addIssueToContext:()=>b,any:()=>eG,array:()=>eX,bigint:()=>ez,boolean:()=>eq,coerce:()=>tp,custom:()=>e$,date:()=>eV,datetimeRegex:()=>H,defaultErrorMap:()=>f,discriminatedUnion:()=>e4,effect:()=>ti,enum:()=>tr,function:()=>e7,getErrorMap:()=>v,getParsedType:()=>l,instanceof:()=>eM,intersection:()=>e2,isAborted:()=>O,isAsync:()=>C,isDirty:()=>R,isValid:()=>Z,late:()=>eD,lazy:()=>te,literal:()=>tt,makeIssue:()=>_,map:()=>e6,nan:()=>eL,nativeEnum:()=>ts,never:()=>eJ,null:()=>eB,nullable:()=>td,number:()=>eQ,object:()=>e0,objectUtil:()=>i,oboolean:()=>th,onumber:()=>tc,optional:()=>tn,ostring:()=>tl,pipeline:()=>to,preprocess:()=>tu,promise:()=>ta,quotelessJson:()=>h,record:()=>e3,set:()=>e8,setErrorMap:()=>y,strictObject:()=>e1,string:()=>eU,symbol:()=>eK,transformer:()=>ti,tuple:()=>e5,undefined:()=>eW,union:()=>e9,unknown:()=>eH,util:()=>a,void:()=>eY}),function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),s={};for(let e of r)s[e]=t[e];return e.objectValues(s)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(a||(a={})),(i||(i={})).mergeShapes=(e,t)=>({...e,...t});let o=a.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),l=e=>{switch(typeof e){case"undefined":return o.undefined;case"string":return o.string;case"number":return Number.isNaN(e)?o.nan:o.number;case"boolean":return o.boolean;case"function":return o.function;case"bigint":return o.bigint;case"symbol":return o.symbol;case"object":if(Array.isArray(e))return o.array;if(null===e)return o.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return o.promise;if("undefined"!=typeof Map&&e instanceof Map)return o.map;if("undefined"!=typeof Set&&e instanceof Set)return o.set;if("undefined"!=typeof Date&&e instanceof Date)return o.date;return o.object;default:return o.unknown}},c=a.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),h=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class p extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},s=e=>{for(let a of e.issues)if("invalid_union"===a.code)a.unionErrors.map(s);else if("invalid_return_type"===a.code)s(a.returnTypeError);else if("invalid_arguments"===a.code)s(a.argumentsError);else if(0===a.path.length)r._errors.push(t(a));else{let e=r,s=0;for(;s<a.path.length;){let r=a.path[s];s===a.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(a))):e[r]=e[r]||{_errors:[]},e=e[r],s++}}};return s(this),r}static assert(e){if(!(e instanceof p))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,a.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let s of this.issues)s.path.length>0?(t[s.path[0]]=t[s.path[0]]||[],t[s.path[0]].push(e(s))):r.push(e(s));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}p.create=e=>new p(e);let f=(e,t)=>{let r;switch(e.code){case c.invalid_type:r=e.received===o.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case c.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,a.jsonStringifyReplacer)}`;break;case c.unrecognized_keys:r=`Unrecognized key(s) in object: ${a.joinValues(e.keys,", ")}`;break;case c.invalid_union:r="Invalid input";break;case c.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${a.joinValues(e.options)}`;break;case c.invalid_enum_value:r=`Invalid enum value. Expected ${a.joinValues(e.options)}, received '${e.received}'`;break;case c.invalid_arguments:r="Invalid function arguments";break;case c.invalid_return_type:r="Invalid function return type";break;case c.invalid_date:r="Invalid date";break;case c.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:a.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case c.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case c.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case c.custom:r="Invalid input";break;case c.invalid_intersection_types:r="Intersection results could not be merged";break;case c.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case c.not_finite:r="Number must be finite";break;default:r=t.defaultError,a.assertNever(e)}return{message:r}},m=f;function y(e){m=e}function v(){return m}let _=e=>{let{data:t,path:r,errorMaps:s,issueData:a}=e,i=[...r,...a.path||[]],n={...a,path:i};if(void 0!==a.message)return{...a,path:i,message:a.message};let d="";for(let e of s.filter(e=>!!e).slice().reverse())d=e(n,{data:t,defaultError:d}).message;return{...a,path:i,message:d}},g=[];function b(e,t){let r=m,s=_({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,r,r===f?void 0:f].filter(e=>!!e)});e.common.issues.push(s)}class k{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let s of t){if("aborted"===s.status)return x;"dirty"===s.status&&e.dirty(),r.push(s.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,s=await e.value;r.push({key:t,value:s})}return k.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let s of t){let{key:t,value:a}=s;if("aborted"===t.status||"aborted"===a.status)return x;"dirty"===t.status&&e.dirty(),"dirty"===a.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==a.value||s.alwaysSet)&&(r[t.value]=a.value)}return{status:e.value,value:r}}}let x=Object.freeze({status:"aborted"}),w=e=>({status:"dirty",value:e}),T=e=>({status:"valid",value:e}),O=e=>"aborted"===e.status,R=e=>"dirty"===e.status,Z=e=>"valid"===e.status,C=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(n||(n={}));class S{constructor(e,t,r,s){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=s}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let I=(e,t)=>{if(Z(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new p(e.common.issues);return this._error=t,this._error}}};function A(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:s,description:a}=e;if(t&&(r||s))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:a}:{errorMap:(t,a)=>{let{message:i}=e;return"invalid_enum_value"===t.code?{message:i??a.defaultError}:void 0===a.data?{message:i??s??a.defaultError}:"invalid_type"!==t.code?{message:a.defaultError}:{message:i??r??a.defaultError}},description:a}}class E{get description(){return this._def.description}_getType(e){return l(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:l(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new k,ctx:{common:e.parent.common,data:e.data,parsedType:l(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(C(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:l(e)},s=this._parseSync({data:e,path:r.path,parent:r});return I(r,s)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:l(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return Z(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>Z(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:l(e)},s=this._parse({data:e,path:r.path,parent:r});return I(r,await (C(s)?s:Promise.resolve(s)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,s)=>{let a=e(t),i=()=>s.addIssue({code:c.custom,...r(t)});return"undefined"!=typeof Promise&&a instanceof Promise?a.then(e=>!!e||(i(),!1)):!!a||(i(),!1)})}refinement(e,t){return this._refinement((r,s)=>!!e(r)||(s.addIssue("function"==typeof t?t(r,s):t),!1))}_refinement(e){return new eR({schema:this,typeName:d.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return eZ.create(this,this._def)}nullable(){return eC.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return eo.create(this)}promise(){return eO.create(this,this._def)}or(e){return ec.create([this,e],this._def)}and(e){return ef.create(this,e,this._def)}transform(e){return new eR({...A(this._def),schema:this,typeName:d.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eS({...A(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:d.ZodDefault})}brand(){return new eN({typeName:d.ZodBranded,type:this,...A(this._def)})}catch(e){return new eI({...A(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:d.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return ej.create(this,e)}readonly(){return eP.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let N=/^c[^\s-]{8,}$/i,j=/^[0-9a-z]+$/,P=/^[0-9A-HJKMNP-TV-Z]{26}$/i,F=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,$=/^[a-z0-9_-]{21}$/i,D=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,M=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,U=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Q=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,L=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,z=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,q=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,V=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,K=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,W="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",B=RegExp(`^${W}$`);function G(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}function H(e){let t=`${W}T${G(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)}class J extends E{_parse(e){var t,r,i,n;let d;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==o.string){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.string,received:t.parsedType}),x}let u=new k;for(let o of this._def.checks)if("min"===o.kind)e.data.length<o.value&&(b(d=this._getOrReturnCtx(e,d),{code:c.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),u.dirty());else if("max"===o.kind)e.data.length>o.value&&(b(d=this._getOrReturnCtx(e,d),{code:c.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),u.dirty());else if("length"===o.kind){let t=e.data.length>o.value,r=e.data.length<o.value;(t||r)&&(d=this._getOrReturnCtx(e,d),t?b(d,{code:c.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}):r&&b(d,{code:c.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}),u.dirty())}else if("email"===o.kind)U.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"email",code:c.invalid_string,message:o.message}),u.dirty());else if("emoji"===o.kind)s||(s=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),s.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"emoji",code:c.invalid_string,message:o.message}),u.dirty());else if("uuid"===o.kind)F.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"uuid",code:c.invalid_string,message:o.message}),u.dirty());else if("nanoid"===o.kind)$.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"nanoid",code:c.invalid_string,message:o.message}),u.dirty());else if("cuid"===o.kind)N.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"cuid",code:c.invalid_string,message:o.message}),u.dirty());else if("cuid2"===o.kind)j.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"cuid2",code:c.invalid_string,message:o.message}),u.dirty());else if("ulid"===o.kind)P.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"ulid",code:c.invalid_string,message:o.message}),u.dirty());else if("url"===o.kind)try{new URL(e.data)}catch{b(d=this._getOrReturnCtx(e,d),{validation:"url",code:c.invalid_string,message:o.message}),u.dirty()}else"regex"===o.kind?(o.regex.lastIndex=0,o.regex.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"regex",code:c.invalid_string,message:o.message}),u.dirty())):"trim"===o.kind?e.data=e.data.trim():"includes"===o.kind?e.data.includes(o.value,o.position)||(b(d=this._getOrReturnCtx(e,d),{code:c.invalid_string,validation:{includes:o.value,position:o.position},message:o.message}),u.dirty()):"toLowerCase"===o.kind?e.data=e.data.toLowerCase():"toUpperCase"===o.kind?e.data=e.data.toUpperCase():"startsWith"===o.kind?e.data.startsWith(o.value)||(b(d=this._getOrReturnCtx(e,d),{code:c.invalid_string,validation:{startsWith:o.value},message:o.message}),u.dirty()):"endsWith"===o.kind?e.data.endsWith(o.value)||(b(d=this._getOrReturnCtx(e,d),{code:c.invalid_string,validation:{endsWith:o.value},message:o.message}),u.dirty()):"datetime"===o.kind?H(o).test(e.data)||(b(d=this._getOrReturnCtx(e,d),{code:c.invalid_string,validation:"datetime",message:o.message}),u.dirty()):"date"===o.kind?B.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{code:c.invalid_string,validation:"date",message:o.message}),u.dirty()):"time"===o.kind?RegExp(`^${G(o)}$`).test(e.data)||(b(d=this._getOrReturnCtx(e,d),{code:c.invalid_string,validation:"time",message:o.message}),u.dirty()):"duration"===o.kind?M.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"duration",code:c.invalid_string,message:o.message}),u.dirty()):"ip"===o.kind?(t=e.data,!(("v4"===(r=o.version)||!r)&&Q.test(t)||("v6"===r||!r)&&z.test(t))&&(b(d=this._getOrReturnCtx(e,d),{validation:"ip",code:c.invalid_string,message:o.message}),u.dirty())):"jwt"===o.kind?!function(e,t){if(!D.test(e))return!1;try{let[r]=e.split("."),s=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),a=JSON.parse(atob(s));if("object"!=typeof a||null===a||"typ"in a&&a?.typ!=="JWT"||!a.alg||t&&a.alg!==t)return!1;return!0}catch{return!1}}(e.data,o.alg)&&(b(d=this._getOrReturnCtx(e,d),{validation:"jwt",code:c.invalid_string,message:o.message}),u.dirty()):"cidr"===o.kind?(i=e.data,!(("v4"===(n=o.version)||!n)&&L.test(i)||("v6"===n||!n)&&q.test(i))&&(b(d=this._getOrReturnCtx(e,d),{validation:"cidr",code:c.invalid_string,message:o.message}),u.dirty())):"base64"===o.kind?V.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"base64",code:c.invalid_string,message:o.message}),u.dirty()):"base64url"===o.kind?K.test(e.data)||(b(d=this._getOrReturnCtx(e,d),{validation:"base64url",code:c.invalid_string,message:o.message}),u.dirty()):a.assertNever(o);return{status:u.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:c.invalid_string,...n.errToObj(r)})}_addCheck(e){return new J({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...n.errToObj(e)})}url(e){return this._addCheck({kind:"url",...n.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...n.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...n.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...n.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...n.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...n.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...n.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...n.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...n.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...n.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...n.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...n.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...n.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...n.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...n.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...n.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...n.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...n.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...n.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...n.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...n.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...n.errToObj(t)})}nonempty(e){return this.min(1,n.errToObj(e))}trim(){return new J({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new J({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new J({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}J.create=e=>new J({checks:[],typeName:d.ZodString,coerce:e?.coerce??!1,...A(e)});class Y extends E{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==o.number){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.number,received:t.parsedType}),x}let r=new k;for(let s of this._def.checks)"int"===s.kind?a.isInteger(e.data)||(b(t=this._getOrReturnCtx(e,t),{code:c.invalid_type,expected:"integer",received:"float",message:s.message}),r.dirty()):"min"===s.kind?(s.inclusive?e.data<s.value:e.data<=s.value)&&(b(t=this._getOrReturnCtx(e,t),{code:c.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):"max"===s.kind?(s.inclusive?e.data>s.value:e.data>=s.value)&&(b(t=this._getOrReturnCtx(e,t),{code:c.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):"multipleOf"===s.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,s=(t.toString().split(".")[1]||"").length,a=r>s?r:s;return Number.parseInt(e.toFixed(a).replace(".",""))%Number.parseInt(t.toFixed(a).replace(".",""))/10**a}(e.data,s.value)&&(b(t=this._getOrReturnCtx(e,t),{code:c.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):"finite"===s.kind?Number.isFinite(e.data)||(b(t=this._getOrReturnCtx(e,t),{code:c.not_finite,message:s.message}),r.dirty()):a.assertNever(s);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,r,s){return new Y({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:n.toString(s)}]})}_addCheck(e){return new Y({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:n.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:n.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:n.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:n.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&a.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks)if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;else"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value);return Number.isFinite(t)&&Number.isFinite(e)}}Y.create=e=>new Y({checks:[],typeName:d.ZodNumber,coerce:e?.coerce||!1,...A(e)});class X extends E{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==o.bigint)return this._getInvalidInput(e);let r=new k;for(let s of this._def.checks)"min"===s.kind?(s.inclusive?e.data<s.value:e.data<=s.value)&&(b(t=this._getOrReturnCtx(e,t),{code:c.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):"max"===s.kind?(s.inclusive?e.data>s.value:e.data>=s.value)&&(b(t=this._getOrReturnCtx(e,t),{code:c.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):"multipleOf"===s.kind?e.data%s.value!==BigInt(0)&&(b(t=this._getOrReturnCtx(e,t),{code:c.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):a.assertNever(s);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.bigint,received:t.parsedType}),x}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,r,s){return new X({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:n.toString(s)}]})}_addCheck(e){return new X({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}X.create=e=>new X({checks:[],typeName:d.ZodBigInt,coerce:e?.coerce??!1,...A(e)});class ee extends E{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==o.boolean){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.boolean,received:t.parsedType}),x}return T(e.data)}}ee.create=e=>new ee({typeName:d.ZodBoolean,coerce:e?.coerce||!1,...A(e)});class et extends E{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==o.date){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.date,received:t.parsedType}),x}if(Number.isNaN(e.data.getTime()))return b(this._getOrReturnCtx(e),{code:c.invalid_date}),x;let r=new k;for(let s of this._def.checks)"min"===s.kind?e.data.getTime()<s.value&&(b(t=this._getOrReturnCtx(e,t),{code:c.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),r.dirty()):"max"===s.kind?e.data.getTime()>s.value&&(b(t=this._getOrReturnCtx(e,t),{code:c.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),r.dirty()):a.assertNever(s);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new et({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:n.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:n.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}et.create=e=>new et({checks:[],coerce:e?.coerce||!1,typeName:d.ZodDate,...A(e)});class er extends E{_parse(e){if(this._getType(e)!==o.symbol){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.symbol,received:t.parsedType}),x}return T(e.data)}}er.create=e=>new er({typeName:d.ZodSymbol,...A(e)});class es extends E{_parse(e){if(this._getType(e)!==o.undefined){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.undefined,received:t.parsedType}),x}return T(e.data)}}es.create=e=>new es({typeName:d.ZodUndefined,...A(e)});class ea extends E{_parse(e){if(this._getType(e)!==o.null){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.null,received:t.parsedType}),x}return T(e.data)}}ea.create=e=>new ea({typeName:d.ZodNull,...A(e)});class ei extends E{constructor(){super(...arguments),this._any=!0}_parse(e){return T(e.data)}}ei.create=e=>new ei({typeName:d.ZodAny,...A(e)});class en extends E{constructor(){super(...arguments),this._unknown=!0}_parse(e){return T(e.data)}}en.create=e=>new en({typeName:d.ZodUnknown,...A(e)});class ed extends E{_parse(e){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.never,received:t.parsedType}),x}}ed.create=e=>new ed({typeName:d.ZodNever,...A(e)});class eu extends E{_parse(e){if(this._getType(e)!==o.undefined){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.void,received:t.parsedType}),x}return T(e.data)}}eu.create=e=>new eu({typeName:d.ZodVoid,...A(e)});class eo extends E{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),s=this._def;if(t.parsedType!==o.array)return b(t,{code:c.invalid_type,expected:o.array,received:t.parsedType}),x;if(null!==s.exactLength){let e=t.data.length>s.exactLength.value,a=t.data.length<s.exactLength.value;(e||a)&&(b(t,{code:e?c.too_big:c.too_small,minimum:a?s.exactLength.value:void 0,maximum:e?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),r.dirty())}if(null!==s.minLength&&t.data.length<s.minLength.value&&(b(t,{code:c.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),r.dirty()),null!==s.maxLength&&t.data.length>s.maxLength.value&&(b(t,{code:c.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>s.type._parseAsync(new S(t,e,t.path,r)))).then(e=>k.mergeArray(r,e));let a=[...t.data].map((e,r)=>s.type._parseSync(new S(t,e,t.path,r)));return k.mergeArray(r,a)}get element(){return this._def.type}min(e,t){return new eo({...this._def,minLength:{value:e,message:n.toString(t)}})}max(e,t){return new eo({...this._def,maxLength:{value:e,message:n.toString(t)}})}length(e,t){return new eo({...this._def,exactLength:{value:e,message:n.toString(t)}})}nonempty(e){return this.min(1,e)}}eo.create=(e,t)=>new eo({type:e,minLength:null,maxLength:null,exactLength:null,typeName:d.ZodArray,...A(t)});class el extends E{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=a.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==o.object){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.object,received:t.parsedType}),x}let{status:t,ctx:r}=this._processInputParams(e),{shape:s,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof ed&&"strip"===this._def.unknownKeys))for(let e in r.data)a.includes(e)||i.push(e);let n=[];for(let e of a){let t=s[e],a=r.data[e];n.push({key:{status:"valid",value:e},value:t._parse(new S(r,a,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof ed){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of i)n.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)i.length>0&&(b(r,{code:c.unrecognized_keys,keys:i}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of i){let s=r.data[t];n.push({key:{status:"valid",value:t},value:e._parse(new S(r,s,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of n){let r=await t.key,s=await t.value;e.push({key:r,value:s,alwaysSet:t.alwaysSet})}return e}).then(e=>k.mergeObjectSync(t,e)):k.mergeObjectSync(t,n)}get shape(){return this._def.shape()}strict(e){return n.errToObj,new el({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{let s=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:n.errToObj(e).message??s}:{message:s}}}:{}})}strip(){return new el({...this._def,unknownKeys:"strip"})}passthrough(){return new el({...this._def,unknownKeys:"passthrough"})}extend(e){return new el({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new el({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:d.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new el({...this._def,catchall:e})}pick(e){let t={};for(let r of a.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new el({...this._def,shape:()=>t})}omit(e){let t={};for(let r of a.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new el({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof el){let r={};for(let s in t.shape){let a=t.shape[s];r[s]=eZ.create(e(a))}return new el({...t._def,shape:()=>r})}if(t instanceof eo)return new eo({...t._def,type:e(t.element)});if(t instanceof eZ)return eZ.create(e(t.unwrap()));if(t instanceof eC)return eC.create(e(t.unwrap()));if(t instanceof em)return em.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let r of a.objectKeys(this.shape)){let s=this.shape[r];e&&!e[r]?t[r]=s:t[r]=s.optional()}return new el({...this._def,shape:()=>t})}required(e){let t={};for(let r of a.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof eZ;)e=e._def.innerType;t[r]=e}return new el({...this._def,shape:()=>t})}keyof(){return ex(a.objectKeys(this.shape))}}el.create=(e,t)=>new el({shape:()=>e,unknownKeys:"strip",catchall:ed.create(),typeName:d.ZodObject,...A(t)}),el.strictCreate=(e,t)=>new el({shape:()=>e,unknownKeys:"strict",catchall:ed.create(),typeName:d.ZodObject,...A(t)}),el.lazycreate=(e,t)=>new el({shape:e,unknownKeys:"strip",catchall:ed.create(),typeName:d.ZodObject,...A(t)});class ec extends E{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new p(e.ctx.common.issues));return b(t,{code:c.invalid_union,unionErrors:r}),x});{let e,s=[];for(let a of r){let r={...t,common:{...t.common,issues:[]},parent:null},i=a._parseSync({data:t.data,path:t.path,parent:r});if("valid"===i.status)return i;"dirty"!==i.status||e||(e={result:i,ctx:r}),r.common.issues.length&&s.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let a=s.map(e=>new p(e));return b(t,{code:c.invalid_union,unionErrors:a}),x}}get options(){return this._def.options}}ec.create=(e,t)=>new ec({options:e,typeName:d.ZodUnion,...A(t)});let eh=e=>{if(e instanceof eb)return eh(e.schema);if(e instanceof eR)return eh(e.innerType());if(e instanceof ek)return[e.value];if(e instanceof ew)return e.options;if(e instanceof eT)return a.objectValues(e.enum);else if(e instanceof eS)return eh(e._def.innerType);else if(e instanceof es)return[void 0];else if(e instanceof ea)return[null];else if(e instanceof eZ)return[void 0,...eh(e.unwrap())];else if(e instanceof eC)return[null,...eh(e.unwrap())];else if(e instanceof eN)return eh(e.unwrap());else if(e instanceof eP)return eh(e.unwrap());else if(e instanceof eI)return eh(e._def.innerType);else return[]};class ep extends E{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==o.object)return b(t,{code:c.invalid_type,expected:o.object,received:t.parsedType}),x;let r=this.discriminator,s=t.data[r],a=this.optionsMap.get(s);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(b(t,{code:c.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),x)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let s=new Map;for(let r of t){let t=eh(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let a of t){if(s.has(a))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(a)}`);s.set(a,r)}}return new ep({typeName:d.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:s,...A(r)})}}class ef extends E{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),s=(e,s)=>{if(O(e)||O(s))return x;let i=function e(t,r){let s=l(t),i=l(r);if(t===r)return{valid:!0,data:t};if(s===o.object&&i===o.object){let s=a.objectKeys(r),i=a.objectKeys(t).filter(e=>-1!==s.indexOf(e)),n={...t,...r};for(let s of i){let a=e(t[s],r[s]);if(!a.valid)return{valid:!1};n[s]=a.data}return{valid:!0,data:n}}if(s===o.array&&i===o.array){if(t.length!==r.length)return{valid:!1};let s=[];for(let a=0;a<t.length;a++){let i=e(t[a],r[a]);if(!i.valid)return{valid:!1};s.push(i.data)}return{valid:!0,data:s}}if(s===o.date&&i===o.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,s.value);return i.valid?((R(e)||R(s))&&t.dirty(),{status:t.value,value:i.data}):(b(r,{code:c.invalid_intersection_types}),x)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>s(e,t)):s(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}ef.create=(e,t,r)=>new ef({left:e,right:t,typeName:d.ZodIntersection,...A(r)});class em extends E{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==o.array)return b(r,{code:c.invalid_type,expected:o.array,received:r.parsedType}),x;if(r.data.length<this._def.items.length)return b(r,{code:c.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),x;!this._def.rest&&r.data.length>this._def.items.length&&(b(r,{code:c.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let s=[...r.data].map((e,t)=>{let s=this._def.items[t]||this._def.rest;return s?s._parse(new S(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(s).then(e=>k.mergeArray(t,e)):k.mergeArray(t,s)}get items(){return this._def.items}rest(e){return new em({...this._def,rest:e})}}em.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new em({items:e,typeName:d.ZodTuple,rest:null,...A(t)})};class ey extends E{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==o.object)return b(r,{code:c.invalid_type,expected:o.object,received:r.parsedType}),x;let s=[],a=this._def.keyType,i=this._def.valueType;for(let e in r.data)s.push({key:a._parse(new S(r,e,r.path,e)),value:i._parse(new S(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?k.mergeObjectAsync(t,s):k.mergeObjectSync(t,s)}get element(){return this._def.valueType}static create(e,t,r){return new ey(t instanceof E?{keyType:e,valueType:t,typeName:d.ZodRecord,...A(r)}:{keyType:J.create(),valueType:e,typeName:d.ZodRecord,...A(t)})}}class ev extends E{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==o.map)return b(r,{code:c.invalid_type,expected:o.map,received:r.parsedType}),x;let s=this._def.keyType,a=this._def.valueType,i=[...r.data.entries()].map(([e,t],i)=>({key:s._parse(new S(r,e,r.path,[i,"key"])),value:a._parse(new S(r,t,r.path,[i,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of i){let s=await r.key,a=await r.value;if("aborted"===s.status||"aborted"===a.status)return x;("dirty"===s.status||"dirty"===a.status)&&t.dirty(),e.set(s.value,a.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of i){let s=r.key,a=r.value;if("aborted"===s.status||"aborted"===a.status)return x;("dirty"===s.status||"dirty"===a.status)&&t.dirty(),e.set(s.value,a.value)}return{status:t.value,value:e}}}}ev.create=(e,t,r)=>new ev({valueType:t,keyType:e,typeName:d.ZodMap,...A(r)});class e_ extends E{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==o.set)return b(r,{code:c.invalid_type,expected:o.set,received:r.parsedType}),x;let s=this._def;null!==s.minSize&&r.data.size<s.minSize.value&&(b(r,{code:c.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),t.dirty()),null!==s.maxSize&&r.data.size>s.maxSize.value&&(b(r,{code:c.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),t.dirty());let a=this._def.valueType;function i(e){let r=new Set;for(let s of e){if("aborted"===s.status)return x;"dirty"===s.status&&t.dirty(),r.add(s.value)}return{status:t.value,value:r}}let n=[...r.data.values()].map((e,t)=>a._parse(new S(r,e,r.path,t)));return r.common.async?Promise.all(n).then(e=>i(e)):i(n)}min(e,t){return new e_({...this._def,minSize:{value:e,message:n.toString(t)}})}max(e,t){return new e_({...this._def,maxSize:{value:e,message:n.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}e_.create=(e,t)=>new e_({valueType:e,minSize:null,maxSize:null,typeName:d.ZodSet,...A(t)});class eg extends E{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==o.function)return b(t,{code:c.invalid_type,expected:o.function,received:t.parsedType}),x;function r(e,r){return _({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,m,f].filter(e=>!!e),issueData:{code:c.invalid_arguments,argumentsError:r}})}function s(e,r){return _({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,m,f].filter(e=>!!e),issueData:{code:c.invalid_return_type,returnTypeError:r}})}let a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof eO){let e=this;return T(async function(...t){let n=new p([]),d=await e._def.args.parseAsync(t,a).catch(e=>{throw n.addIssue(r(t,e)),n}),u=await Reflect.apply(i,this,d);return await e._def.returns._def.type.parseAsync(u,a).catch(e=>{throw n.addIssue(s(u,e)),n})})}{let e=this;return T(function(...t){let n=e._def.args.safeParse(t,a);if(!n.success)throw new p([r(t,n.error)]);let d=Reflect.apply(i,this,n.data),u=e._def.returns.safeParse(d,a);if(!u.success)throw new p([s(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new eg({...this._def,args:em.create(e).rest(en.create())})}returns(e){return new eg({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new eg({args:e||em.create([]).rest(en.create()),returns:t||en.create(),typeName:d.ZodFunction,...A(r)})}}class eb extends E{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}eb.create=(e,t)=>new eb({getter:e,typeName:d.ZodLazy,...A(t)});class ek extends E{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return b(t,{received:t.data,code:c.invalid_literal,expected:this._def.value}),x}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ex(e,t){return new ew({values:e,typeName:d.ZodEnum,...A(t)})}ek.create=(e,t)=>new ek({value:e,typeName:d.ZodLiteral,...A(t)});class ew extends E{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return b(t,{expected:a.joinValues(r),received:t.parsedType,code:c.invalid_type}),x}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return b(t,{received:t.data,code:c.invalid_enum_value,options:r}),x}return T(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ew.create(e,{...this._def,...t})}exclude(e,t=this._def){return ew.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}ew.create=ex;class eT extends E{_parse(e){let t=a.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==o.string&&r.parsedType!==o.number){let e=a.objectValues(t);return b(r,{expected:a.joinValues(e),received:r.parsedType,code:c.invalid_type}),x}if(this._cache||(this._cache=new Set(a.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=a.objectValues(t);return b(r,{received:r.data,code:c.invalid_enum_value,options:e}),x}return T(e.data)}get enum(){return this._def.values}}eT.create=(e,t)=>new eT({values:e,typeName:d.ZodNativeEnum,...A(t)});class eO extends E{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==o.promise&&!1===t.common.async?(b(t,{code:c.invalid_type,expected:o.promise,received:t.parsedType}),x):T((t.parsedType===o.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eO.create=(e,t)=>new eO({type:e,typeName:d.ZodPromise,...A(t)});class eR extends E{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===d.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),s=this._def.effect||null,i={addIssue:e=>{b(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===s.type){let e=s.transform(r.data,i);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return x;let s=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===s.status?x:"dirty"===s.status||"dirty"===t.value?w(s.value):s});{if("aborted"===t.value)return x;let s=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===s.status?x:"dirty"===s.status||"dirty"===t.value?w(s.value):s}}if("refinement"===s.type){let e=e=>{let t=s.refinement(e,i);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?x:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let s=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===s.status?x:("dirty"===s.status&&t.dirty(),e(s.value),{status:t.value,value:s.value})}}if("transform"===s.type)if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>Z(e)?Promise.resolve(s.transform(e.value,i)).then(e=>({status:t.value,value:e})):x);else{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!Z(e))return x;let a=s.transform(e.value,i);if(a instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:a}}a.assertNever(s)}}eR.create=(e,t,r)=>new eR({schema:e,typeName:d.ZodEffects,effect:t,...A(r)}),eR.createWithPreprocess=(e,t,r)=>new eR({schema:t,effect:{type:"preprocess",transform:e},typeName:d.ZodEffects,...A(r)});class eZ extends E{_parse(e){return this._getType(e)===o.undefined?T(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eZ.create=(e,t)=>new eZ({innerType:e,typeName:d.ZodOptional,...A(t)});class eC extends E{_parse(e){return this._getType(e)===o.null?T(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eC.create=(e,t)=>new eC({innerType:e,typeName:d.ZodNullable,...A(t)});class eS extends E{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===o.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eS.create=(e,t)=>new eS({innerType:e,typeName:d.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...A(t)});class eI extends E{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},s=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return C(s)?s.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new p(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===s.status?s.value:this._def.catchValue({get error(){return new p(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}eI.create=(e,t)=>new eI({innerType:e,typeName:d.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...A(t)});class eA extends E{_parse(e){if(this._getType(e)!==o.nan){let t=this._getOrReturnCtx(e);return b(t,{code:c.invalid_type,expected:o.nan,received:t.parsedType}),x}return{status:"valid",value:e.data}}}eA.create=e=>new eA({typeName:d.ZodNaN,...A(e)});let eE=Symbol("zod_brand");class eN extends E{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class ej extends E{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?x:"dirty"===e.status?(t.dirty(),w(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?x:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new ej({in:e,out:t,typeName:d.ZodPipeline})}}class eP extends E{_parse(e){let t=this._def.innerType._parse(e),r=e=>(Z(e)&&(e.value=Object.freeze(e.value)),e);return C(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}function eF(e,t){let r="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof r?{message:r}:r}function e$(e,t={},r){return e?ei.create().superRefine((s,a)=>{let i=e(s);if(i instanceof Promise)return i.then(e=>{if(!e){let e=eF(t,s),i=e.fatal??r??!0;a.addIssue({code:"custom",...e,fatal:i})}});if(!i){let e=eF(t,s),i=e.fatal??r??!0;a.addIssue({code:"custom",...e,fatal:i})}}):ei.create()}eP.create=(e,t)=>new eP({innerType:e,typeName:d.ZodReadonly,...A(t)});let eD={object:el.lazycreate};!function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(d||(d={}));let eM=(e,t={message:`Input not instance of ${e.name}`})=>e$(t=>t instanceof e,t),eU=J.create,eQ=Y.create,eL=eA.create,ez=X.create,eq=ee.create,eV=et.create,eK=er.create,eW=es.create,eB=ea.create,eG=ei.create,eH=en.create,eJ=ed.create,eY=eu.create,eX=eo.create,e0=el.create,e1=el.strictCreate,e9=ec.create,e4=ep.create,e2=ef.create,e5=em.create,e3=ey.create,e6=ev.create,e8=e_.create,e7=eg.create,te=eb.create,tt=ek.create,tr=ew.create,ts=eT.create,ta=eO.create,ti=eR.create,tn=eZ.create,td=eC.create,tu=eR.createWithPreprocess,to=ej.create,tl=()=>eU().optional(),tc=()=>eQ().optional(),th=()=>eq().optional(),tp={string:e=>J.create({...e,coerce:!0}),number:e=>Y.create({...e,coerce:!0}),boolean:e=>ee.create({...e,coerce:!0}),bigint:e=>X.create({...e,coerce:!0}),date:e=>et.create({...e,coerce:!0})},tf=x}}]);