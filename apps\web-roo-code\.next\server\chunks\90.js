"use strict";exports.id=90,exports.ids=[90],exports.modules={818:(e,o,t)=>{t.r(o),t.d(o,{InstallSection:()=>l});var r=t(3641),n=t(4326),s=t(40644),a=t.n(s),i=t(95850);function l({downloads:e}){return(0,r.jsxs)("section",{className:"relative overflow-hidden border-t border-border py-16 sm:py-24 lg:py-32",children:[(0,r.jsx)(i.P.div,{className:"absolute inset-x-0 top-1/2 -translate-y-1/2",initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1.2,ease:"easeOut"}}},children:(0,r.jsx)("div",{className:"relative mx-auto max-w-[1200px]",children:(0,r.jsx)("div",{className:"absolute left-1/2 top-1/2 h-[500px] w-[700px] -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"})})}),(0,r.jsx)("div",{className:"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"mx-auto max-w-3xl text-center",children:[(0,r.jsx)("h2",{className:"text-center text-xl font-semibold uppercase tracking-wider text-muted-foreground sm:text-2xl",children:"Install Roo Code — Open & Flexible"}),(0,r.jsx)("p",{className:"mt-4 text-center text-base text-muted-foreground sm:mt-6 sm:text-lg",children:"Roo Code is open-source, model-agnostic, and developer-focused. Install from the VS Code Marketplace or the CLI in minutes, then bring your own AI model."}),(0,r.jsxs)("div",{className:"mt-10 flex flex-col items-center justify-center gap-6",children:[(0,r.jsxs)(a(),{href:"https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline",target:"_blank",className:"group relative inline-flex w-full items-center justify-center gap-2 rounded-xl border border-border/50 bg-background/30 px-4 py-3 text-base backdrop-blur-xl transition-all duration-300 hover:border-border hover:bg-background/40 sm:w-auto sm:gap-3 sm:px-6 sm:py-4 sm:text-lg md:text-2xl",children:[(0,r.jsx)("div",{className:"absolute -inset-px rounded-xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"}),(0,r.jsxs)("div",{className:"relative flex items-center gap-2 sm:gap-3",children:[(0,r.jsx)(n.$$9,{className:"h-5 w-5 text-blue-400 sm:h-6 sm:w-6 md:h-8 md:w-8"}),(0,r.jsxs)("span",{className:"flex flex-wrap items-center gap-1 sm:gap-2 md:gap-3",children:[(0,r.jsx)("span",{className:"text-foreground/90",children:"VSCode Marketplace"}),null!==e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"hidden font-black text-muted-foreground sm:inline",children:"\xb7"}),(0,r.jsxs)("span",{className:"text-muted-foreground",children:[e," Downloads"]})]})]})]})]}),(0,r.jsxs)("div",{className:"group relative w-full max-w-xl",children:[(0,r.jsx)("div",{className:"absolute -inset-px rounded-xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"}),(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-border group-hover:bg-background/40",children:[(0,r.jsx)("div",{className:"border-b border-border/50 px-3 py-2 sm:px-4",children:(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"Install via CLI"})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsx)("pre",{className:"p-3 sm:p-4",children:(0,r.jsx)("code",{className:"whitespace-pre-wrap break-all text-sm text-foreground/90 sm:break-normal",children:"code --install-extension RooVeterinaryInc.roo-cline"})})})]})]})]})]})})]})}},6007:(e,o,t)=>{t.r(o),t.d(o,{AnimatedBackground:()=>r});let r=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call AnimatedBackground() from the server but AnimatedBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\animated-background.tsx","AnimatedBackground")},17004:(e,o,t)=>{t.d(o,{Q9:()=>r.AnimatedBackground,Gv:()=>n.CodeExample,YS:()=>s.FAQSection,O5:()=>a.Features,$i:()=>i.InstallSection,bZ:()=>l.Testimonials});var r=t(6007),n=t(77533);t(46477);var s=t(60833);t(39183);var a=t(87262),i=t(56012);t(90811);var l=t(86587);t(67082)},20919:(e,o,t)=>{t.d(o,{AnimatedText:()=>s});var r=t(3641),n=t(95850);function s({children:e,className:o}){return(0,r.jsx)(n.P.span,{className:o,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,ease:[.2,.65,.3,.9]},children:e})}},30141:(e,o,t)=>{t.r(o),t.d(o,{ScrollButton:()=>r});let r=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call ScrollButton() from the server but ScrollButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\scroll-button.tsx","ScrollButton")},39183:(e,o,t)=>{t.r(o),t.d(o,{FeaturesMobile:()=>r});let r=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call FeaturesMobile() from the server but FeaturesMobile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\features-mobile.tsx","FeaturesMobile")},42463:(e,o,t)=>{t.r(o),t.d(o,{CodeExample:()=>a});var r=t(3641),n=t(44508),s=t(95850);function a(){let[e,o]=(0,n.useState)("code"),[t,a]=(0,n.useState)(!1),[i,l]=(0,n.useState)(""),[d,c]=(0,n.useState)(0),m=(0,n.useRef)(null),p=e=>{o(e),l(""),c(0),a(!0),m.current&&(m.current.scrollTop=0)};return(0,r.jsx)("div",{className:"relative z-10 w-full max-w-[90vw] rounded-lg border border-border bg-background/50 p-2 shadow-2xl backdrop-blur-sm sm:max-w-[500px]",children:(0,r.jsxs)("div",{className:"rounded-md bg-muted p-1.5 dark:bg-gray-900 sm:p-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between border-b border-border px-2 py-1.5 sm:px-3 sm:py-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,r.jsx)("div",{className:"h-2.5 w-2.5 rounded-full bg-red-500 sm:h-3 sm:w-3"}),(0,r.jsx)("div",{className:"h-2.5 w-2.5 rounded-full bg-yellow-500 sm:h-3 sm:w-3"}),(0,r.jsx)("div",{className:"h-2.5 w-2.5 rounded-full bg-green-500 sm:h-3 sm:w-3"})]}),(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("button",{onClick:()=>p("code"),className:`rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm ${"code"===e?"bg-blue-500/20 text-blue-400":"text-gray-400 hover:bg-gray-800"}`,children:"Code"}),(0,r.jsx)("button",{onClick:()=>p("architect"),className:`rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm ${"architect"===e?"bg-purple-500/20 text-purple-400":"text-gray-400 hover:bg-gray-800"}`,children:"Architect"}),(0,r.jsx)("button",{onClick:()=>p("debug"),className:`rounded px-2 py-0.5 text-xs font-medium transition-colors sm:text-sm ${"debug"===e?"bg-green-500/20 text-green-400":"text-gray-400 hover:bg-gray-800"}`,children:"Debug"})]})]}),(0,r.jsx)("div",{className:"p-2 sm:p-4",children:(0,r.jsx)("pre",{ref:m,className:"scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent overflow-y-hidden text-xs text-muted-foreground max-lg:h-[25vh] sm:text-sm lg:max-h-[50vh]",children:(0,r.jsxs)("code",{className:"block whitespace-pre font-mono",children:[i,t&&(0,r.jsx)(s.P.span,{animate:{opacity:[1,0]},transition:{repeat:Number.POSITIVE_INFINITY,duration:.8},className:"inline-block h-4 w-2 bg-blue-500"})]})})})]})})}},44953:(e,o,t)=>{t.r(o),t.d(o,{ChartContainer:()=>n,ChartLegend:()=>i,ChartLegendContent:()=>l,ChartStyle:()=>d,ChartTooltip:()=>s,ChartTooltipContent:()=>a});var r=t(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call ChartContainer() from the server but ChartContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\chart.tsx","ChartContainer"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call ChartTooltip() from the server but ChartTooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\chart.tsx","ChartTooltip"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ChartTooltipContent() from the server but ChartTooltipContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\chart.tsx","ChartTooltipContent"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call ChartLegend() from the server but ChartLegend is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\chart.tsx","ChartLegend"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call ChartLegendContent() from the server but ChartLegendContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\chart.tsx","ChartLegendContent"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call ChartStyle() from the server but ChartStyle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\chart.tsx","ChartStyle")},45693:(e,o,t)=>{t.d(o,{AnimatedText:()=>r});let r=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call AnimatedText() from the server but AnimatedText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\animated-text.tsx","AnimatedText")},46477:(e,o,t)=>{t.r(o),t.d(o,{CompanyLogos:()=>r});let r=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call CompanyLogos() from the server but CompanyLogos is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\company-logos.tsx","CompanyLogos")},50092:(e,o,t)=>{t.r(o),t.d(o,{Features:()=>l,features:()=>i});var r=t(3641),n=t(95850),s=t(37739),a=t(94425);let i=[{icon:(0,r.jsx)(s.y8Q,{className:"h-6 w-6"}),title:"Your AI Dev Team in VS Code",description:"Roo Code puts a team of agentic AI assistants directly in your editor, with the power to plan, write, and fix code across multiple files.",size:"large"},{icon:(0,r.jsx)(s.FSj,{className:"h-6 w-6"}),title:"Multiple Specialized Modes",description:"From coding to debugging to architecture, Roo Code has a mode for every dev scenario—just switch on the fly.",size:"small"},{icon:(0,r.jsx)(s.KuA,{className:"h-6 w-6"}),title:"Deep Project-wide Context",description:"Roo Code reads your entire codebase, preserving valid code through diff-based edits for seamless multi-file refactors.",size:"small"},{icon:(0,r.jsx)(s.xdT,{className:"h-6 w-6"}),title:"Open-Source and Model-Agnostic",description:"Bring your own model or use local AI—no vendor lock-in. Roo Code is free, open, and adaptable to your needs.",size:"large"},{icon:(0,r.jsx)(s.iuJ,{className:"h-6 w-6"}),title:"Guarded Command Execution",description:"Approve or deny commands as needed. Roo Code automates your dev workflow while keeping oversight firmly in your hands.",size:"small"},{icon:(0,r.jsx)(s.yRn,{className:"h-6 w-6"}),title:"Fully Customizable",description:"Create or tweak modes, define usage rules, and shape Roo Code’s behavior precisely—your code, your way.",size:"small"},{icon:(0,r.jsx)(s.f35,{className:"h-6 w-6"}),title:"Automated Browser Actions",description:"Seamlessly test and verify your web app directly from VS Code—Roo Code can open a browser, run checks, and more.",size:"small"}];function l(){let e={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:[.21,.45,.27,.9]}}};return(0,r.jsxs)("section",{className:"relative overflow-hidden border-t border-border py-32",children:[(0,r.jsx)(n.P.div,{className:"absolute inset-0",initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1.2,ease:"easeOut"}}},children:(0,r.jsx)("div",{className:"absolute inset-y-0 left-1/2 h-full w-full max-w-[1200px] -translate-x-1/2",children:(0,r.jsx)("div",{className:"absolute left-1/2 top-1/2 h-[800px] w-full -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"})})}),(0,r.jsxs)("div",{className:"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mx-auto mb-24 max-w-3xl text-center",children:(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,ease:[.21,.45,.27,.9]},children:[(0,r.jsx)("h2",{className:"bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl",children:"Powerful features for modern developers."}),(0,r.jsx)("p",{className:"mt-6 text-lg text-muted-foreground",children:"Everything you need to build faster and write better code."})]})}),(0,r.jsx)(a.FeaturesMobile,{}),(0,r.jsx)(n.P.div,{className:"relative mx-auto hidden max-w-[1200px] md:block",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.3}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:(0,r.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-8",children:i.map((o,t)=>(0,r.jsxs)(n.P.div,{variants:e,className:`group relative ${"large"===o.size?"lg:col-span-2":""} ${t%2==0?"lg:translate-y-12":""}`,children:[(0,r.jsx)("div",{className:"absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100"}),(0,r.jsxs)("div",{className:"relative h-full rounded-2xl border border-border/50 bg-background/30 p-8 backdrop-blur-xl transition-colors duration-300 hover:border-border",children:[(0,r.jsx)("div",{className:"mb-5 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5",children:(0,r.jsx)("div",{className:"rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5",children:(0,r.jsx)("div",{className:"text-foreground/90",children:o.icon})})}),(0,r.jsx)("h3",{className:"mb-3 text-xl font-medium text-foreground/90",children:o.title}),(0,r.jsx)("p",{className:"leading-relaxed text-muted-foreground",children:o.description})]})]},t))})})]})]})}},50793:(e,o,t)=>{t.r(o),t.d(o,{Testimonials:()=>m,testimonials:()=>c});var r=t(3641),n=t(44508),s=t(95850),a=t(19876),i=t(7729),l=t(67005);function d(){let[e]=(0,i.A)({loop:!0},[(0,l.A)({playOnInit:!0,speed:1,stopOnInteraction:!0,stopOnMouseEnter:!0})]);return(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)("div",{className:"overflow-hidden px-4",ref:e,children:(0,r.jsx)("div",{className:"flex",children:c.map(e=>(0,r.jsx)("div",{className:"min-w-0 flex-[0_0_100%] px-4",children:(0,r.jsxs)("div",{className:"relative py-8",children:[(0,r.jsx)("svg",{className:"absolute left-0 top-0 h-8 w-8 text-blue-500/30",fill:"currentColor",viewBox:"0 0 32 32",children:(0,r.jsx)("path",{d:"M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z"})}),(0,r.jsxs)("blockquote",{className:"mt-8",children:[(0,r.jsxs)("p",{className:"text-lg font-light italic leading-relaxed text-muted-foreground",children:['"',e.quote,'"']}),(0,r.jsxs)("footer",{className:"mt-6",children:[(0,r.jsx)("div",{className:"h-px w-12 bg-gradient-to-r from-blue-500/30 to-transparent"}),(0,r.jsx)("p",{className:"mt-4 font-medium text-foreground/90",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.role," at ",e.company]})]})]})]})},e.id))})})})}let c=[{id:1,name:"Luca",role:"Reviewer",company:"VS Code Marketplace",quote:"Roo Code is an absolute game-changer! \uD83D\uDE80 It makes coding faster, easier, and more intuitive with its smart AI-powered suggestions, real-time debugging, and automation features. The seamless integration with VS Code is a huge plus, and the constant updates ensure it keeps getting better"},{id:2,name:"Taro Woollett-Chiba",role:"AI Product Lead",company:"Vendidit",quote:"Easily the best AI code editor. Roo Code has the best features and capabilities, along with the best development team. I swear, they're the fastest to support new models and implement useful functionality whenever users mention it... simply amazing."},{id:3,name:"Can Nuri",role:"Reviewer",company:"VS Code Marketplace",quote:"Roo Code is one of the most inspiring projects I have seen for a long time. It shapes the way I think and deal with software development."},{id:4,name:"Michael",role:"Reviewer",company:"VS Code Marketplace",quote:"I switched from Windsurf to Roo Code in January and honestly, it's been a huge upgrade. Windsurf kept making mistakes and being dumb when I ask it for things. Roo just gets it. Projects that used to take a full day now wrap up before lunch. "}];function m(){let e=(0,n.useRef)(null),o={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6,ease:[.21,.45,.27,.9]}}};return(0,r.jsxs)("section",{ref:e,className:"relative overflow-hidden border-t border-border py-32",children:[(0,r.jsx)(s.P.div,{className:"absolute inset-0",initial:"hidden",whileInView:"visible",viewport:{once:!0},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1.2,ease:"easeOut"}}},children:(0,r.jsx)("div",{className:"absolute inset-y-0 left-1/2 h-full w-full max-w-[1200px] -translate-x-1/2",children:(0,r.jsx)("div",{className:"absolute left-1/2 top-1/2 h-[800px] w-full -translate-x-1/2 -translate-y-1/2 rounded-[100%] bg-blue-500/10 blur-[120px]"})})}),(0,r.jsxs)("div",{className:"container relative z-10 mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mx-auto mb-24 max-w-3xl text-center",children:(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,ease:[.21,.45,.27,.9]},children:[(0,r.jsx)("h2",{className:"bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl",children:"Empowering developers worldwide."}),(0,r.jsx)("p",{className:"mt-6 text-lg text-muted-foreground",children:"Join thousands of developers who are revolutionizing their workflow with AI-powered assistance."})]})}),(0,r.jsx)(d,{}),(0,r.jsx)(s.P.div,{className:"relative mx-auto hidden max-w-[1200px] md:block",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.3}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:(0,r.jsx)("div",{className:"relative grid grid-cols-1 gap-12 md:grid-cols-2",children:c.map((e,t)=>(0,r.jsxs)(s.P.div,{variants:o,className:`group relative ${t%2==0?"md:translate-y-4":"md:translate-y-12"}`,children:[(0,r.jsx)("div",{className:"absolute -inset-px rounded-2xl bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-purple-500/30 opacity-0 blur-sm transition-all duration-500 ease-out group-hover:opacity-100"}),(0,r.jsxs)("div",{className:"relative h-full rounded-2xl border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-500 ease-out group-hover:border-border group-hover:bg-background/40",children:[e.image&&(0,r.jsx)("div",{className:"absolute -right-3 -top-3 h-16 w-16 overflow-hidden rounded-xl border border-border/50 bg-background/50 p-1.5 backdrop-blur-xl transition-all duration-500 ease-out group-hover:scale-105",children:(0,r.jsx)("div",{className:"relative h-full w-full overflow-hidden rounded-lg",children:(0,r.jsx)(a.default,{src:e.image||"/placeholder_pfp.png",alt:e.name,fill:!0,className:"object-cover"})})}),(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("svg",{className:"h-8 w-8 text-blue-500/20",fill:"currentColor",viewBox:"0 0 32 32",children:(0,r.jsx)("path",{d:"M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z"})})}),(0,r.jsx)("p",{className:"relative mb-6 text-lg leading-relaxed text-muted-foreground",children:e.quote}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"mb-4 h-px w-12 bg-gradient-to-r from-blue-500/50 to-transparent"}),(0,r.jsx)("h3",{className:"font-medium text-foreground/90",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.role," at ",e.company]})]})]})]})]},e.id))})})]})]})}},51975:(e,o,t)=>{t.r(o),t.d(o,{CompanyLogos:()=>s});var r=t(3641),n=t(95850);function s(){return(0,r.jsx)("div",{className:"mt-10",children:(0,r.jsx)("div",{className:"mx-auto grid max-w-5xl grid-cols-2 gap-8 py-8 md:grid-cols-3 lg:grid-cols-6",children:[{name:"Company 1",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 2",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 3",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 4",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 5",logo:"/placeholder.svg?height=40&width=120"},{name:"Company 6",logo:"/placeholder.svg?height=40&width=120"}].map((e,o)=>(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*o,ease:"easeOut"},className:"flex items-center justify-center",children:(0,r.jsx)("img",{src:e.logo||"/placeholder.svg",alt:e.name,className:"h-10 w-auto opacity-70 grayscale transition-all duration-300 hover:opacity-100 hover:grayscale-0"})},o))})})}},56012:(e,o,t)=>{t.r(o),t.d(o,{InstallSection:()=>r});let r=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call InstallSection() from the server but InstallSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\install-section.tsx","InstallSection")},56997:(e,o,t)=>{t.r(o),t.d(o,{AnimatedBackground:()=>s});var r=t(3641),n=t(44508);function s(){let e=(0,n.useRef)(null);return(0,r.jsx)("canvas",{ref:e,className:"absolute inset-0 h-full w-full",style:{zIndex:0}})}},60833:(e,o,t)=>{t.r(o),t.d(o,{FAQSection:()=>r});let r=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call FAQSection() from the server but FAQSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\faq-section.tsx","FAQSection")},67082:(e,o,t)=>{t.r(o),t.d(o,{WhatsNewButton:()=>r});let r=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call WhatsNewButton() from the server but WhatsNewButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\whats-new-button.tsx","WhatsNewButton")},67144:(e,o,t)=>{t.d(o,{$n:()=>m});var r=t(90811),n=t(4446),s=t(32290),a=t(26199),i=t(71537),l=t(82612);function d(...e){return(0,l.QP)((0,i.$)(e))}let c=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),m=n.forwardRef(({className:e,variant:o,size:t,asChild:n=!1,...a},i)=>{let l=n?s.DX:"button";return(0,r.jsx)(l,{className:d(c({variant:o,size:t,className:e})),ref:i,...a})});m.displayName="Button",t(44953),t(96182),t(30141),n.forwardRef(({className:e,...o},t)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:d("w-full caption-bottom text-sm",e),...o})})).displayName="Table",n.forwardRef(({className:e,...o},t)=>(0,r.jsx)("thead",{ref:t,className:d("[&_tr]:border-b",e),...o})).displayName="TableHeader",n.forwardRef(({className:e,...o},t)=>(0,r.jsx)("tbody",{ref:t,className:d("[&_tr:last-child]:border-0",e),...o})).displayName="TableBody",n.forwardRef(({className:e,...o},t)=>(0,r.jsx)("tfoot",{ref:t,className:d("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...o})).displayName="TableFooter",n.forwardRef(({className:e,...o},t)=>(0,r.jsx)("tr",{ref:t,className:d("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...o})).displayName="TableRow",n.forwardRef(({className:e,...o},t)=>(0,r.jsx)("th",{ref:t,className:d("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...o})).displayName="TableHead",n.forwardRef(({className:e,...o},t)=>(0,r.jsx)("td",{ref:t,className:d("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...o})).displayName="TableCell",n.forwardRef(({className:e,...o},t)=>(0,r.jsx)("caption",{ref:t,className:d("mt-4 text-sm text-muted-foreground",e),...o})).displayName="TableCaption"},77533:(e,o,t)=>{t.r(o),t.d(o,{CodeExample:()=>r});let r=(0,t(87741).registerClientReference)(function(){throw Error("Attempted to call CodeExample() from the server but CodeExample is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\code-example.tsx","CodeExample")},79051:(e,o,t)=>{t.r(o),t.d(o,{FAQSection:()=>d});var r=t(3641),n=t(44508),s=t(95850),a=t(84054),i=t(10244);let l=[{question:"What exactly is Roo Code?",answer:"Roo Code is an open-source, AI-powered coding assistant that runs in VS Code. It goes beyond simple autocompletion by reading and writing across multiple files, executing commands, and adapting to your workflow—like having a whole dev team right inside your editor."},{question:"How does Roo Code differ from Copilot, Cursor, or Windsurf?",answer:"Open & Customizable: Roo Code is open-source and allows you to integrate any AI model (OpenAI, Anthropic, local LLMs, etc.). Multi-File Edits: It can read, refactor, and update multiple files at once for more holistic changes. Agentic Abilities: Roo Code can run tests, open a browser, or do deeper tasks than a typical AI autocomplete. Permission-Based: You control and approve any file changes or command executions."},{question:"Is Roo Code really free?",answer:"Yes! Roo Code is completely free and open-source. You'll only pay for the AI model usage if you use a paid API (like OpenAI). If you choose free or self-hosted models, there's no cost at all."},{question:"Will my code stay private?",answer:"Yes. Because Roo Code is an extension in your local VS Code, your code never leaves your machine unless you connect to an external AI API. Even then, you control exactly what is sent to the AI model. You can use tools like .rooignore to exclude sensitive files, and you can also run Roo Code with offline/local models for full privacy."},{question:"Which AI models does Roo Code support?",answer:"Roo Code is model-agnostic. It works with: OpenAI models (GPT-3.5, GPT-4, etc.), Anthropic Claude, Local LLMs (through APIs or special plugins), Any other API that follows Roo Code's Model Context Protocol (MCP)."},{question:"Does Roo Code support my programming language?",answer:"Likely yes! Roo Code supports a wide range of languages—Python, Java, C#, JavaScript/TypeScript, Go, Rust, etc. Since it leverages the AI model's understanding, new or lesser-known languages may also work, depending on model support."},{question:"How do I install and get started?",answer:"Install Roo Code from the VS Code Marketplace (or GitHub). Add your AI keys (OpenAI, Anthropic, or other) in the extension settings. Open the Roo panel (the rocket icon) in VS Code, and start typing commands in plain English!"},{question:"Can it handle large, enterprise-scale projects?",answer:"Absolutely. Roo Code uses efficient strategies (like partial-file analysis, summarization, or user-specified context) to handle large codebases. Enterprises especially appreciate the on-prem or self-hosted model option for compliance and security needs."},{question:"Is it safe for enterprise use?",answer:"Yes. Roo Code was designed with enterprise in mind: Self-host AI models or choose your own provider. Permission gating on file writes and commands. Auditable: The entire code is open-source, so you know exactly how it operates."},{question:"Can Roo Code run commands and tests automatically?",answer:"Yes! One of Roo Code's superpowers is command execution (optional and fully permission-based). It can: Run npm install or any terminal command you grant permission for. Execute your test suites. Open a web browser for integration tests."},{question:"What if I just want a casual coding 'vibe'?",answer:'Roo Code shines for both serious enterprise development and casual "vibe coding." You can ask it to quickly prototype ideas, refactor on the fly, or provide design suggestions—without a rigid, step-by-step process.'},{question:"Can I contribute to Roo Code?",answer:"Yes, please do! Roo Code is open-source on GitHub. Submit issues, suggest features, or open a pull request. There's also an active community on Discord and Reddit if you want to share feedback or help others."},{question:"Where can I learn more or get help?",answer:"Check out: Official Documentation for setup and advanced guides. Discord & Reddit channels for community support. YouTube tutorials and blog posts from fellow developers showcasing real-world usage."}];function d(){let[e,o]=(0,n.useState)(null),t=t=>{o(e===t?null:t)};return(0,r.jsx)("section",{id:"faq-section",className:"border-t border-border py-20",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"mx-auto mb-24 max-w-3xl text-center",children:(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.6,ease:[.21,.45,.27,.9]},children:[(0,r.jsx)("h2",{className:"bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl",children:"Frequently Asked Questions"}),(0,r.jsx)("p",{className:"mt-6 text-lg text-muted-foreground",children:"Everything you need to know about Roo Code and how it can transform your development workflow."})]})}),(0,r.jsx)("div",{className:"mx-auto max-w-3xl",children:(0,r.jsx)("div",{className:"space-y-4",children:l.map((o,n)=>(0,r.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.5,delay:.1*n,ease:[.21,.45,.27,.9]},children:(0,r.jsxs)("div",{className:"group relative rounded-lg border border-border/50 bg-background/30 backdrop-blur-xl transition-all duration-300 hover:border-border",children:[(0,r.jsxs)("button",{onClick:()=>t(n),className:"flex w-full items-center justify-between p-6 text-left","aria-expanded":e===n,children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-foreground/90",children:o.question}),(0,r.jsx)(a.A,{className:(0,i.cn)("h-5 w-5 text-muted-foreground transition-transform duration-200",e===n?"rotate-180":"")})]}),(0,r.jsx)("div",{className:(0,i.cn)("overflow-hidden transition-all duration-300 ease-in-out",e===n?"max-h-96 pb-6":"max-h-0"),children:(0,r.jsx)("div",{className:"px-6 text-muted-foreground",children:(0,r.jsx)("p",{children:o.answer})})})]})},n))})})]})})}},85856:(e,o,t)=>{t.r(o),t.d(o,{WhatsNewButton:()=>f});var r=t(3641),n=t(44508),s=t(63046),a=t(95850),i=t(85088),l=t(20574),d=t(69097),c=t(90631),m=t(30917),p=t(40644),u=t.n(p);function h({icon:e,color:o,title:t,description:n}){return(0,r.jsxs)("div",{className:"space-y-1.5 sm:space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 space-x-2",children:[(0,r.jsx)("div",{className:`rounded-full ${{blue:"bg-blue-500/20",purple:"bg-purple-500/20",green:"bg-green-500/20"}[o]} p-3 ${{blue:"text-blue-400",purple:"text-purple-400",green:"text-green-400"}[o]}`,children:(0,r.jsx)(e,{className:"h-6 w-6"})}),(0,r.jsx)("h3",{className:"text-base font-semibold sm:text-lg",children:t})]}),(0,r.jsx)("p",{className:"text-sm text-gray-400 sm:text-base",children:n})]})}let x="v3.8.0";function f(){let[e,o]=(0,n.useState)(!1),t=(0,n.useRef)(null),p=(0,n.useRef)(null);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"relative inline-flex",ref:t,children:[(0,r.jsx)("canvas",{ref:p,className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2",style:{pointerEvents:"none"}}),(0,r.jsxs)(u(),{href:"#",onClick:e=>{e.preventDefault(),o(!0)},className:"relative z-10 flex items-center space-x-2 rounded-full bg-black px-4 py-2 text-sm font-medium text-white transition-all hover:bg-gray-900",children:[(0,r.jsxs)("span",{children:["See what's new in ",x]}),(0,r.jsx)(i.A,{className:"h-3.5 w-3.5"})]})]}),(0,r.jsx)(s.N,{children:e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.P.div,{className:"fixed inset-0 z-40 bg-black/80 backdrop-blur-sm",initial:{opacity:0,backdropFilter:"blur(0px)"},animate:{opacity:1,backdropFilter:"blur(8px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},transition:{duration:.2}}),(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",onClick:()=>o(!1),children:(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,r.jsxs)(a.P.div,{className:"relative w-full max-w-2xl rounded-lg border border-gray-800 bg-black p-6 sm:p-8",initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:20,scale:.95},transition:{type:"spring",damping:20,stiffness:400,mass:.6,duration:.25},onClick:e=>{e.stopPropagation()},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold sm:text-2xl",children:["What's New in Roo Code ",x]}),(0,r.jsx)("button",{onClick:()=>o(!1),className:"flex-shrink-0 rounded-full p-1.5 text-gray-400 hover:bg-gray-800 hover:text-white",children:(0,r.jsx)(l.A,{className:"h-5 w-5"})})]}),(0,r.jsxs)("div",{className:"mt-4 space-y-4 sm:mt-6 sm:space-y-6",children:[(0,r.jsx)(h,{icon:d.A,color:"blue",title:"AI-Powered Code Generation",description:"Generate high-quality code snippets and entire components with our new AI assistant. Trained on millions of code repositories to understand your project context."}),(0,r.jsx)(h,{icon:c.A,color:"purple",title:"Real-time Collaboration",description:"Work together with your team in real-time with our new collaborative editing features. See changes as they happen and resolve conflicts automatically."}),(0,r.jsx)(h,{icon:m.A,color:"green",title:"Performance Optimizations",description:"We've completely rewritten our core engine for blazing fast performance. Experience up to 10x faster build times and smoother development workflow."})]})]})})})]})})]})}},86587:(e,o,t)=>{t.r(o),t.d(o,{Testimonials:()=>s,testimonials:()=>n});var r=t(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call testimonials() from the server but testimonials is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\testimonials.tsx","testimonials"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call Testimonials() from the server but Testimonials is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\testimonials.tsx","Testimonials")},87262:(e,o,t)=>{t.r(o),t.d(o,{Features:()=>s,features:()=>n});var r=t(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call features() from the server but features is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\features.tsx","features"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call Features() from the server but Features is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\homepage\\features.tsx","Features")},94425:(e,o,t)=>{t.r(o),t.d(o,{FeaturesMobile:()=>m});var r=t(3641),n=t(44508),s=t(7729),a=t(82085),i=t(99115),l=t(35783),d=t(95542),c=t(50092);function m(){let e=(0,a.A)({delay:5e3,stopOnInteraction:!0,stopOnMouseEnter:!0,rootNode:e=>e}),[o,t]=(0,s.A)({loop:!0,containScroll:"trimSnaps"},[e]),[m,p]=(0,n.useState)(0),[u,h]=(0,n.useState)([]),x=(0,n.useCallback)(e=>t&&t.scrollTo(e),[t]);return(0,n.useCallback)(e=>{h(e.scrollSnapList())},[]),(0,n.useCallback)(e=>{p(e.selectedScrollSnap())},[]),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)("div",{className:"relative px-4",children:[(0,r.jsx)("div",{className:"overflow-hidden",ref:o,children:(0,r.jsx)("div",{className:"flex",children:c.features.map((e,o)=>(0,r.jsx)("div",{className:"flex min-w-0 flex-[0_0_100%] px-4",children:(0,r.jsxs)("div",{className:"relative h-full min-h-[280px] rounded-2xl border border-border/50 bg-background/30 p-6 backdrop-blur-xl transition-colors duration-300 hover:border-border hover:bg-gray-900/20",children:[(0,r.jsx)("div",{className:"mb-2 inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-blue-500/5 to-cyan-500/5 p-2.5",children:(0,r.jsx)("div",{className:"rounded-lg bg-gradient-to-r from-blue-500/80 to-cyan-500/80 p-2.5",children:(0,r.jsx)("div",{className:"text-foreground/90",children:e.icon})})}),(0,r.jsx)("h3",{className:"mb-3 text-xl font-medium text-foreground/90",children:e.title}),(0,r.jsx)("p",{className:"leading-relaxed text-muted-foreground",children:e.description})]})},o))})}),(0,r.jsxs)("div",{className:"mt-6 flex items-center justify-between px-4",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(i.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-full border-border/50 bg-background/80 hover:bg-background",onClick:()=>t?.scrollPrev(),children:[(0,r.jsx)(l.A,{className:"h-4 w-4 text-foreground/80"}),(0,r.jsx)("span",{className:"sr-only",children:"Previous slide"})]}),(0,r.jsxs)(i.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-full border-border/50 bg-background/80 hover:bg-background",onClick:()=>t?.scrollNext(),children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-foreground/80"}),(0,r.jsx)("span",{className:"sr-only",children:"Next slide"})]})]}),(0,r.jsx)("div",{className:"flex gap-2",children:u.map((e,o)=>(0,r.jsx)("button",{type:"button",className:`h-3 w-3 rounded-full border border-border p-0 ${o===m?"bg-foreground":"bg-background"}`,onClick:()=>x(o),"aria-label":`Go to slide ${o+1}`},o))})]})]})})}},96182:(e,o,t)=>{t.r(o),t.d(o,{Dialog:()=>n,DialogClose:()=>l,DialogContent:()=>d,DialogDescription:()=>u,DialogFooter:()=>m,DialogHeader:()=>c,DialogOverlay:()=>a,DialogPortal:()=>s,DialogTitle:()=>p,DialogTrigger:()=>i});var r=t(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Dialog() from the server but Dialog is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx","Dialog"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogPortal() from the server but DialogPortal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx","DialogPortal"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogOverlay() from the server but DialogOverlay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx","DialogOverlay"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogTrigger() from the server but DialogTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx","DialogTrigger"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogClose() from the server but DialogClose is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx","DialogClose"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogContent() from the server but DialogContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx","DialogContent"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogHeader() from the server but DialogHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx","DialogHeader"),m=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogFooter() from the server but DialogFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx","DialogFooter"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogTitle() from the server but DialogTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx","DialogTitle"),u=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogDescription() from the server but DialogDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-roo-code\\src\\components\\ui\\modal.tsx","DialogDescription")}};