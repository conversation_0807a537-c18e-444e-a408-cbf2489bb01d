(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[128],{1129:(e,r,s)=>{"use strict";s.d(r,{NewRun:()=>E});var n=s(7093),t=s(4545),l=s(8923),a=s(9860),i=s(8590),o=s(4060),c=s(7296),d=s(5049),u=s.n(d),x=s(2247),m=s(8365),h=s(1040),f=s(1005),j=s(5745),p=s(8977),g=s(8936),v=s(7676),y=s(8511),N=s(3401);let b=(0,N.createServerReference)("4091b401de92e6c0f5dbb6ec6e2898a9f5ec057637",N.callServer,void 0,N.findSourceMapURL,"createRun"),C=(0,N.createServerReference)("7f4dfccfb1ce82bde7fe6bae316a3b65cfa8f3c634",N.callServer,void 0,N.findSourceMapURL,"getExercises"),S=a.z.object({model:a.z.string().min(1,{message:"Model is required."}),description:a.z.string().optional(),suite:a.z.enum(["full","partial"]),exercises:a.z.array(a.z.string()).optional(),settings:y.us.optional(),concurrency:a.z.number().int().min(1).max(25),systemPrompt:a.z.string().optional()}).refine(e=>"full"===e.suite||(e.exercises||[]).length>0,{message:"Exercises are required when running a partial suite.",path:["exercises"]});var z=s(8322);let w=a.z.object({id:a.z.string(),name:a.z.string()}),R=async()=>{let e=await fetch("https://openrouter.ai/api/v1/models");if(!e.ok)return[];let r=a.z.object({data:a.z.array(w)}).safeParse(await e.json());return r.success?r.data.data.sort((e,r)=>e.name.localeCompare(r.name)):(console.error(r.error),[])},k=()=>(0,i.I)({queryKey:["getOpenRouterModels"],queryFn:R});var A=s(4548);let O=[...y.jK,...y.LT];function I(e){let{customSettings:{experiments:r,...s},defaultSettings:{experiments:t,...l},className:a,...i}=e,o={...l,...t},c={...s,...r};return(0,n.jsxs)("div",{className:(0,z.cn)("grid grid-cols-3 gap-2 text-sm p-2",a),...i,children:[(0,n.jsx)("div",{className:"font-medium text-muted-foreground",children:"Setting"}),(0,n.jsx)("div",{className:"font-medium text-muted-foreground",children:"Default"}),(0,n.jsx)("div",{className:"font-medium text-muted-foreground",children:"Custom"}),O.map(e=>{let r=o[e],s=c[e];return JSON.stringify(r)===JSON.stringify(s)?null:(0,n.jsx)(M,{name:e,defaultValue:JSON.stringify(r,null,2),customValue:JSON.stringify(s,null,2)},e)})]})}function M(e){let{name:r,defaultValue:s,customValue:l,...a}=e;return(0,n.jsxs)(t.Fragment,{...a,children:[(0,n.jsx)("div",{className:"overflow-hidden font-mono",title:r,children:r}),(0,n.jsx)("pre",{className:"overflow-hidden inline text-rose-500 line-through",title:s,children:s}),(0,n.jsx)("pre",{className:"overflow-hidden inline text-teal-500",title:l,children:l})]})}function E(){let e=(0,l.useRouter)(),[r,s]=(0,t.useState)("openrouter"),[d,N]=(0,t.useState)(""),[w,R]=(0,t.useState)(!1),O=(0,t.useRef)(new Map),M=(0,t.useRef)(""),E=k(),V=(0,i.I)({queryKey:["getExercises"],queryFn:()=>C()}),F=(0,o.mN)({resolver:(0,c.u)(S),defaultValues:{model:"anthropic/claude-sonnet-4",description:"",suite:"full",exercises:[],settings:void 0,concurrency:1}}),{setValue:J,clearErrors:L,watch:$,formState:{isSubmitting:_}}=F,[q,B,P]=$(["model","suite","settings","concurrency"]),[K,U]=(0,t.useState)(!1),[D,T]=(0,t.useState)(""),X=(0,t.useRef)(null),G=(0,t.useCallback)(async s=>{try{"openrouter"===r&&(s.settings={...s.settings||{},openRouterModelId:q});let{id:n}=await b({...s,systemPrompt:D});e.push("/runs/".concat(n))}catch(e){x.oR.error(e instanceof Error?e.message:"An unknown error occurred.")}},[r,q,e,D]),Q=(0,t.useCallback)((e,r)=>{var s;if(M.current!==r)for(let{obj:{id:e},score:s}of(M.current=r,O.current.clear(),u().go(r,E.data||[],{key:"name"})))O.current.set(e,s);return null!==(s=O.current.get(e))&&void 0!==s?s:0},[E.data]),W=(0,t.useCallback)(e=>{J("model",e),R(!1)},[J]),Y=(0,t.useCallback)(async e=>{var r,n,t;let l=null===(r=e.target.files)||void 0===r?void 0:r[0];if(l){L("settings");try{let{providerProfiles:r,globalSettings:i}=a.z.object({providerProfiles:a.z.object({currentApiConfigName:a.z.string(),apiConfigs:a.z.record(a.z.string(),y.AQ)}),globalSettings:y.YZ}).parse(JSON.parse(await l.text())),o=null!==(n=r.apiConfigs[r.currentApiConfigName])&&void 0!==n?n:{};J("model",null!==(t=(0,y.Xx)(o))&&void 0!==t?t:""),J("settings",{...y.Ur,...o,...i}),s("settings"),e.target.value=""}catch(e){console.error(e),x.oR.error(e instanceof Error?e.message:"An unknown error occurred.")}}},[L,J]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.Op,{...F,children:(0,n.jsxs)("form",{onSubmit:F.handleSubmit(G),className:"flex flex-col justify-center divide-y divide-primary *:py-5",children:[(0,n.jsxs)("div",{className:"flex flex-row justify-between gap-4",children:["openrouter"===r&&(0,n.jsx)(A.zB,{control:F.control,name:"model",render:()=>{var e,r,s;return(0,n.jsxs)(A.eI,{className:"flex-1",children:[(0,n.jsxs)(A.AM,{open:w,onOpenChange:R,children:[(0,n.jsx)(A.Wv,{asChild:!0,children:(0,n.jsxs)(A.$n,{variant:"input",role:"combobox","aria-expanded":w,className:"flex items-center justify-between",children:[(0,n.jsx)("div",{children:(null===(r=E.data)||void 0===r?void 0:null===(e=r.find(e=>{let{id:r}=e;return r===q}))||void 0===e?void 0:e.name)||q||"Select OpenRouter Model"}),(0,n.jsx)(m.A,{className:"opacity-50"})]})}),(0,n.jsx)(A.hl,{className:"p-0 w-[var(--radix-popover-trigger-width)]",children:(0,n.jsxs)(A.uB,{filter:Q,children:[(0,n.jsx)(A.G7,{placeholder:"Search",value:d,onValueChange:N,className:"h-9"}),(0,n.jsxs)(A.oI,{children:[(0,n.jsx)(A.xL,{children:"No model found."}),(0,n.jsx)(A.L$,{children:null===(s=E.data)||void 0===s?void 0:s.map(e=>{let{id:r,name:s}=e;return(0,n.jsxs)(A.h_,{value:r,onSelect:W,children:[s,(0,n.jsx)(h.A,{className:(0,z.cn)("ml-auto text-accent group-data-[selected=true]:text-accent-foreground size-4",r===q?"opacity-100":"opacity-0")})]},r)})})]})]})})]}),(0,n.jsx)(A.C5,{})]})}}),(0,n.jsxs)(A.eI,{className:"flex-1",children:[(0,n.jsxs)(A.$n,{type:"button",variant:"secondary",onClick:()=>{var e;return null===(e=document.getElementById("json-upload"))||void 0===e?void 0:e.click()},children:[(0,n.jsx)(f.A,{}),"Import Settings"]}),(0,n.jsx)("input",{id:"json-upload",type:"file",accept:"application/json",className:"hidden",onChange:Y}),P&&(0,n.jsx)(A.FK,{className:"max-h-64 border rounded-sm",children:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"flex items-center gap-1 p-2 border-b",children:[(0,n.jsx)(j.A,{className:"size-4 text-ring"}),(0,n.jsx)("div",{className:"text-sm",children:"Imported valid Roo Code settings. Showing differences from default settings."})]}),(0,n.jsx)(I,{defaultSettings:y.Ur,customSettings:P})]})}),(0,n.jsx)(A.C5,{})]}),(0,n.jsxs)(A.$n,{type:"button",variant:"secondary",onClick:()=>U(!0),children:[(0,n.jsx)(p.A,{}),"Override System Prompt"]}),(0,n.jsx)(A.lG,{open:K,onOpenChange:U,children:(0,n.jsxs)(A.Cf,{children:[(0,n.jsx)(A.L3,{children:"Override System Prompt"}),(0,n.jsx)(A.TM,{ref:X,value:D,onChange:e=>T(e.target.value)}),(0,n.jsx)(A.Es,{children:(0,n.jsx)(A.$n,{onClick:()=>U(!1),children:"Done"})})]})})]}),(0,n.jsx)(A.zB,{control:F.control,name:"suite",render:()=>{var e;return(0,n.jsxs)(A.eI,{children:[(0,n.jsx)(A.lR,{children:"Exercises"}),(0,n.jsx)(A.tU,{defaultValue:"full",onValueChange:e=>J("suite",e),children:(0,n.jsxs)(A.j7,{children:[(0,n.jsx)(A.Xi,{value:"full",children:"All"}),(0,n.jsx)(A.Xi,{value:"partial",children:"Some"})]})}),"partial"===B&&(0,n.jsx)(A.KF,{options:(null===(e=V.data)||void 0===e?void 0:e.map(e=>({value:e,label:e})))||[],onValueChange:e=>J("exercises",e),placeholder:"Select",variant:"inverted",maxCount:4}),(0,n.jsx)(A.C5,{})]})}}),(0,n.jsx)(A.zB,{control:F.control,name:"concurrency",render:e=>{let{field:r}=e;return(0,n.jsxs)(A.eI,{children:[(0,n.jsx)(A.lR,{children:"Concurrency"}),(0,n.jsx)(A.MJ,{children:(0,n.jsxs)("div",{className:"flex flex-row items-center gap-2",children:[(0,n.jsx)(A.Ap,{defaultValue:[r.value],min:1,max:25,step:1,onValueChange:e=>r.onChange(e[0])}),(0,n.jsx)("div",{children:r.value})]})}),(0,n.jsx)(A.C5,{})]})}}),(0,n.jsx)(A.zB,{control:F.control,name:"description",render:e=>{let{field:r}=e;return(0,n.jsxs)(A.eI,{children:[(0,n.jsx)(A.lR,{children:"Description / Notes"}),(0,n.jsx)(A.MJ,{children:(0,n.jsx)(A.TM,{placeholder:"Optional",...r})}),(0,n.jsx)(A.C5,{})]})}}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsxs)(A.$n,{size:"lg",type:"submit",disabled:_,children:[(0,n.jsx)(g.A,{className:"size-4"}),"Launch"]})})]})}),(0,n.jsx)(A.$n,{variant:"default",className:"absolute top-4 right-12 size-12 rounded-full",onClick:()=>e.push("/"),children:(0,n.jsx)(v.A,{className:"size-6"})})]})}},6010:(e,r,s)=>{Promise.resolve().then(s.bind(s,1129))}},e=>{var r=r=>e(e.s=r);e.O(0,[409,221,581,33,777,370,335,550,358],()=>r(6010)),_N_E=e.O()}]);